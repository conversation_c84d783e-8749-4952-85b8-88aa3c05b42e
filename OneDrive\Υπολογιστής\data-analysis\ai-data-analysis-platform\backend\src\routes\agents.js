const express = require('express');
const router = express.Router();
const ExecutorAgent = require('../agents/ExecutorAgent');
const ExpresserAgent = require('../agents/ExpresserAgent');
const ReviewerAgent = require('../agents/ReviewerAgent');
const DataFiningAgent = require('../agents/DataFiningAgent');
const StorytellerAgent = require('../agents/StorytellerAgent');
const DeepSeekAPI = require('../services/DeepSeekAPI');

// Create DeepSeek API instance
const deepseekAPI = new DeepSeekAPI();

// Helper function to create appropriate agent
function createAgent(type, analysisId = null) {
  switch (type) {
    case 'executor':
      return new ExecutorAgent(deepseekAPI);
    case 'expresser':
      return new ExpresserAgent(deepseekAPI);
    case 'reviewer':
      return new ReviewerAgent(deepseekAPI);
    case 'datafining':
      return new DataFiningAgent(deepseekAPI);
    case 'storyteller':
      return new StorytellerAgent(deepseekAPI);
    default:
      throw new Error(`Unknown agent type: ${type}`);
  }
}

/**
 * @route POST /api/agents/execute
 * @desc Execute an agent task
 * @access Public (for testing)
 */
router.post('/execute', async (req, res, next) => {
  try {
    const { type, data, context = {}, options = {} } = req.body;

    // Simple validation
    if (!type || !data) {
      return res.status(400).json({
        success: false,
        error: 'Type and data are required',
        validTypes: ['executor', 'expresser', 'reviewer', 'datafining', 'storyteller']
      });
    }    // Create and execute agent
    const agent = createAgent(type);

    // Execute agent with context as second parameter
    const result = await agent.execute(data, context);

    res.json({
      success: true,
      data: {
        type,
        status: 'completed',
        result,
        executedAt: new Date()
      }
    });

  } catch (error) {
    console.error('Agent execution error:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
});

/**
 * @route GET /api/agents/types
 * @desc Get available agent types
 * @access Public
 */
router.get('/types', (req, res) => {
  res.json({
    success: true,
    data: {
      types: [
        {
          type: 'executor',
          description: 'Executes complex analysis tasks'
        },
        {
          type: 'expresser',
          description: 'Generates insights and expressions from data'
        },
        {
          type: 'reviewer',
          description: 'Reviews and validates analysis results'
        },
        {
          type: 'datafining',
          description: 'Performs data mining and pattern discovery'
        },
        {
          type: 'storyteller',
          description: 'Creates narrative reports from analysis'
        }
      ]
    }
  });
});

/**
 * @route GET /api/agents/health
 * @desc Health check for agents system
 * @access Public
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'operational',
      timestamp: new Date(),
      version: '1.0.0'
    }
  });
});

module.exports = router;
