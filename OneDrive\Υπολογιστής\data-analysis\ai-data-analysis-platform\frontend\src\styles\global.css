/* Global styles for AI Data Analysis Platform */

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f7fafc;
  color: #2d3748;
  line-height: 1.6;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Custom utility classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .7;
  }
}

.bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

/* Loading spinner */
.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3182ce;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Custom card styles */
.custom-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.custom-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px);
}

.custom-card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e2e8f0;
}

.custom-card-body {
  padding: 24px;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.pending {
  background-color: #fef5e7;
  color: #d69e2e;
}

.status-badge.running {
  background-color: #ebf8ff;
  color: #3182ce;
}

.status-badge.completed {
  background-color: #f0fff4;
  color: #38a169;
}

.status-badge.failed {
  background-color: #fed7d7;
  color: #e53e3e;
}

.status-badge.cancelled {
  background-color: #edf2f7;
  color: #718096;
}

/* Progress bars */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #3182ce 0%, #63b3ed 100%);
  transition: width 0.3s ease;
  border-radius: 4px;
}

/* File upload styles */
.file-drop-zone {
  border: 2px dashed #cbd5e0;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  transition: all 0.2s ease;
  background-color: #f7fafc;
}

.file-drop-zone.active {
  border-color: #3182ce;
  background-color: #ebf8ff;
}

.file-drop-zone:hover {
  border-color: #4299e1;
  background-color: #ebf8ff;
}

/* Data visualization styles */
.chart-container {
  position: relative;
  width: 100%;
  height: 400px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-tooltip {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
}

/* Agent dashboard styles */
.agent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.agent-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  border-left: 4px solid #3182ce;
}

.agent-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.agent-card.executor {
  border-left-color: #3182ce;
}

.agent-card.expresser {
  border-left-color: #38a169;
}

.agent-card.reviewer {
  border-left-color: #d69e2e;
}

.agent-card.datafining {
  border-left-color: #805ad5;
}

.agent-card.storyteller {
  border-left-color: #e53e3e;
}

/* Analysis results styles */
.analysis-timeline {
  position: relative;
  padding-left: 30px;
}

.analysis-timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e2e8f0;
}

.analysis-timeline-item {
  position: relative;
  margin-bottom: 30px;
}

.analysis-timeline-item::before {
  content: '';
  position: absolute;
  left: -23px;
  top: 8px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #3182ce;
  border: 3px solid white;
  box-shadow: 0 0 0 2px #3182ce;
}

.analysis-timeline-item.completed::before {
  background: #38a169;
  box-shadow: 0 0 0 2px #38a169;
}

.analysis-timeline-item.failed::before {
  background: #e53e3e;
  box-shadow: 0 0 0 2px #e53e3e;
}

/* Chat interface styles */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 500px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chat-message {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
}

.chat-message.user {
  align-self: flex-end;
  background: #3182ce;
  color: white;
}

.chat-message.assistant {
  align-self: flex-start;
  background: #f7fafc;
  color: #2d3748;
  border: 1px solid #e2e8f0;
}

.chat-input-container {
  padding: 20px;
  border-top: 1px solid #e2e8f0;
  background: #f7fafc;
}

/* Table styles */
.data-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-table th {
  background: #f7fafc;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #4a5568;
  border-bottom: 1px solid #e2e8f0;
}

.data-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #e2e8f0;
}

.data-table tr:hover {
  background: #f7fafc;
}

/* Form styles */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #2d3748;
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.form-input.error {
  border-color: #e53e3e;
}

.form-error {
  color: #e53e3e;
  font-size: 12px;
  margin-top: 4px;
}

.form-help {
  color: #718096;
  font-size: 12px;
  margin-top: 4px;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3182ce;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2c5aa0;
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover:not(:disabled) {
  background: #cbd5e0;
}

.btn-success {
  background: #38a169;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #2f855a;
}

.btn-danger {
  background: #e53e3e;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c53030;
}

.btn-outline {
  background: transparent;
  border: 1px solid #e2e8f0;
  color: #4a5568;
}

.btn-outline:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-lg {
  padding: 14px 28px;
  font-size: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .agent-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    height: 300px;
    padding: 10px;
  }
  
  .chat-message {
    max-width: 90%;
  }
  
  .data-table {
    font-size: 12px;
  }
  
  .data-table th,
  .data-table td {
    padding: 8px 12px;
  }
}

@media (max-width: 480px) {
  .custom-card-body {
    padding: 16px;
  }
  
  .custom-card-header {
    padding: 16px 16px 12px;
  }
  
  .chat-container {
    height: 400px;
  }
  
  .btn {
    padding: 8px 16px;
    font-size: 12px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1a202c;
    color: #e2e8f0;
  }
  
  .custom-card {
    background: #2d3748;
    border: 1px solid #4a5568;
  }
  
  .custom-card-header {
    border-bottom-color: #4a5568;
  }
  
  .file-drop-zone {
    background-color: #2d3748;
    border-color: #4a5568;
  }
  
  .file-drop-zone.active,
  .file-drop-zone:hover {
    background-color: #2c5aa0;
    border-color: #3182ce;
  }
  
  .data-table {
    background: #2d3748;
  }
  
  .data-table th {
    background: #4a5568;
    color: #e2e8f0;
    border-bottom-color: #718096;
  }
  
  .data-table td {
    border-bottom-color: #4a5568;
  }
  
  .data-table tr:hover {
    background: #4a5568;
  }
  
  .form-input {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .form-input:focus {
    border-color: #3182ce;
  }
  
  .chat-container {
    background: #2d3748;
  }
  
  .chat-message.assistant {
    background: #4a5568;
    color: #e2e8f0;
    border-color: #718096;
  }
  
  .chat-input-container {
    background: #4a5568;
    border-top-color: #718096;
  }
}

/* Print styles */
@media print {
  .btn,
  .chat-input-container,
  .file-drop-zone {
    display: none !important;
  }
  
  .custom-card {
    box-shadow: none;
    border: 1px solid #e2e8f0;
  }
  
  .chart-container {
    break-inside: avoid;
  }
  
  body {
    background: white;
    color: black;
  }
}
