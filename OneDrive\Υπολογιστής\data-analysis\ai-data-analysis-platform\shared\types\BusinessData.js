/**
 * Business data-related type definitions and interfaces
 */

// Data types
export const DATA_TYPES = {
  NUMERICAL: 'numerical',
  CATEGORICAL: 'categorical',
  TEMPORAL: 'temporal',
  TEXTUAL: 'textual',
  BOOLEAN: 'boolean',
  GEOSPATIAL: 'geospatial'
};

// File types
export const FILE_TYPES = {
  CSV: 'text/csv',
  EXCEL: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  EXCEL_LEGACY: 'application/vnd.ms-excel',
  JSON: 'application/json',
  TEXT: 'text/plain',
  PDF: 'application/pdf'
};

// Data categories
export const DATA_CATEGORIES = {
  FINANCIAL: 'financial_data',
  SALES: 'sales_data',
  CUSTOMER: 'customer_data',
  OPERATIONAL: 'operational_data',
  MARKETING: 'marketing_data',
  HR: 'hr_data',
  INVENTORY: 'inventory_data',
  BUSINESS: 'business_data',
  OTHER: 'other'
};

// Data processing status
export const PROCESSING_STATUS = {
  UPLOADED: 'uploaded',
  VALIDATING: 'validating',
  PROCESSING: 'processing',
  READY: 'ready',
  FAILED: 'failed',
  ARCHIVED: 'archived'
};

// Business data file
export class BusinessDataFile {
  constructor({
    fileId,
    originalName,
    fileName,
    size,
    type,
    category = DATA_CATEGORIES.BUSINESS,
    description = '',
    tags = [],
    metadata = {},
    status = PROCESSING_STATUS.UPLOADED,
    uploadedBy,
    uploadedAt = new Date(),
    processedAt = null,
    validationResults = null,
    schema = null
  } = {}) {
    this.fileId = fileId;
    this.originalName = originalName;
    this.fileName = fileName;
    this.size = size;
    this.type = type;
    this.category = category;
    this.description = description;
    this.tags = tags;
    this.metadata = metadata;
    this.status = status;
    this.uploadedBy = uploadedBy;
    this.uploadedAt = uploadedAt;
    this.processedAt = processedAt;
    this.validationResults = validationResults;
    this.schema = schema;
    this.createdAt = new Date();
  }

  // Get file extension
  getExtension() {
    const extensionMap = {
      [FILE_TYPES.CSV]: 'csv',
      [FILE_TYPES.EXCEL]: 'xlsx',
      [FILE_TYPES.EXCEL_LEGACY]: 'xls',
      [FILE_TYPES.JSON]: 'json',
      [FILE_TYPES.TEXT]: 'txt',
      [FILE_TYPES.PDF]: 'pdf'
    };
    return extensionMap[this.type] || 'unknown';
  }

  // Get formatted file size
  getFormattedSize() {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (this.size === 0) return '0 B';
    
    const i = Math.floor(Math.log(this.size) / Math.log(1024));
    return Math.round(this.size / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  // Check if file is ready for analysis
  isReady() {
    return this.status === PROCESSING_STATUS.READY;
  }

  // Check if file has validation errors
  hasValidationErrors() {
    return this.validationResults && this.validationResults.errors.length > 0;
  }

  // Get category display name
  getCategoryDisplayName() {
    const categoryMap = {
      [DATA_CATEGORIES.FINANCIAL]: 'Financial Data',
      [DATA_CATEGORIES.SALES]: 'Sales Data',
      [DATA_CATEGORIES.CUSTOMER]: 'Customer Data',
      [DATA_CATEGORIES.OPERATIONAL]: 'Operational Data',
      [DATA_CATEGORIES.MARKETING]: 'Marketing Data',
      [DATA_CATEGORIES.HR]: 'HR Data',
      [DATA_CATEGORIES.INVENTORY]: 'Inventory Data',
      [DATA_CATEGORIES.BUSINESS]: 'Business Data',
      [DATA_CATEGORIES.OTHER]: 'Other'
    };
    return categoryMap[this.category] || this.category;
  }
}

// Data column definition
export class DataColumn {
  constructor({
    name,
    type,
    nullable = true,
    unique = false,
    primaryKey = false,
    description = '',
    format = null,
    constraints = {},
    statistics = null,
    samples = []
  } = {}) {
    this.name = name;
    this.type = type;
    this.nullable = nullable;
    this.unique = unique;
    this.primaryKey = primaryKey;
    this.description = description;
    this.format = format;
    this.constraints = constraints;
    this.statistics = statistics;
    this.samples = samples;
  }

  // Check if column is numeric
  isNumeric() {
    return this.type === DATA_TYPES.NUMERICAL;
  }

  // Check if column is categorical
  isCategorical() {
    return this.type === DATA_TYPES.CATEGORICAL;
  }

  // Check if column is temporal
  isTemporal() {
    return this.type === DATA_TYPES.TEMPORAL;
  }

  // Get type display name
  getTypeDisplayName() {
    const typeMap = {
      [DATA_TYPES.NUMERICAL]: 'Number',
      [DATA_TYPES.CATEGORICAL]: 'Category',
      [DATA_TYPES.TEMPORAL]: 'Date/Time',
      [DATA_TYPES.TEXTUAL]: 'Text',
      [DATA_TYPES.BOOLEAN]: 'Boolean',
      [DATA_TYPES.GEOSPATIAL]: 'Geographic'
    };
    return typeMap[this.type] || this.type;
  }
}

// Data schema
export class DataSchema {
  constructor({
    columns = [],
    rowCount = 0,
    metadata = {},
    validationRules = [],
    relationships = []
  } = {}) {
    this.columns = columns.map(col => col instanceof DataColumn ? col : new DataColumn(col));
    this.rowCount = rowCount;
    this.metadata = metadata;
    this.validationRules = validationRules;
    this.relationships = relationships;
    this.createdAt = new Date();
  }

  // Get column by name
  getColumn(name) {
    return this.columns.find(col => col.name === name);
  }

  // Get columns by type
  getColumnsByType(type) {
    return this.columns.filter(col => col.type === type);
  }

  // Get numeric columns
  getNumericColumns() {
    return this.getColumnsByType(DATA_TYPES.NUMERICAL);
  }

  // Get categorical columns
  getCategoricalColumns() {
    return this.getColumnsByType(DATA_TYPES.CATEGORICAL);
  }

  // Get temporal columns
  getTemporalColumns() {
    return this.getColumnsByType(DATA_TYPES.TEMPORAL);
  }

  // Get primary key columns
  getPrimaryKeyColumns() {
    return this.columns.filter(col => col.primaryKey);
  }

  // Validate schema
  validate() {
    const errors = [];
    
    if (this.columns.length === 0) {
      errors.push('Schema must have at least one column');
    }

    const columnNames = this.columns.map(col => col.name);
    const duplicateNames = columnNames.filter((name, index) => 
      columnNames.indexOf(name) !== index
    );
    
    if (duplicateNames.length > 0) {
      errors.push(`Duplicate column names: ${duplicateNames.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Data validation result
export class DataValidationResult {
  constructor({
    isValid = true,
    errors = [],
    warnings = [],
    statistics = {},
    qualityScore = 0,
    recommendations = []
  } = {}) {
    this.isValid = isValid;
    this.errors = errors;
    this.warnings = warnings;
    this.statistics = statistics;
    this.qualityScore = qualityScore;
    this.recommendations = recommendations;
    this.validatedAt = new Date();
  }

  // Add error
  addError(error) {
    this.errors.push({
      message: error.message || error,
      column: error.column || null,
      row: error.row || null,
      severity: 'error',
      timestamp: new Date()
    });
    this.isValid = false;
  }

  // Add warning
  addWarning(warning) {
    this.warnings.push({
      message: warning.message || warning,
      column: warning.column || null,
      row: warning.row || null,
      severity: 'warning',
      timestamp: new Date()
    });
  }

  // Get total issues
  getTotalIssues() {
    return this.errors.length + this.warnings.length;
  }

  // Get quality level
  getQualityLevel() {
    if (this.qualityScore >= 0.9) return 'Excellent';
    if (this.qualityScore >= 0.8) return 'Good';
    if (this.qualityScore >= 0.6) return 'Fair';
    if (this.qualityScore >= 0.4) return 'Poor';
    return 'Very Poor';
  }
}

// Business metric
export class BusinessMetric {
  constructor({
    name,
    value,
    unit = '',
    category = 'general',
    description = '',
    formula = null,
    target = null,
    benchmark = null,
    trend = null,
    confidence = 1.0,
    metadata = {}
  } = {}) {
    this.name = name;
    this.value = value;
    this.unit = unit;
    this.category = category;
    this.description = description;
    this.formula = formula;
    this.target = target;
    this.benchmark = benchmark;
    this.trend = trend;
    this.confidence = confidence;
    this.metadata = metadata;
    this.calculatedAt = new Date();
  }

  // Get formatted value
  getFormattedValue() {
    if (this.value === null || this.value === undefined) {
      return 'N/A';
    }

    if (typeof this.value === 'number') {
      return this.value.toLocaleString() + (this.unit ? ` ${this.unit}` : '');
    }

    return this.value.toString();
  }

  // Check if target is met
  isTargetMet() {
    if (this.target === null || this.value === null) {
      return null;
    }
    return this.value >= this.target;
  }

  // Get performance vs benchmark
  getBenchmarkPerformance() {
    if (this.benchmark === null || this.value === null) {
      return null;
    }
    return ((this.value - this.benchmark) / this.benchmark) * 100;
  }
}

// Data preprocessing options
export class PreprocessingOptions {
  constructor({
    cleanData = true,
    handleMissingValues = 'auto',
    removeOutliers = false,
    normalizeData = false,
    encoding = 'auto',
    dateFormat = 'auto',
    customTransformations = []
  } = {}) {
    this.cleanData = cleanData;
    this.handleMissingValues = handleMissingValues; // 'auto', 'remove', 'fill', 'interpolate'
    this.removeOutliers = removeOutliers;
    this.normalizeData = normalizeData;
    this.encoding = encoding; // 'auto', 'utf8', 'latin1'
    this.dateFormat = dateFormat;
    this.customTransformations = customTransformations;
  }

  // Validate options
  validate() {
    const errors = [];
    
    const validMissingValueHandling = ['auto', 'remove', 'fill', 'interpolate'];
    if (!validMissingValueHandling.includes(this.handleMissingValues)) {
      errors.push('Invalid missing value handling option');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Export default BusinessData class
export default class BusinessData {
  constructor({
    fileId,
    schema,
    data = [],
    metadata = {},
    preprocessingOptions = new PreprocessingOptions()
  } = {}) {
    this.fileId = fileId;
    this.schema = schema instanceof DataSchema ? schema : new DataSchema(schema);
    this.data = data;
    this.metadata = metadata;
    this.preprocessingOptions = preprocessingOptions;
    this.validationResult = null;
    this.metrics = [];
    this.createdAt = new Date();
  }

  // Get row count
  getRowCount() {
    return this.data.length;
  }

  // Get column count
  getColumnCount() {
    return this.schema.columns.length;
  }

  // Get data sample
  getSample(rows = 10) {
    return this.data.slice(0, rows);
  }

  // Get column values
  getColumnValues(columnName) {
    const columnIndex = this.schema.columns.findIndex(col => col.name === columnName);
    if (columnIndex === -1) return [];
    
    return this.data.map(row => row[columnIndex]);
  }

  // Get unique values for column
  getUniqueValues(columnName) {
    const values = this.getColumnValues(columnName);
    return [...new Set(values)];
  }

  // Add business metric
  addMetric(metric) {
    this.metrics.push(metric instanceof BusinessMetric ? metric : new BusinessMetric(metric));
  }

  // Get metrics by category
  getMetricsByCategory(category) {
    return this.metrics.filter(metric => metric.category === category);
  }

  // Calculate basic statistics
  calculateBasicStatistics() {
    const stats = {};
    
    this.schema.getNumericColumns().forEach(column => {
      const values = this.getColumnValues(column.name).filter(v => v !== null && v !== undefined);
      
      if (values.length > 0) {
        const numValues = values.map(v => parseFloat(v)).filter(v => !isNaN(v));
        
        if (numValues.length > 0) {
          stats[column.name] = {
            count: numValues.length,
            mean: numValues.reduce((a, b) => a + b, 0) / numValues.length,
            min: Math.min(...numValues),
            max: Math.max(...numValues),
            median: this.calculateMedian(numValues),
            stdDev: this.calculateStandardDeviation(numValues)
          };
        }
      }
    });

    return stats;
  }

  // Calculate median
  calculateMedian(values) {
    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    
    return sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid];
  }

  // Calculate standard deviation
  calculateStandardDeviation(values) {
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    const variance = squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
    return Math.sqrt(variance);
  }
}

// Export all constants and classes
export {
  DATA_TYPES,
  FILE_TYPES,
  DATA_CATEGORIES,
  PROCESSING_STATUS,
  BusinessDataFile,
  DataColumn,
  DataSchema,
  DataValidationResult,
  BusinessMetric,
  PreprocessingOptions
};
