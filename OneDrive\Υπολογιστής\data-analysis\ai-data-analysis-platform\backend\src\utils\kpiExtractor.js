const winston = require('winston');

class KPIExtractor {
  constructor() {
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/kpi-extractor.log' })
      ]
    });

    // Predefined KPI patterns and calculations
    this.kpiDefinitions = {
      // Financial KPIs
      revenue: {
        patterns: ['revenue', 'sales', 'income', 'turnover'],
        type: 'sum',
        category: 'financial',
        importance: 'high',
        description: 'Total revenue generated'
      },
      profit: {
        patterns: ['profit', 'net_income', 'earnings'],
        type: 'sum',
        category: 'financial',
        importance: 'high',
        description: 'Total profit earned'
      },
      cost: {
        patterns: ['cost', 'expense', 'expenditure', 'spending'],
        type: 'sum',
        category: 'financial',
        importance: 'high',
        description: 'Total costs incurred'
      },
      margin: {
        patterns: ['margin', 'profit_margin'],
        type: 'average',
        category: 'financial',
        importance: 'high',
        description: 'Average profit margin'
      },

      // Customer KPIs
      customerCount: {
        patterns: ['customers', 'clients', 'users', 'accounts'],
        type: 'count',
        category: 'customer',
        importance: 'high',
        description: 'Total number of customers'
      },
      customerAcquisition: {
        patterns: ['new_customers', 'acquisitions', 'signups'],
        type: 'sum',
        category: 'customer',
        importance: 'medium',
        description: 'New customers acquired'
      },
      customerRetention: {
        patterns: ['retention', 'churn', 'loyalty'],
        type: 'rate',
        category: 'customer',
        importance: 'high',
        description: 'Customer retention rate'
      },
      customerLifetimeValue: {
        patterns: ['clv', 'ltv', 'lifetime_value'],
        type: 'average',
        category: 'customer',
        importance: 'medium',
        description: 'Average customer lifetime value'
      },

      // Operational KPIs
      productivity: {
        patterns: ['productivity', 'efficiency', 'output'],
        type: 'average',
        category: 'operational',
        importance: 'medium',
        description: 'Average productivity metrics'
      },
      quality: {
        patterns: ['quality', 'defects', 'errors', 'satisfaction'],
        type: 'average',
        category: 'operational',
        importance: 'medium',
        description: 'Quality metrics'
      },
      capacity: {
        patterns: ['capacity', 'utilization', 'usage'],
        type: 'average',
        category: 'operational',
        importance: 'medium',
        description: 'Capacity utilization'
      },

      // Performance KPIs
      growth: {
        patterns: ['growth', 'increase', 'expansion'],
        type: 'rate',
        category: 'performance',
        importance: 'high',
        description: 'Growth rate'
      },
      conversion: {
        patterns: ['conversion', 'rate', 'success'],
        type: 'rate',
        category: 'performance',
        importance: 'medium',
        description: 'Conversion rate'
      }
    };
  }

  // Extract KPIs from data
  async extractKPIs(data, options = {}) {
    try {
      const {
        autoDetect = true,
        customKPIs = [],
        timeColumn = null,
        groupBy = null
      } = options;

      this.logger.info('Starting KPI extraction:', {
        records: data.length,
        customKPIs: customKPIs.length,
        autoDetect
      });

      let kpis = {};

      // Auto-detect KPIs based on column names
      if (autoDetect) {
        kpis = { ...kpis, ...this.autoDetectKPIs(data) };
      }

      // Process custom KPIs
      if (customKPIs.length > 0) {
        const customResults = this.processCustomKPIs(data, customKPIs);
        kpis = { ...kpis, ...customResults };
      }

      // Calculate time-based KPIs if time column is provided
      if (timeColumn) {
        const timeBased = this.calculateTimeBasedKPIs(data, timeColumn, kpis);
        kpis = { ...kpis, ...timeBased };
      }

      // Group KPIs if groupBy is specified
      if (groupBy) {
        kpis.grouped = this.groupKPIs(data, groupBy, kpis);
      }

      // Add metadata and insights
      const result = {
        kpis,
        metadata: this.generateKPIMetadata(kpis, data),
        insights: this.generateKPIInsights(kpis),
        recommendations: this.generateKPIRecommendations(kpis)
      };

      this.logger.info('KPI extraction completed:', {
        totalKPIs: Object.keys(kpis).length,
        categories: this.getCategoryCounts(kpis)
      });

      return result;
    } catch (error) {
      this.logger.error('KPI extraction failed:', error);
      throw error;
    }
  }

  // Auto-detect KPIs based on column names and data patterns
  autoDetectKPIs(data) {
    if (!data.length) return {};

    const columns = Object.keys(data[0]);
    const detectedKPIs = {};

    // Match columns to KPI patterns
    Object.entries(this.kpiDefinitions).forEach(([kpiName, definition]) => {
      const matchingColumns = columns.filter(column => {
        const lowerColumn = column.toLowerCase();
        return definition.patterns.some(pattern => 
          lowerColumn.includes(pattern.toLowerCase())
        );
      });

      matchingColumns.forEach(column => {
        const kpi = this.calculateKPI(data, column, definition);
        if (kpi !== null) {
          detectedKPIs[`${kpiName}_${column}`] = {
            ...kpi,
            source: column,
            autoDetected: true,
            definition
          };
        }
      });
    });

    // Detect additional numeric KPIs
    const numericColumns = this.getNumericColumns(data);
    numericColumns.forEach(column => {
      if (!this.isColumnUsed(column, detectedKPIs)) {
        const kpi = this.calculateBasicKPI(data, column);
        detectedKPIs[`metric_${column}`] = {
          ...kpi,
          source: column,
          autoDetected: true,
          category: 'general'
        };
      }
    });

    return detectedKPIs;
  }

  // Calculate KPI based on definition
  calculateKPI(data, column, definition) {
    const values = this.getColumnValues(data, column);
    if (values.length === 0) return null;

    let result;
    
    switch (definition.type) {
      case 'sum':
        result = this.calculateSum(values);
        break;
      case 'average':
        result = this.calculateAverage(values);
        break;
      case 'count':
        result = this.calculateCount(values);
        break;
      case 'rate':
        result = this.calculateRate(values);
        break;
      case 'median':
        result = this.calculateMedian(values);
        break;
      default:
        result = this.calculateAverage(values);
    }

    return {
      value: result,
      type: definition.type,
      category: definition.category,
      importance: definition.importance,
      description: definition.description,
      unit: this.inferUnit(column, values),
      trend: this.calculateTrend(values),
      variance: this.calculateVariance(values)
    };
  }

  // Calculate basic KPI for numeric columns
  calculateBasicKPI(data, column) {
    const values = this.getColumnValues(data, column);
    
    return {
      value: this.calculateAverage(values),
      sum: this.calculateSum(values),
      min: Math.min(...values),
      max: Math.max(...values),
      count: values.length,
      type: 'average',
      category: 'general',
      importance: 'low',
      description: `Average ${column}`,
      unit: this.inferUnit(column, values),
      trend: this.calculateTrend(values),
      variance: this.calculateVariance(values)
    };
  }

  // Process custom KPI definitions
  processCustomKPIs(data, customKPIs) {
    const results = {};

    customKPIs.forEach(kpi => {
      try {
        const result = this.calculateCustomKPI(data, kpi);
        results[kpi.name] = {
          ...result,
          custom: true,
          definition: kpi
        };
      } catch (error) {
        this.logger.warn('Custom KPI calculation failed:', {
          kpi: kpi.name,
          error: error.message
        });
      }
    });

    return results;
  }

  // Calculate custom KPI
  calculateCustomKPI(data, kpiDefinition) {
    const { name, formula, columns, type = 'calculated' } = kpiDefinition;

    if (formula) {
      return this.calculateFormulaKPI(data, formula);
    }

    if (columns && columns.length > 0) {
      return this.calculateMultiColumnKPI(data, columns, type);
    }

    throw new Error(`Invalid custom KPI definition for ${name}`);
  }

  // Calculate KPI using formula
  calculateFormulaKPI(data, formula) {
    // Simple formula evaluation (extend as needed)
    // Example: "revenue - cost" or "profit / revenue * 100"
    
    const values = data.map(row => {
      try {
        // Replace column names with values in formula
        let expr = formula;
        Object.keys(row).forEach(column => {
          const value = parseFloat(row[column]) || 0;
          expr = expr.replace(new RegExp(`\\b${column}\\b`, 'g'), value);
        });

        // Evaluate the expression (basic evaluation)
        return this.evaluateExpression(expr);
      } catch (error) {
        return null;
      }
    }).filter(val => val !== null);

    return {
      value: this.calculateAverage(values),
      formula,
      calculatedValues: values,
      count: values.length
    };
  }

  // Calculate multi-column KPI
  calculateMultiColumnKPI(data, columns, type) {
    const columnValues = columns.map(column => 
      this.getColumnValues(data, column)
    );

    let result;
    
    switch (type) {
      case 'ratio':
        if (columnValues.length >= 2) {
          result = this.calculateRatio(columnValues[0], columnValues[1]);
        }
        break;
      case 'correlation':
        if (columnValues.length >= 2) {
          result = this.calculateCorrelation(columnValues[0], columnValues[1]);
        }
        break;
      case 'weighted_average':
        if (columnValues.length >= 2) {
          result = this.calculateWeightedAverage(columnValues[0], columnValues[1]);
        }
        break;
      default:
        // Sum all columns
        result = columnValues.reduce((sum, values) => 
          sum + this.calculateSum(values), 0
        );
    }

    return {
      value: result,
      type,
      columns,
      columnValues: columnValues.map(values => ({
        sum: this.calculateSum(values),
        average: this.calculateAverage(values),
        count: values.length
      }))
    };
  }

  // Calculate time-based KPIs
  calculateTimeBasedKPIs(data, timeColumn, existingKPIs) {
    const timeBasedKPIs = {};

    // Group data by time periods
    const timeGroups = this.groupByTimePeriod(data, timeColumn);

    Object.entries(existingKPIs).forEach(([kpiName, kpi]) => {
      if (kpi.source) {
        // Calculate KPI for each time period
        const timeSeries = {};
        Object.entries(timeGroups).forEach(([period, periodData]) => {
          const periodKPI = this.calculateKPI(
            periodData, 
            kpi.source, 
            kpi.definition || { type: 'average' }
          );
          if (periodKPI) {
            timeSeries[period] = periodKPI.value;
          }
        });

        timeBasedKPIs[`${kpiName}_timeseries`] = {
          timeSeries,
          growth: this.calculateGrowthRate(Object.values(timeSeries)),
          trend: this.calculateTrend(Object.values(timeSeries)),
          seasonality: this.detectSeasonality(Object.values(timeSeries))
        };
      }
    });

    return timeBasedKPIs;
  }

  // Group KPIs by specified column
  groupKPIs(data, groupBy, kpis) {
    const groups = this.groupDataBy(data, groupBy);
    const groupedKPIs = {};

    Object.entries(groups).forEach(([groupValue, groupData]) => {
      groupedKPIs[groupValue] = {};
      
      Object.entries(kpis).forEach(([kpiName, kpi]) => {
        if (kpi.source) {
          const groupKPI = this.calculateKPI(
            groupData,
            kpi.source,
            kpi.definition || { type: 'average' }
          );
          if (groupKPI) {
            groupedKPIs[groupValue][kpiName] = groupKPI;
          }
        }
      });
    });

    return groupedKPIs;
  }

  // Helper calculation methods
  calculateSum(values) {
    return values.reduce((sum, val) => sum + (parseFloat(val) || 0), 0);
  }

  calculateAverage(values) {
    const numericValues = values.filter(val => !isNaN(parseFloat(val)));
    return numericValues.length > 0 
      ? numericValues.reduce((sum, val) => sum + parseFloat(val), 0) / numericValues.length 
      : 0;
  }

  calculateCount(values) {
    return values.filter(val => val !== null && val !== undefined && val !== '').length;
  }

  calculateRate(values) {
    // For rate calculation, assume percentage values or calculate success rate
    const numericValues = values.filter(val => !isNaN(parseFloat(val)));
    if (numericValues.length === 0) return 0;
    
    // If values are between 0 and 1, assume they're already rates
    const avgValue = this.calculateAverage(numericValues);
    return avgValue <= 1 ? avgValue * 100 : avgValue;
  }

  calculateMedian(values) {
    const numericValues = values.filter(val => !isNaN(parseFloat(val)))
      .map(val => parseFloat(val))
      .sort((a, b) => a - b);
    
    if (numericValues.length === 0) return 0;
    
    const mid = Math.floor(numericValues.length / 2);
    return numericValues.length % 2 === 0
      ? (numericValues[mid - 1] + numericValues[mid]) / 2
      : numericValues[mid];
  }

  calculateVariance(values) {
    const numericValues = values.filter(val => !isNaN(parseFloat(val))).map(val => parseFloat(val));
    if (numericValues.length <= 1) return 0;
    
    const mean = this.calculateAverage(numericValues);
    const variance = numericValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / numericValues.length;
    return Math.sqrt(variance); // Return standard deviation
  }

  calculateTrend(values) {
    const numericValues = values.filter(val => !isNaN(parseFloat(val))).map(val => parseFloat(val));
    if (numericValues.length < 2) return 'insufficient_data';
    
    // Simple linear trend calculation
    const n = numericValues.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = numericValues.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * numericValues[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    
    if (slope > 0.1) return 'increasing';
    if (slope < -0.1) return 'decreasing';
    return 'stable';
  }

  calculateGrowthRate(values) {
    if (values.length < 2) return null;
    
    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    
    if (firstValue === 0) return null;
    
    return ((lastValue - firstValue) / firstValue) * 100;
  }

  calculateRatio(numeratorValues, denominatorValues) {
    const numeratorSum = this.calculateSum(numeratorValues);
    const denominatorSum = this.calculateSum(denominatorValues);
    
    return denominatorSum !== 0 ? numeratorSum / denominatorSum : 0;
  }

  calculateCorrelation(values1, values2) {
    if (values1.length !== values2.length || values1.length < 2) return 0;
    
    const n = values1.length;
    const sum1 = this.calculateSum(values1);
    const sum2 = this.calculateSum(values2);
    const sum1Sq = values1.reduce((sum, val) => sum + val * val, 0);
    const sum2Sq = values2.reduce((sum, val) => sum + val * val, 0);
    const pSum = values1.reduce((sum, val, i) => sum + val * values2[i], 0);
    
    const num = pSum - (sum1 * sum2 / n);
    const den = Math.sqrt((sum1Sq - sum1 * sum1 / n) * (sum2Sq - sum2 * sum2 / n));
    
    return den === 0 ? 0 : num / den;
  }

  calculateWeightedAverage(values, weights) {
    if (values.length !== weights.length) return 0;
    
    const weightedSum = values.reduce((sum, val, i) => sum + val * weights[i], 0);
    const totalWeight = this.calculateSum(weights);
    
    return totalWeight !== 0 ? weightedSum / totalWeight : 0;
  }

  // Utility methods
  getColumnValues(data, column) {
    return data.map(row => row[column])
      .filter(val => val !== null && val !== undefined && val !== '' && !isNaN(parseFloat(val)))
      .map(val => parseFloat(val));
  }

  getNumericColumns(data) {
    if (!data.length) return [];
    
    const columns = Object.keys(data[0]);
    return columns.filter(column => {
      const sample = data.slice(0, 10).map(row => row[column]);
      return sample.some(val => !isNaN(parseFloat(val)));
    });
  }

  isColumnUsed(column, kpis) {
    return Object.values(kpis).some(kpi => kpi.source === column);
  }

  inferUnit(column, values) {
    const columnLower = column.toLowerCase();
    
    if (columnLower.includes('percent') || columnLower.includes('rate')) return '%';
    if (columnLower.includes('price') || columnLower.includes('cost') || columnLower.includes('revenue')) return '$';
    if (columnLower.includes('time') || columnLower.includes('duration')) return 'time';
    if (columnLower.includes('count') || columnLower.includes('number')) return 'count';
    
    // Infer from values
    const avgValue = this.calculateAverage(values);
    if (avgValue <= 1 && avgValue >= 0) return '%';
    
    return 'units';
  }

  groupByTimePeriod(data, timeColumn) {
    const groups = {};
    
    data.forEach(row => {
      const date = new Date(row[timeColumn]);
      if (!isNaN(date.getTime())) {
        const period = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        if (!groups[period]) groups[period] = [];
        groups[period].push(row);
      }
    });
    
    return groups;
  }

  groupDataBy(data, column) {
    const groups = {};
    
    data.forEach(row => {
      const groupValue = row[column];
      if (!groups[groupValue]) groups[groupValue] = [];
      groups[groupValue].push(row);
    });
    
    return groups;
  }

  detectSeasonality(values) {
    // Simple seasonality detection
    if (values.length < 12) return 'insufficient_data';
    
    // Check for quarterly patterns
    const quarters = [];
    for (let i = 0; i < values.length - 3; i += 3) {
      quarters.push(values.slice(i, i + 3));
    }
    
    // Calculate variance within quarters vs between quarters
    // (Simplified implementation)
    return 'none'; // Placeholder
  }

  evaluateExpression(expr) {
    // Basic expression evaluation (for security, this should be more sophisticated)
    try {
      // Remove any non-numeric, non-operator characters for safety
      const sanitized = expr.replace(/[^0-9+\-*/().\s]/g, '');
      return Function(`"use strict"; return (${sanitized})`)();
    } catch (error) {
      return null;
    }
  }

  // Metadata and insights generation
  generateKPIMetadata(kpis, data) {
    return {
      totalKPIs: Object.keys(kpis).length,
      categories: this.getCategoryCounts(kpis),
      dataSource: {
        records: data.length,
        columns: data.length > 0 ? Object.keys(data[0]).length : 0
      },
      extractionTimestamp: new Date().toISOString()
    };
  }

  getCategoryCounts(kpis) {
    const counts = {};
    Object.values(kpis).forEach(kpi => {
      const category = kpi.category || 'general';
      counts[category] = (counts[category] || 0) + 1;
    });
    return counts;
  }

  generateKPIInsights(kpis) {
    const insights = [];
    
    Object.entries(kpis).forEach(([name, kpi]) => {
      if (kpi.trend === 'increasing' && kpi.importance === 'high') {
        insights.push(`${name} shows positive growth trend`);
      } else if (kpi.trend === 'decreasing' && kpi.importance === 'high') {
        insights.push(`${name} shows concerning decline`);
      }
      
      if (kpi.variance > kpi.value * 0.5) {
        insights.push(`${name} shows high volatility`);
      }
    });
    
    return insights;
  }

  generateKPIRecommendations(kpis) {
    const recommendations = [];
    
    const highImportanceKPIs = Object.entries(kpis)
      .filter(([_, kpi]) => kpi.importance === 'high');
    
    highImportanceKPIs.forEach(([name, kpi]) => {
      if (kpi.trend === 'decreasing') {
        recommendations.push({
          kpi: name,
          action: `Investigate causes of decline in ${name}`,
          priority: 'high',
          category: 'performance'
        });
      }
    });
    
    return recommendations;
  }
}

module.exports = KPIExtractor;
