/**
 * Utility functions for formatting data
 */

// Format file size in human-readable format
export const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B';

  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

// Format date in various formats
export const formatDate = (date, format = 'short') => {
  if (!date) return '';

  const d = new Date(date);
  if (isNaN(d.getTime())) return 'Invalid Date';

  const options = {
    short: {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    },
    long: {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    },
    relative: null, // Special handling below
    time: {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    },
    iso: null // Special handling below
  };

  if (format === 'relative') {
    return formatRelativeTime(d);
  }

  if (format === 'iso') {
    return d.toISOString();
  }

  return d.toLocaleDateString('en-US', options[format] || options.short);
};

// Format relative time (e.g., "2 hours ago")
export const formatRelativeTime = (date) => {
  if (!date) return '';

  const now = new Date();
  const diffMs = now - new Date(date);
  const diffSec = Math.round(diffMs / 1000);
  const diffMin = Math.round(diffSec / 60);
  const diffHour = Math.round(diffMin / 60);
  const diffDay = Math.round(diffHour / 24);
  const diffWeek = Math.round(diffDay / 7);
  const diffMonth = Math.round(diffDay / 30);
  const diffYear = Math.round(diffDay / 365);

  if (diffSec < 60) return 'Just now';
  if (diffMin < 60) return `${diffMin} minute${diffMin !== 1 ? 's' : ''} ago`;
  if (diffHour < 24) return `${diffHour} hour${diffHour !== 1 ? 's' : ''} ago`;
  if (diffDay < 7) return `${diffDay} day${diffDay !== 1 ? 's' : ''} ago`;
  if (diffWeek < 4) return `${diffWeek} week${diffWeek !== 1 ? 's' : ''} ago`;
  if (diffMonth < 12) return `${diffMonth} month${diffMonth !== 1 ? 's' : ''} ago`;
  return `${diffYear} year${diffYear !== 1 ? 's' : ''} ago`;
};

// Format duration in human-readable format
export const formatDuration = (ms) => {
  if (!ms || ms === 0) return '0 seconds';

  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}d ${hours % 24}h ${minutes % 60}m`;
  }
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  }
  if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  }
  return `${seconds}s`;
};

// Format numbers with appropriate precision and units
export const formatNumber = (value, options = {}) => {
  if (value === null || value === undefined || isNaN(value)) {
    return options.fallback || '—';
  }

  const {
    precision = 2,
    unit = '',
    prefix = '',
    suffix = '',
    compact = false,
    percentage = false,
    currency = null
  } = options;

  let formattedValue = Number(value);

  if (percentage) {
    formattedValue = formattedValue * 100;
    return `${prefix}${formattedValue.toFixed(precision)}%${suffix}`;
  }

  if (currency) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(formattedValue);
  }

  if (compact && Math.abs(formattedValue) >= 1000) {
    const units = ['', 'K', 'M', 'B', 'T'];
    let unitIndex = 0;
    
    while (Math.abs(formattedValue) >= 1000 && unitIndex < units.length - 1) {
      formattedValue /= 1000;
      unitIndex++;
    }
    
    return `${prefix}${formattedValue.toFixed(precision)}${units[unitIndex]}${unit}${suffix}`;
  }

  return `${prefix}${formattedValue.toFixed(precision)}${unit}${suffix}`;
};

// Format percentage
export const formatPercentage = (value, precision = 1) => {
  return formatNumber(value, { percentage: true, precision });
};

// Format currency
export const formatCurrency = (value, currency = 'USD', precision = 2) => {
  return formatNumber(value, { currency, precision });
};

// Format KPI values
export const formatKPI = (value, type = 'number', options = {}) => {
  switch (type) {
    case 'percentage':
      return formatPercentage(value, options.precision);
    case 'currency':
      return formatCurrency(value, options.currency, options.precision);
    case 'duration':
      return formatDuration(value);
    case 'fileSize':
      return formatFileSize(value);
    case 'compact':
      return formatNumber(value, { ...options, compact: true });
    default:
      return formatNumber(value, options);
  }
};

// Format analysis status
export const formatAnalysisStatus = (status) => {
  const statusMap = {
    pending: { label: 'Pending', color: 'yellow' },
    running: { label: 'Running', color: 'blue' },
    completed: { label: 'Completed', color: 'green' },
    failed: { label: 'Failed', color: 'red' },
    cancelled: { label: 'Cancelled', color: 'gray' }
  };

  return statusMap[status] || { label: status, color: 'gray' };
};

// Format agent type
export const formatAgentType = (type) => {
  const typeMap = {
    executor: 'Executor Agent',
    expresser: 'Expresser Agent',
    reviewer: 'Reviewer Agent',
    datafining: 'Data Fining Agent',
    storyteller: 'Storyteller Agent'
  };

  return typeMap[type] || type;
};

// Format file type
export const formatFileType = (mimeType) => {
  const typeMap = {
    'text/csv': 'CSV',
    'application/vnd.ms-excel': 'Excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',
    'application/json': 'JSON',
    'text/plain': 'Text',
    'application/pdf': 'PDF'
  };

  return typeMap[mimeType] || mimeType || 'Unknown';
};

// Truncate text with ellipsis
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  
  if (text.length <= maxLength) return text;
  
  return text.substring(0, maxLength).trim() + '...';
};

// Capitalize first letter
export const capitalize = (str) => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// Convert camelCase to Title Case
export const camelToTitle = (str) => {
  if (!str) return '';
  
  return str
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (s) => s.toUpperCase())
    .trim();
};

// Convert snake_case to Title Case
export const snakeToTitle = (str) => {
  if (!str) return '';
  
  return str
    .split('_')
    .map(word => capitalize(word))
    .join(' ');
};

// Format table data for display
export const formatTableValue = (value, column) => {
  if (value === null || value === undefined) {
    return '—';
  }

  switch (column.type) {
    case 'date':
      return formatDate(value, column.format || 'short');
    case 'number':
      return formatNumber(value, column.options || {});
    case 'percentage':
      return formatPercentage(value, column.precision);
    case 'currency':
      return formatCurrency(value, column.currency, column.precision);
    case 'fileSize':
      return formatFileSize(value);
    case 'duration':
      return formatDuration(value);
    case 'boolean':
      return value ? 'Yes' : 'No';
    case 'array':
      return Array.isArray(value) ? value.join(', ') : value;
    case 'truncate':
      return truncateText(value, column.maxLength);
    default:
      return String(value);
  }
};

// Format error messages
export const formatErrorMessage = (error) => {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  return 'An unexpected error occurred';
};

// Format validation errors
export const formatValidationErrors = (errors) => {
  if (!errors || typeof errors !== 'object') {
    return [];
  }

  return Object.entries(errors).map(([field, message]) => ({
    field: camelToTitle(field),
    message: Array.isArray(message) ? message[0] : message
  }));
};

// Format progress percentage
export const formatProgress = (current, total) => {
  if (!total || total === 0) return 0;
  return Math.round((current / total) * 100);
};

// Format chart data labels
export const formatChartLabel = (value, type = 'default') => {
  switch (type) {
    case 'percentage':
      return `${formatPercentage(value)}`;
    case 'currency':
      return formatCurrency(value);
    case 'compact':
      return formatNumber(value, { compact: true });
    case 'date':
      return formatDate(value, 'short');
    default:
      return formatNumber(value);
  }
};

export default {
  formatFileSize,
  formatDate,
  formatRelativeTime,
  formatDuration,
  formatNumber,
  formatPercentage,
  formatCurrency,
  formatKPI,
  formatAnalysisStatus,
  formatAgentType,
  formatFileType,
  truncateText,
  capitalize,
  camelToTitle,
  snakeToTitle,
  formatTableValue,
  formatErrorMessage,
  formatValidationErrors,
  formatProgress,
  formatChartLabel
};
