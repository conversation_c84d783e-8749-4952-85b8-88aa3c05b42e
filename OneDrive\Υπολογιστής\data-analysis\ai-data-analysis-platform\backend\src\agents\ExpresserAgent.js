class ExpresserAgent {
  constructor(deepseekAPI) {
    this.deepseekAPI = deepseekAPI;
    this.agentId = 'expresser';
    this.pattern = 'PEER';
    this.status = 'idle';
    this.capabilities = [
      'data_visualization',
      'chart_generation',
      'dashboard_creation',
      'visual_insights',
      'interactive_elements'
    ];
  }

  async execute(data, analysisResults = {}) {
    this.status = 'processing';
    
    try {
      // Step 1: Generate chart configurations
      const charts = await this.generateCharts(data, analysisResults);
      
      // Step 2: Create dashboard layout
      const dashboards = await this.createDashboards(charts, analysisResults);
      
      // Step 3: Generate visual insights
      const visualInsights = await this.generateVisualInsights(charts, analysisResults);
      
      // Step 4: Create interactive elements
      const interactiveElements = await this.createInteractiveElements(data, charts);
      
      // Step 5: Generate presentation materials
      const presentations = await this.generatePresentations(dashboards, visualInsights);
      
      this.status = 'completed';
      
      return {
        charts,
        dashboards,
        visualInsights,
        interactiveElements,
        presentations,
        metadata: {
          agentId: this.agentId,
          pattern: this.pattern,
          timestamp: new Date().toISOString(),
          chartCount: charts.length,
          visualComplexity: this.assessVisualComplexity(charts)
        }
      };
      
    } catch (error) {
      this.status = 'error';
      console.error('Expresser Agent Error:', error);
      throw error;
    }
  }

  async generateCharts(data, analysisResults) {
    const charts = [];
    const numericColumns = this.getNumericColumns(data);
    const timeColumns = this.getTimeColumns(data);
    
    // KPI Overview Chart
    if (analysisResults.execute?.kpiCalculations) {
      charts.push(this.createKPIOverviewChart(analysisResults.execute.kpiCalculations));
    }
    
    // Time Series Charts
    if (timeColumns.length > 0 && numericColumns.length > 0) {
      charts.push(...this.createTimeSeriesCharts(data, timeColumns, numericColumns));
    }
    
    // Statistical Distribution Charts
    charts.push(...this.createDistributionCharts(data, numericColumns));
    
    // Correlation Matrix
    if (numericColumns.length > 2) {
      charts.push(this.createCorrelationMatrix(data, numericColumns));
    }
    
    // Trend Analysis Charts
    if (analysisResults.execute?.trendAnalysis) {
      charts.push(...this.createTrendCharts(analysisResults.execute.trendAnalysis));
    }
    
    // Forecast Charts
    if (analysisResults.execute?.forecasts) {
      charts.push(...this.createForecastCharts(analysisResults.execute.forecasts));
    }
    
    return charts;
  }

  createKPIOverviewChart(kpiCalculations) {
    const kpiData = Object.entries(kpiCalculations)
      .filter(([key, value]) => value && typeof value === 'object' && value.current_value !== undefined)
      .map(([key, value]) => ({
        name: this.formatKPIName(key),
        value: value.current_value,
        growth: value.growth_rate || 0,
        trend: value.trend || 'stable'
      }));

    return {
      id: 'kpi-overview',
      type: 'bar',
      title: 'Key Performance Indicators',
      subtitle: 'Current values and trends',
      data: kpiData,
      config: {
        xAxis: { dataKey: 'name', angle: -45 },
        yAxis: { tickFormatter: this.formatNumber },
        bars: [
          { dataKey: 'value', fill: '#8884d8', name: 'Current Value' }
        ],
        tooltip: {
          formatter: (value, name, props) => [
            this.formatNumber(value),
            `Growth: ${(props.payload.growth * 100).toFixed(1)}%`
          ]
        }
      },
      insights: [
        `Tracking ${kpiData.length} key performance indicators`,
        `${kpiData.filter(d => d.growth > 0).length} metrics showing positive growth`
      ]
    };
  }

  createTimeSeriesCharts(data, timeColumns, numericColumns) {
    const charts = [];
    const timeColumn = timeColumns[0];
    
    // Multi-metric time series
    const timeSeriesData = data.map(row => {
      const dataPoint = { [timeColumn]: row[timeColumn] };
      numericColumns.forEach(col => {
        dataPoint[col] = parseFloat(row[col]) || 0;
      });
      return dataPoint;
    });

    charts.push({
      id: 'time-series-multi',
      type: 'line',
      title: 'Performance Over Time',
      subtitle: 'Multi-metric trend analysis',
      data: timeSeriesData,
      config: {
        xAxis: { dataKey: timeColumn, tickFormatter: this.formatDate },
        yAxis: { tickFormatter: this.formatNumber },
        lines: numericColumns.slice(0, 5).map((col, index) => ({
          dataKey: col,
          stroke: this.getColor(index),
          name: this.formatKPIName(col),
          strokeWidth: 2
        })),
        tooltip: { labelFormatter: this.formatDate }
      },
      insights: [
        'Track multiple metrics over time',
        'Identify seasonal patterns and trends'
      ]
    });

    // Individual metric charts for top 3 KPIs
    numericColumns.slice(0, 3).forEach((column, index) => {
      charts.push({
        id: `time-series-${column}`,
        type: 'area',
        title: `${this.formatKPIName(column)} Trend`,
        subtitle: 'Historical performance analysis',
        data: timeSeriesData,
        config: {
          xAxis: { dataKey: timeColumn, tickFormatter: this.formatDate },
          yAxis: { tickFormatter: this.formatNumber },
          areas: [
            {
              dataKey: column,
              fill: this.getColor(index),
              stroke: this.getColor(index),
              fillOpacity: 0.3
            }
          ]
        },
        insights: [`Detailed ${column} performance tracking`]
      });
    });

    return charts;
  }

  createDistributionCharts(data, numericColumns) {
    const charts = [];
    
    // Box plot for top metrics
    if (numericColumns.length > 0) {
      const boxPlotData = numericColumns.slice(0, 4).map(column => {
        const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
        const sorted = values.sort((a, b) => a - b);
        
        return {
          name: this.formatKPIName(column),
          min: Math.min(...values),
          q1: this.calculateQuartile(sorted, 0.25),
          median: this.calculateQuartile(sorted, 0.5),
          q3: this.calculateQuartile(sorted, 0.75),
          max: Math.max(...values),
          outliers: this.detectOutliers(values)
        };
      });

      charts.push({
        id: 'distribution-box',
        type: 'box',
        title: 'Data Distribution Analysis',
        subtitle: 'Statistical distribution of key metrics',
        data: boxPlotData,
        config: {
          xAxis: { dataKey: 'name' },
          yAxis: { tickFormatter: this.formatNumber }
        },
        insights: [
          'Understand data spread and outliers',
          'Identify potential data quality issues'
        ]
      });
    }

    return charts;
  }

  createCorrelationMatrix(data, numericColumns) {
    const correlationData = this.calculateCorrelationMatrix(data, numericColumns);
    
    return {
      id: 'correlation-matrix',
      type: 'heatmap',
      title: 'Correlation Analysis',
      subtitle: 'Relationships between metrics',
      data: correlationData,
      config: {
        colorScale: ['#ff4444', '#ffffff', '#44ff44'],
        range: [-1, 1]
      },
      insights: [
        'Discover hidden relationships between metrics',
        'Identify strong positive and negative correlations'
      ]
    };
  }

  createTrendCharts(trendAnalysis) {
    const charts = [];
    
    // Trend strength visualization
    const trendData = Object.entries(trendAnalysis).map(([metric, trend]) => ({
      metric: this.formatKPIName(metric),
      strength: trend.strength || 0,
      direction: trend.direction,
      volatility: trend.volatility || 0
    }));

    charts.push({
      id: 'trend-strength',
      type: 'scatter',
      title: 'Trend Analysis',
      subtitle: 'Trend strength vs volatility',
      data: trendData,
      config: {
        xAxis: { dataKey: 'strength', name: 'Trend Strength' },
        yAxis: { dataKey: 'volatility', name: 'Volatility' },
        scatter: { fill: '#8884d8' },
        tooltip: {
          formatter: (value, name, props) => [
            `${props.payload.metric}`,
            `Direction: ${props.payload.direction}`
          ]
        }
      },
      insights: [
        'High strength, low volatility indicates stable trends',
        'High volatility suggests unpredictable behavior'
      ]
    });

    return charts;
  }

  createForecastCharts(forecasts) {
    const charts = [];
    
    if (forecasts.forecasts && forecasts.forecasts.length > 0) {
      forecasts.forecasts.forEach((forecast, index) => {
        const forecastData = [
          {
            period: 'Current',
            value: forecast.current_value,
            type: 'actual'
          },
          {
            period: 'Next Month',
            value: forecast.forecast_next_month,
            type: 'forecast',
            confidence: forecast.confidence_score
          },
          {
            period: 'Next Quarter',
            value: forecast.forecast_next_quarter,
            type: 'forecast',
            confidence: forecast.confidence_score
          },
          {
            period: 'Next Year',
            value: forecast.forecast_next_year,
            type: 'forecast',
            confidence: forecast.confidence_score
          }
        ];

        charts.push({
          id: `forecast-${forecast.metric}`,
          type: 'line',
          title: `${this.formatKPIName(forecast.metric)} Forecast`,
          subtitle: `Predicted performance with ${(forecast.confidence_score * 100).toFixed(0)}% confidence`,
          data: forecastData,
          config: {
            xAxis: { dataKey: 'period' },
            yAxis: { tickFormatter: this.formatNumber },
            lines: [
              {
                dataKey: 'value',
                stroke: '#8884d8',
                strokeDasharray: '5 5',
                name: 'Forecast'
              }
            ],
            areas: [
              {
                dataKey: 'value',
                fill: '#8884d8',
                fillOpacity: 0.1
              }
            ]
          },
          insights: [
            `${forecast.trend} trend expected`,
            `Growth rate: ${(forecast.growth_rate * 100).toFixed(1)}%`
          ]
        });
      });
    }

    return charts;
  }

  async createDashboards(charts, analysisResults) {
    const dashboards = [];
    
    // Executive Dashboard
    dashboards.push({
      id: 'executive-dashboard',
      title: 'Executive Overview',
      layout: 'grid',
      widgets: [
        {
          id: 'kpi-summary',
          type: 'kpi-cards',
          span: { cols: 4, rows: 1 },
          data: this.extractKPISummary(analysisResults)
        },
        {
          id: 'main-chart',
          type: 'chart',
          chartId: 'time-series-multi',
          span: { cols: 3, rows: 2 }
        },
        {
          id: 'trend-indicator',
          type: 'chart',
          chartId: 'trend-strength',
          span: { cols: 1, rows: 2 }
        }
      ]
    });
    
    // Operational Dashboard
    dashboards.push({
      id: 'operational-dashboard',
      title: 'Operational Metrics',
      layout: 'flex',
      widgets: charts.slice(0, 6).map(chart => ({
        id: `widget-${chart.id}`,
        type: 'chart',
        chartId: chart.id,
        span: { cols: 2, rows: 1 }
      }))
    });

    return dashboards;
  }

  async generateVisualInsights(charts, analysisResults) {
    const insights = [];
    
    // Chart-specific insights
    charts.forEach(chart => {
      if (chart.insights) {
        insights.push(...chart.insights.map(insight => ({
          type: 'visual',
          chartId: chart.id,
          message: insight,
          importance: 'medium'
        })));
      }
    });
    
    // Pattern recognition insights
    if (charts.length > 5) {
      insights.push({
        type: 'pattern',
        message: `Generated ${charts.length} visualizations for comprehensive analysis`,
        importance: 'low'
      });
    }
    
    return insights;
  }

  async createInteractiveElements(data, charts) {
    return {
      filters: this.generateFilters(data),
      drilldowns: this.generateDrilldowns(charts),
      tooltips: this.generateTooltips(charts),
      animations: this.generateAnimations(charts)
    };
  }

  async generatePresentations(dashboards, visualInsights) {
    return {
      slides: this.generateSlides(dashboards),
      narratives: this.generateNarratives(visualInsights),
      exportFormats: ['png', 'svg', 'pdf']
    };
  }

  // Utility methods
  getNumericColumns(data) {
    if (!data || data.length === 0) return [];
    return Object.keys(data[0]).filter(key => 
      data.some(row => typeof row[key] === 'number' && !isNaN(row[key]))
    );
  }

  getTimeColumns(data) {
    if (!data || data.length === 0) return [];
    return Object.keys(data[0]).filter(key => 
      key.toLowerCase().includes('date') || 
      key.toLowerCase().includes('time')
    );
  }

  formatKPIName(name) {
    return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  formatNumber(value) {
    if (typeof value !== 'number') return value;
    
    if (Math.abs(value) >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (Math.abs(value) >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    } else {
      return value.toFixed(2);
    }
  }

  formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  }

  getColor(index) {
    const colors = [
      '#8884d8', '#82ca9d', '#ffc658', '#ff7300', 
      '#00ff00', '#ff00ff', '#00ffff', '#ffff00'
    ];
    return colors[index % colors.length];
  }

  calculateQuartile(sortedValues, quartile) {
    const index = quartile * (sortedValues.length - 1);
    if (Number.isInteger(index)) {
      return sortedValues[index];
    } else {
      const lower = Math.floor(index);
      const upper = Math.ceil(index);
      const weight = index - lower;
      return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
    }
  }

  detectOutliers(values) {
    const sorted = values.sort((a, b) => a - b);
    const q1 = this.calculateQuartile(sorted, 0.25);
    const q3 = this.calculateQuartile(sorted, 0.75);
    const iqr = q3 - q1;
    const lowerBound = q1 - 1.5 * iqr;
    const upperBound = q3 + 1.5 * iqr;
    
    return values.filter(value => value < lowerBound || value > upperBound);
  }

  calculateCorrelationMatrix(data, columns) {
    const matrix = [];
    
    for (let i = 0; i < columns.length; i++) {
      for (let j = 0; j < columns.length; j++) {
        const col1Values = data.map(row => parseFloat(row[columns[i]])).filter(v => !isNaN(v));
        const col2Values = data.map(row => parseFloat(row[columns[j]])).filter(v => !isNaN(v));
        
        const correlation = this.calculateCorrelation(col1Values, col2Values);
        
        matrix.push({
          x: columns[i],
          y: columns[j],
          value: correlation
        });
      }
    }
    
    return matrix;
  }

  calculateCorrelation(x, y) {
    if (x.length !== y.length || x.length === 0) return 0;
    
    const n = x.length;
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
    const sumYY = y.reduce((sum, yi) => sum + yi * yi, 0);
    
    const correlation = (n * sumXY - sumX * sumY) / 
      Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));
    
    return isNaN(correlation) ? 0 : correlation;
  }

  extractKPISummary(analysisResults) {
    const summary = [];
    
    if (analysisResults.execute?.kpiCalculations) {
      Object.entries(analysisResults.execute.kpiCalculations).forEach(([key, value]) => {
        if (value && typeof value === 'object' && value.current_value !== undefined) {
          summary.push({
            name: this.formatKPIName(key),
            value: value.current_value,
            trend: value.trend || 'stable',
            growth: value.growth_rate || 0
          });
        }
      });
    }
    
    return summary.slice(0, 4); // Top 4 KPIs
  }

  generateFilters(data) {
    const filters = {};
    
    Object.keys(data[0] || {}).forEach(column => {
      const uniqueValues = [...new Set(data.map(row => row[column]))];
      
      if (uniqueValues.length < 20 && uniqueValues.length > 1) {
        filters[column] = {
          type: 'select',
          options: uniqueValues
        };
      } else if (typeof data[0][column] === 'number') {
        const values = data.map(row => row[column]).filter(v => !isNaN(v));
        filters[column] = {
          type: 'range',
          min: Math.min(...values),
          max: Math.max(...values)
        };
      }
    });
    
    return filters;
  }

  generateDrilldowns(charts) {
    return charts.map(chart => ({
      chartId: chart.id,
      levels: ['overview', 'detailed', 'individual']
    }));
  }

  generateTooltips(charts) {
    return charts.map(chart => ({
      chartId: chart.id,
      enabled: true,
      showData: true,
      showInsights: true
    }));
  }

  generateAnimations(charts) {
    return {
      entrance: 'fadeIn',
      duration: 1000,
      stagger: 200
    };
  }

  generateSlides(dashboards) {
    return dashboards.map((dashboard, index) => ({
      id: `slide-${index}`,
      title: dashboard.title,
      content: dashboard.widgets,
      layout: dashboard.layout
    }));
  }

  generateNarratives(insights) {
    return insights.map(insight => ({
      insight: insight.message,
      context: insight.type,
      importance: insight.importance
    }));
  }

  assessVisualComplexity(charts) {
    const complexityScore = charts.reduce((score, chart) => {
      let chartComplexity = 1;
      
      if (chart.type === 'heatmap' || chart.type === 'scatter') chartComplexity += 2;
      if (chart.data && chart.data.length > 50) chartComplexity += 1;
      if (chart.config && chart.config.lines && chart.config.lines.length > 3) chartComplexity += 1;
      
      return score + chartComplexity;
    }, 0);
    
    return complexityScore / charts.length;
  }

  getStatus() {
    return {
      agentId: this.agentId,
      status: this.status,
      pattern: this.pattern,
      capabilities: this.capabilities,
      last_execution: this.lastExecution || null
    };
  }
}

module.exports = ExpresserAgent;
