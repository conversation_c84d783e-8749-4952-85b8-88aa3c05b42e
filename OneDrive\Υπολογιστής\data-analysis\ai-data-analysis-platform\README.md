# AI Data Analysis Platform

A comprehensive AI-powered business data analysis platform that leverages DeepSeek AI and a sophisticated multi-agent system to provide intelligent insights from your business data.

## 🚀 Features

- **Multi-Agent Analysis System**: Specialized AI agents for different analysis tasks
- **DeepSeek AI Integration**: Advanced AI capabilities for data interpretation
- **Business Intelligence**: Automated KPI extraction and trend analysis
- **Interactive Dashboards**: Real-time data visualization and reporting
- **File Upload Support**: CSV, Excel, and JSON data import
- **Custom Reports**: Generate tailored business reports
- **RESTful API**: Complete backend API for integration
- **Modern UI**: React-based responsive frontend

## 🏗️ Architecture

### Backend (Node.js/Express)
- **Multi-Agent System**: Executor, Expresser, Reviewer, DataFining, and Storyteller agents
- **DeepSeek Integration**: AI-powered data analysis and insights
- **Business Analyzer**: Core analysis engine
- **File Processing**: Data parsing and validation
- **Authentication**: JWT-based security
- **Database**: MongoDB integration

### Frontend (React)
- **Dashboard**: Overview of analyses and insights
- **File Upload**: Drag-and-drop file handling
- **Analysis Views**: Interactive data visualization
- **Report Generation**: Custom report builder
- **Real-time Updates**: WebSocket integration

## 📋 Prerequisites

- Node.js 18.0.0 or higher
- npm 8.0.0 or higher
- MongoDB 5.0 or higher
- DeepSeek API key

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/ai-data-analysis-platform.git
   cd ai-data-analysis-platform
   ```

2. **Install dependencies**
   ```bash
   npm run setup
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start MongoDB**
   ```bash
   # Using Docker
   docker run -d -p 27017:27017 --name mongodb mongo:latest
   
   # Or use your local MongoDB installation
   mongod
   ```

## 🚀 Quick Start

### Development Mode
```bash
# Start both backend and frontend
npm run dev

# Or start individually
npm run dev:backend   # Backend on http://localhost:3001
npm run dev:frontend  # Frontend on http://localhost:3000
```

### Production Build
```bash
# Build the application
npm run build

# Start production server
npm start
```

### Docker Deployment
```bash
# Build and run with Docker Compose
npm run docker:build
npm run docker:run
```

## 📁 Project Structure

```
ai-data-analysis-platform/
├── backend/
│   ├── src/
│   │   ├── agents/          # AI agent implementations
│   │   ├── config/          # Configuration files
│   │   ├── controllers/     # Route controllers
│   │   ├── middleware/      # Express middleware
│   │   ├── models/          # Database models
│   │   ├── routes/          # API routes
│   │   ├── services/        # Business logic services
│   │   └── utils/           # Utility functions
│   ├── tests/               # Backend tests
│   └── package.json
├── frontend/
│   ├── public/              # Static assets
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── contexts/        # React contexts
│   │   ├── hooks/           # Custom hooks
│   │   ├── services/        # API services
│   │   ├── styles/          # CSS and styling
│   │   └── utils/           # Utility functions
│   ├── tests/               # Frontend tests
│   └── package.json
├── shared/
│   ├── types/               # TypeScript type definitions
│   └── constants/           # Shared constants
├── docs/                    # Documentation
├── docker-compose.yml       # Docker configuration
├── Dockerfile              # Docker image definition
└── README.md
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout

### File Upload
- `POST /api/upload` - Upload data files
- `GET /api/upload/:fileId` - Get file details

### Analysis
- `POST /api/analysis` - Start new analysis
- `GET /api/analysis/:analysisId` - Get analysis results
- `GET /api/analysis` - List user analyses
- `DELETE /api/analysis/:analysisId` - Delete analysis

### Reports
- `POST /api/analysis/:analysisId/report` - Generate custom report

## 🧪 Testing

```bash
# Run all tests
npm test

# Run backend tests only
npm run test:backend

# Run frontend tests only
npm run test:frontend
```

## 🔍 Code Quality

```bash
# Lint all code
npm run lint

# Format code
npx prettier --write .
```

## 🌐 Environment Variables

See `.env.example` for all available configuration options.

### Required Variables
- `DEEPSEEK_API_KEY`: Your DeepSeek API key
- `DATABASE_URL`: MongoDB connection string
- `JWT_SECRET`: Secret key for JWT tokens

## 🐳 Docker Support

The platform includes full Docker support with multi-stage builds for production optimization.

```bash
# Development with Docker Compose
docker-compose -f docker-compose.dev.yml up

# Production deployment
docker-compose up -d
```

## 📊 Multi-Agent System

### Agent Roles
- **ExecutorAgent**: Data processing and computation
- **ExpresserAgent**: Visualization and chart generation
- **ReviewerAgent**: Quality assurance and validation
- **DataFiningAgent**: Data cleaning and preprocessing
- **StorytellerAgent**: Narrative generation and insights

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔗 Links

- [DeepSeek AI](https://deepseek.com)
- [Documentation](./docs/)
- [API Reference](./docs/API.md)
- [Agent System Guide](./docs/AGENTS.md)
- [Deployment Guide](./docs/DEPLOYMENT.md)

## 📞 Support

For support, email <EMAIL> or create an issue on GitHub.

---

Built with ❤️ using DeepSeek AI, Node.js, and React.
