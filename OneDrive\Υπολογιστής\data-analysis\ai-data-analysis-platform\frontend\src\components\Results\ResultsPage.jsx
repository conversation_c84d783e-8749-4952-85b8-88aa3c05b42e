import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import axios from 'axios';

const ResultsPage = () => {
  const { sessionId, component } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(component || 'summary');

  // Fetch analysis results
  const { data: results, isLoading, error } = useQuery(
    ['analysisResults', sessionId],
    async () => {
      const response = await axios.get(`/api/analysis/results/${sessionId}`);
      return response.data;
    },
    {
      enabled: !!sessionId,
      refetchOnWindowFocus: false,
    }
  );

  useEffect(() => {
    if (!sessionId) {
      navigate('/');
      return;
    }
  }, [sessionId, navigate]);

  useEffect(() => {
    if (component && component !== activeTab) {
      setActiveTab(component);
    }
  }, [component, activeTab]);

  const tabs = [
    { id: 'summary', label: 'Summary', icon: '📊' },
    { id: 'charts', label: 'Visualizations', icon: '📈' },
    { id: 'insights', label: 'Insights', icon: '💡' },
    { id: 'recommendations', label: 'Recommendations', icon: '🎯' },
    { id: 'data', label: 'Raw Data', icon: '📋' },
  ];

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
    navigate(`/results/${sessionId}/${tabId}`);
  };

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="h-64 bg-gray-200 rounded mb-4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <h2 className="text-lg font-medium text-red-800 mb-2">
            Error Loading Results
          </h2>
          <p className="text-red-600">
            {error.message || 'Unable to load analysis results.'}
          </p>
          <button
            onClick={() => navigate('/')}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Analysis Results
            </h1>
            <p className="text-gray-600">
              Session: <span className="font-mono text-sm">{sessionId}</span>
              {results?.createdAt && (
                <span className="ml-4">
                  • Generated {new Date(results.createdAt).toLocaleString()}
                </span>
              )}
            </p>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={() => navigate('/')}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              ← Back to Dashboard
            </button>
            
            <button
              onClick={() => window.print()}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Export PDF
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabChange(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="bg-white rounded-lg shadow">
        {activeTab === 'summary' && (
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Analysis Summary</h2>
            {results?.summary ? (
              <div className="prose max-w-none">
                <p className="text-gray-700 leading-relaxed">
                  {results.summary}
                </p>
              </div>
            ) : (
              <p className="text-gray-500">No summary available.</p>
            )}
          </div>
        )}

        {activeTab === 'charts' && (
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Data Visualizations</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {results?.charts?.length > 0 ? (
                results.charts.map((chart, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <h3 className="font-medium mb-2">{chart.title}</h3>
                    {/* Chart component would go here */}
                    <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
                      <span className="text-gray-500">Chart: {chart.type}</span>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500">No visualizations available.</p>
              )}
            </div>
          </div>
        )}

        {activeTab === 'insights' && (
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Key Insights</h2>
            {results?.insights?.length > 0 ? (
              <div className="space-y-4">
                {results.insights.map((insight, index) => (
                  <div key={index} className="border-l-4 border-blue-500 pl-4">
                    <h3 className="font-medium text-gray-900">{insight.title}</h3>
                    <p className="text-gray-700 mt-1">{insight.description}</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No insights available.</p>
            )}
          </div>
        )}

        {activeTab === 'recommendations' && (
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Recommendations</h2>
            {results?.recommendations?.length > 0 ? (
              <div className="space-y-4">
                {results.recommendations.map((rec, index) => (
                  <div key={index} className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <h3 className="font-medium text-yellow-800">{rec.title}</h3>
                    <p className="text-yellow-700 mt-1">{rec.description}</p>
                    {rec.priority && (
                      <span className={`inline-block mt-2 px-2 py-1 text-xs rounded ${
                        rec.priority === 'high' ? 'bg-red-100 text-red-800' :
                        rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {rec.priority} priority
                      </span>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No recommendations available.</p>
            )}
          </div>
        )}

        {activeTab === 'data' && (
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Raw Data</h2>
            {results?.data ? (
              <div className="bg-gray-50 rounded-lg p-4 overflow-auto">
                <pre className="text-sm text-gray-800">
                  {JSON.stringify(results.data, null, 2)}
                </pre>
              </div>
            ) : (
              <p className="text-gray-500">No raw data available.</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ResultsPage;
