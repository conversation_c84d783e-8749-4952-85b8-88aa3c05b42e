import React from 'react';
import {
  Box,
  SimpleGrid,
  Text,
  Progress,
  Badge,
  VStack,
  HStack,
  Icon,
  useColorModeValue,
  Spinner,
} from '@chakra-ui/react';
import {
  MdSettings,
  MdAnalytics,
  MdVisibility,
  MdVerified,
  MdAutoStories,
  MdCheckCircle,
  MdError,
} from 'react-icons/md';

const AgentCard = ({ agentType, status, progress = 0, result }) => {
  const bgColor = useColorModeValue('white', 'navy.800');
  const textColor = useColorModeValue('secondaryGray.900', 'white');
  
  const agentConfig = {
    executor: {
      name: 'Executor Agent',
      description: 'Performing statistical analysis and calculations',
      icon: MdSettings,
      color: 'blue',
    },
    expresser: {
      name: 'Expresser Agent',
      description: 'Creating visualizations and charts',
      icon: MdVisibility,
      color: 'purple',
    },
    reviewer: {
      name: 'Reviewer Agent',
      description: 'Validating results and quality assurance',
      icon: MdVerified,
      color: 'green',
    },
    datafining: {
      name: 'Data Mining Agent',
      description: 'Discovering patterns and insights',
      icon: MdAnalytics,
      color: 'orange',
    },
    storyteller: {
      name: 'Storyteller Agent',
      description: 'Generating business narratives and reports',
      icon: MdAutoStories,
      color: 'teal',
    },
  };

  const config = agentConfig[agentType] || {
    name: 'Unknown Agent',
    description: 'Processing...',
    icon: MdSettings,
    color: 'gray',
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return MdCheckCircle;
      case 'failed':
        return MdError;
      case 'running':
        return null; // Will show spinner
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'failed':
        return 'red';
      case 'running':
        return 'blue';
      default:
        return 'gray';
    }
  };

  return (
    <Box
      bg={bgColor}
      p="4"
      borderRadius="12px"
      border="1px solid"
      borderColor="gray.200"
      transition="all 0.3s"
      _hover={{ transform: 'translateY(-2px)', boxShadow: 'lg' }}
    >
      <VStack align="stretch" spacing="3">
        <HStack justify="space-between">
          <HStack spacing="3">
            <Box
              p="2"
              borderRadius="8px"
              bg={`${config.color}.50`}
              color={`${config.color}.500`}
            >
              <Icon as={config.icon} boxSize="5" />
            </Box>
            <VStack align="start" spacing="0">
              <Text fontWeight="bold" fontSize="sm" color={textColor}>
                {config.name}
              </Text>
              <Text fontSize="xs" color="gray.500">
                {config.description}
              </Text>
            </VStack>
          </HStack>
          
          <HStack>
            {status === 'running' && <Spinner size="sm" color={`${config.color}.500`} />}
            {getStatusIcon() && (
              <Icon as={getStatusIcon()} color={`${getStatusColor()}.500`} boxSize="5" />
            )}
            <Badge colorScheme={getStatusColor()} variant="subtle" fontSize="xs">
              {status || 'pending'}
            </Badge>
          </HStack>
        </HStack>

        {status === 'running' && (
          <Progress
            value={progress}
            size="sm"
            colorScheme={config.color}
            borderRadius="4px"
          />
        )}

        {status === 'completed' && result && (
          <Box p="2" bg="green.50" borderRadius="6px">
            <Text fontSize="xs" color="green.700">
              ✓ Analysis completed successfully
            </Text>
          </Box>
        )}

        {status === 'failed' && (
          <Box p="2" bg="red.50" borderRadius="6px">
            <Text fontSize="xs" color="red.700">
              ✗ Analysis failed - please retry
            </Text>
          </Box>
        )}
      </VStack>
    </Box>
  );
};

const AgentStatus = ({ agentStates }) => {
  const agents = ['executor', 'expresser', 'reviewer', 'datafining', 'storyteller'];
  
  const completedAgents = Object.values(agentStates).filter(
    state => state.status === 'completed'
  ).length;
  
  const totalAgents = agents.length;
  const overallProgress = (completedAgents / totalAgents) * 100;

  return (
    <VStack spacing="6" align="stretch">
      {/* Overall Progress */}
      <Box>
        <HStack justify="space-between" mb="2">
          <Text fontWeight="bold" fontSize="md">
            AI Agent Swarm Progress
          </Text>
          <Text fontSize="sm" color="gray.500">
            {completedAgents}/{totalAgents} agents completed
          </Text>
        </HStack>
        <Progress
          value={overallProgress}
          size="lg"
          colorScheme="blue"
          borderRadius="8px"
        />
      </Box>

      {/* Individual Agent Cards */}
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing="4">
        {agents.map((agentType) => (
          <AgentCard
            key={agentType}
            agentType={agentType}
            status={agentStates[agentType]?.status}
            progress={agentStates[agentType]?.progress}
            result={agentStates[agentType]?.result}
          />
        ))}
      </SimpleGrid>
    </VStack>
  );
};

export default AgentStatus;
