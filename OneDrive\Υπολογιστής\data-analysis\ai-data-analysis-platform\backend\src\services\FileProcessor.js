const fs = require('fs').promises;
const path = require('path');
const csv = require('csv-parser');
const XLSX = require('xlsx');
const winston = require('winston');
const { v4: uuidv4 } = require('uuid');

class FileProcessor {
  constructor() {
    this.supportedTypes = [
      'text/csv',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/plain',
      'application/json'
    ];

    this.maxFileSize = 50 * 1024 * 1024; // 50MB
    this.uploadDir = process.env.UPLOAD_DIR || 'uploads';

    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/file-processor.log' })
      ]
    });

    this.initializeUploadDir();
  }

  async initializeUploadDir() {
    try {
      await fs.mkdir(this.uploadDir, { recursive: true });
      this.logger.info(`Upload directory initialized: ${this.uploadDir}`);
    } catch (error) {
      this.logger.error('Failed to initialize upload directory:', error);
    }
  }

  validateFile(file) {
    const errors = [];

    if (!file) {
      errors.push('No file provided');
      return { valid: false, errors };
    }

    if (!this.supportedTypes.includes(file.mimetype)) {
      errors.push(`Unsupported file type: ${file.mimetype}. Supported types: ${this.supportedTypes.join(', ')}`);
    }

    if (file.size > this.maxFileSize) {
      errors.push(`File size exceeds limit: ${file.size} bytes. Maximum: ${this.maxFileSize} bytes`);
    }

    if (!file.originalname || file.originalname.trim() === '') {
      errors.push('Invalid filename');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  async processFile(file) {
    try {
      const validation = this.validateFile(file);
      if (!validation.valid) {
        throw new Error(`File validation failed: ${validation.errors.join(', ')}`);
      }

      const fileId = uuidv4();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${timestamp}-${fileId}-${file.originalname}`;
      const filepath = path.join(this.uploadDir, filename);

      // Save file to disk
      await fs.writeFile(filepath, file.buffer);

      this.logger.info('File saved:', { filename, size: file.size, type: file.mimetype });

      // Process file based on type
      const processedData = await this.parseFileContent(filepath, file.mimetype);

      const result = {
        fileId,
        filename,
        originalName: file.originalname,
        filepath,
        size: file.size,
        mimetype: file.mimetype,
        uploadedAt: new Date(),
        data: processedData.data,
        metadata: {
          ...processedData.metadata,
          processingTime: processedData.processingTime
        }
      };

      this.logger.info('File processed successfully:', {
        fileId,
        rows: processedData.metadata.rows,
        columns: processedData.metadata.columns
      });

      return result;
    } catch (error) {
      this.logger.error('File processing failed:', error);
      throw error;
    }
  }

  async parseFileContent(filepath, mimetype) {
    const startTime = Date.now();
    let data = [];
    let metadata = {};

    try {
      switch (mimetype) {
        case 'text/csv':
          const result = await this.parseCSV(filepath);
          data = result.data;
          metadata = result.metadata;
          break;

        case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        case 'application/vnd.ms-excel':
          const excelResult = await this.parseExcel(filepath);
          data = excelResult.data;
          metadata = excelResult.metadata;
          break;

        case 'application/json':
          const jsonResult = await this.parseJSON(filepath);
          data = jsonResult.data;
          metadata = jsonResult.metadata;
          break;

        case 'text/plain':
          const textResult = await this.parseText(filepath);
          data = textResult.data;
          metadata = textResult.metadata;
          break;

        default:
          throw new Error(`Unsupported file type: ${mimetype}`);
      }

      const processingTime = Date.now() - startTime;

      // Analyze data structure
      const analysis = this.analyzeDataStructure(data);

      return {
        data,
        metadata: {
          ...metadata,
          ...analysis,
          processingTime
        }
      };
    } catch (error) {
      this.logger.error('File parsing failed:', error);
      throw error;
    }
  }

  async parseCSV(filepath) {
    return new Promise((resolve, reject) => {
      const data = [];
      const headers = [];
      let rowCount = 0;

      const stream = require('fs').createReadStream(filepath)
        .pipe(csv({
          mapHeaders: ({ header }) => {
            if (headers.length === 0 || !headers.includes(header)) {
              headers.push(header);
            }
            return header;
          }
        }))
        .on('data', (row) => {
          data.push(row);
          rowCount++;
        })
        .on('end', () => {
          resolve({
            data,
            metadata: {
              rows: rowCount,
              columns: headers.length,
              headers,
              format: 'csv'
            }
          });
        })
        .on('error', reject);
    });
  }

  async parseExcel(filepath) {
    try {
      const workbook = XLSX.readFile(filepath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      
      const data = XLSX.utils.sheet_to_json(worksheet, { defval: '' });
      const headers = Object.keys(data[0] || {});

      return {
        data,
        metadata: {
          rows: data.length,
          columns: headers.length,
          headers,
          format: 'excel',
          sheets: workbook.SheetNames,
          activeSheet: sheetName
        }
      };
    } catch (error) {
      throw new Error(`Excel parsing failed: ${error.message}`);
    }
  }

  async parseJSON(filepath) {
    try {
      const content = await fs.readFile(filepath, 'utf8');
      const data = JSON.parse(content);

      let processedData = [];
      let headers = [];

      if (Array.isArray(data)) {
        processedData = data;
        if (data.length > 0 && typeof data[0] === 'object') {
          headers = Object.keys(data[0]);
        }
      } else if (typeof data === 'object') {
        processedData = [data];
        headers = Object.keys(data);
      } else {
        processedData = [{ value: data }];
        headers = ['value'];
      }

      return {
        data: processedData,
        metadata: {
          rows: processedData.length,
          columns: headers.length,
          headers,
          format: 'json'
        }
      };
    } catch (error) {
      throw new Error(`JSON parsing failed: ${error.message}`);
    }
  }

  async parseText(filepath) {
    try {
      const content = await fs.readFile(filepath, 'utf8');
      const lines = content.split('\n').filter(line => line.trim() !== '');
      
      const data = lines.map((line, index) => ({
        lineNumber: index + 1,
        content: line.trim()
      }));

      return {
        data,
        metadata: {
          rows: data.length,
          columns: 2,
          headers: ['lineNumber', 'content'],
          format: 'text',
          totalLines: lines.length
        }
      };
    } catch (error) {
      throw new Error(`Text parsing failed: ${error.message}`);
    }
  }

  analyzeDataStructure(data) {
    if (!Array.isArray(data) || data.length === 0) {
      return {
        dataTypes: {},
        numericColumns: [],
        textColumns: [],
        dateColumns: [],
        hasNulls: false,
        sampleData: []
      };
    }

    const sample = data.slice(0, Math.min(100, data.length));
    const firstRow = data[0];
    const columns = Object.keys(firstRow);
    
    const dataTypes = {};
    const numericColumns = [];
    const textColumns = [];
    const dateColumns = [];
    let hasNulls = false;

    columns.forEach(column => {
      const values = sample.map(row => row[column]).filter(val => val !== null && val !== undefined && val !== '');
      
      if (values.length === 0) {
        hasNulls = true;
        dataTypes[column] = 'empty';
        return;
      }

      // Check for dates
      const dateValues = values.filter(val => {
        const date = new Date(val);
        return !isNaN(date.getTime()) && isNaN(Number(val));
      });

      if (dateValues.length > values.length * 0.5) {
        dataTypes[column] = 'date';
        dateColumns.push(column);
        return;
      }

      // Check for numbers
      const numericValues = values.filter(val => !isNaN(Number(val)) && val !== '');
      
      if (numericValues.length > values.length * 0.8) {
        dataTypes[column] = 'number';
        numericColumns.push(column);
      } else {
        dataTypes[column] = 'text';
        textColumns.push(column);
      }

      // Check for nulls
      if (values.length < sample.length) {
        hasNulls = true;
      }
    });

    return {
      dataTypes,
      numericColumns,
      textColumns,
      dateColumns,
      hasNulls,
      sampleData: sample.slice(0, 5)
    };
  }

  async getFileInfo(filepath) {
    try {
      const stats = await fs.stat(filepath);
      return {
        exists: true,
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        isFile: stats.isFile()
      };
    } catch (error) {
      return {
        exists: false,
        error: error.message
      };
    }
  }

  async deleteFile(filepath) {
    try {
      await fs.unlink(filepath);
      this.logger.info('File deleted:', filepath);
      return { success: true };
    } catch (error) {
      this.logger.error('File deletion failed:', error);
      return { success: false, error: error.message };
    }
  }

  async cleanupOldFiles(maxAge = 24 * 60 * 60 * 1000) { // 24 hours
    try {
      const files = await fs.readdir(this.uploadDir);
      const now = Date.now();
      let deletedCount = 0;

      for (const file of files) {
        const filepath = path.join(this.uploadDir, file);
        const stats = await fs.stat(filepath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await this.deleteFile(filepath);
          deletedCount++;
        }
      }

      this.logger.info(`Cleanup completed: ${deletedCount} files deleted`);
      return { deletedCount };
    } catch (error) {
      this.logger.error('Cleanup failed:', error);
      throw error;
    }
  }

  getSupportedTypes() {
    return this.supportedTypes;
  }

  getMaxFileSize() {
    return this.maxFileSize;
  }
}

module.exports = FileProcessor;
