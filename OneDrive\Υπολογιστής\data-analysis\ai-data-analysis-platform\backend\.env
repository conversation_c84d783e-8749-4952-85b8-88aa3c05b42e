# Backend Server Configuration
PORT=5000
NODE_ENV=development

# DeepSeek API Configuration
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_API_URL=https://api.deepseek.com/v1

# Enhanced AI API Configuration
# Jina AI Embeddings API
JINA_API_KEY=jina_66d0dc09e9734fe4ab8590f9ed73dfdcUizdXTFmIhHxCB9SXSjrwHuMTNVD
JINA_API_URL=https://api.jina.ai/v1

# Cohere AI Embeddings API
COHERE_API_KEY=rlyiSXlCgNP81JU5TX6proOZUCO0AXhYu1sNMHCK
COHERE_API_URL=https://api.cohere.ai/v1

# HuggingFace Transformers API
HUGGINGFACE_API_KEY=*************************************
HUGGINGFACE_API_URL=https://api-inference.huggingface.co

# Database Configuration (optional for basic functionality)
MONGODB_URI=mongodb://localhost:27017/ai-data-analysis

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=csv,xlsx,xls,json

# CORS Configuration
CORS_ORIGIN=http://localhost:3001

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
