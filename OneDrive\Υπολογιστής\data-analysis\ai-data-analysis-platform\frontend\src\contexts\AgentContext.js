import React, { createContext, useContext, useReducer, useEffect } from 'react';
import api from '../services/api';

// Agent Context
const AgentContext = createContext();

// Action types
const AGENT_ACTIONS = {
  FETCH_AGENTS_START: 'FETCH_AGENTS_START',
  FETCH_AGENTS_SUCCESS: 'FETCH_AGENTS_SUCCESS',
  FETCH_AGENTS_ERROR: 'FETCH_AGENTS_ERROR',
  EXECUTE_AGENT_START: 'EXECUTE_AGENT_START',
  EXECUTE_AGENT_SUCCESS: 'EXECUTE_AGENT_SUCCESS',
  EXECUTE_AGENT_ERROR: 'EXECUTE_AGENT_ERROR',
  UPDATE_AGENT_STATUS: 'UPDATE_AGENT_STATUS',
  ADD_AGENT_EXECUTION: 'ADD_AGENT_EXECUTION',
  REMOVE_AGENT_EXECUTION: 'REMOVE_AGENT_EXECUTION',
  SET_AGENT_TYPES: 'SET_AGENT_TYPES',
  CLEAR_AGENTS: 'CLEAR_AGENTS'
};

// Initial state
const initialState = {
  agents: [],
  agentTypes: [],
  executions: [],
  loading: false,
  error: null,
  executing: {},
  lastUpdated: null
};

// Reducer function
const agentReducer = (state, action) => {
  switch (action.type) {
    case AGENT_ACTIONS.FETCH_AGENTS_START:
      return {
        ...state,
        loading: true,
        error: null
      };

    case AGENT_ACTIONS.FETCH_AGENTS_SUCCESS:
      return {
        ...state,
        loading: false,
        executions: action.payload.executions || [],
        error: null,
        lastUpdated: new Date()
      };

    case AGENT_ACTIONS.FETCH_AGENTS_ERROR:
      return {
        ...state,
        loading: false,
        error: action.payload
      };

    case AGENT_ACTIONS.EXECUTE_AGENT_START:
      return {
        ...state,
        executing: {
          ...state.executing,
          [action.payload.type]: true
        },
        error: null
      };

    case AGENT_ACTIONS.EXECUTE_AGENT_SUCCESS:
      return {
        ...state,
        executing: {
          ...state.executing,
          [action.payload.type]: false
        },
        executions: [action.payload.execution, ...state.executions],
        error: null
      };

    case AGENT_ACTIONS.EXECUTE_AGENT_ERROR:
      return {
        ...state,
        executing: {
          ...state.executing,
          [action.payload.type]: false
        },
        error: action.payload.error
      };

    case AGENT_ACTIONS.UPDATE_AGENT_STATUS:
      return {
        ...state,
        executions: state.executions.map(execution =>
          execution.agentId === action.payload.agentId
            ? { ...execution, ...action.payload.updates }
            : execution
        )
      };

    case AGENT_ACTIONS.ADD_AGENT_EXECUTION:
      return {
        ...state,
        executions: [action.payload, ...state.executions]
      };

    case AGENT_ACTIONS.REMOVE_AGENT_EXECUTION:
      return {
        ...state,
        executions: state.executions.filter(
          execution => execution.agentId !== action.payload.agentId
        )
      };

    case AGENT_ACTIONS.SET_AGENT_TYPES:
      return {
        ...state,
        agentTypes: action.payload
      };

    case AGENT_ACTIONS.CLEAR_AGENTS:
      return {
        ...initialState
      };

    default:
      return state;
  }
};

// Provider component
export const AgentProvider = ({ children }) => {
  const [state, dispatch] = useReducer(agentReducer, initialState);

  // Fetch agent executions
  const fetchAgents = async (filters = {}) => {
    try {
      dispatch({ type: AGENT_ACTIONS.FETCH_AGENTS_START });

      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      const response = await api.get(`/agents?${queryParams.toString()}`);
      
      dispatch({
        type: AGENT_ACTIONS.FETCH_AGENTS_SUCCESS,
        payload: response.data.data
      });

      return response.data.data;
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to fetch agents';
      dispatch({
        type: AGENT_ACTIONS.FETCH_AGENTS_ERROR,
        payload: errorMessage
      });
      throw error;
    }
  };

  // Fetch agent types
  const fetchAgentTypes = async () => {
    try {
      const response = await api.get('/agents/types');
      dispatch({
        type: AGENT_ACTIONS.SET_AGENT_TYPES,
        payload: response.data.data.agentTypes
      });
      return response.data.data.agentTypes;
    } catch (error) {
      console.error('Failed to fetch agent types:', error);
      throw error;
    }
  };

  // Execute agent
  const executeAgent = async (type, data, options = {}) => {
    try {
      dispatch({
        type: AGENT_ACTIONS.EXECUTE_AGENT_START,
        payload: { type }
      });

      const payload = {
        type,
        data,
        context: options.context || {},
        options: options.options || {}
      };

      const endpoint = options.async ? '/agents/execute/async' : '/agents/execute';
      const response = await api.post(endpoint, payload);

      dispatch({
        type: AGENT_ACTIONS.EXECUTE_AGENT_SUCCESS,
        payload: {
          type,
          execution: response.data.data
        }
      });

      return response.data.data;
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to execute agent';
      dispatch({
        type: AGENT_ACTIONS.EXECUTE_AGENT_ERROR,
        payload: { type, error: errorMessage }
      });
      throw error;
    }
  };

  // Get agent status
  const getAgentStatus = async (agentId) => {
    try {
      const response = await api.get(`/agents/${agentId}/status`);
      
      // Update execution status in state
      dispatch({
        type: AGENT_ACTIONS.UPDATE_AGENT_STATUS,
        payload: {
          agentId,
          updates: response.data.data
        }
      });

      return response.data.data;
    } catch (error) {
      console.error('Failed to get agent status:', error);
      throw error;
    }
  };

  // Get agent result
  const getAgentResult = async (agentId) => {
    try {
      const response = await api.get(`/agents/${agentId}/result`);
      return response.data.data;
    } catch (error) {
      console.error('Failed to get agent result:', error);
      throw error;
    }
  };

  // Cancel agent execution
  const cancelAgentExecution = async (agentId) => {
    try {
      await api.post(`/agents/${agentId}/cancel`);
      
      dispatch({
        type: AGENT_ACTIONS.UPDATE_AGENT_STATUS,
        payload: {
          agentId,
          updates: { status: 'cancelled' }
        }
      });

      return true;
    } catch (error) {
      console.error('Failed to cancel agent execution:', error);
      throw error;
    }
  };

  // Delete agent execution
  const deleteAgentExecution = async (agentId) => {
    try {
      await api.delete(`/agents/${agentId}`);
      
      dispatch({
        type: AGENT_ACTIONS.REMOVE_AGENT_EXECUTION,
        payload: { agentId }
      });

      return true;
    } catch (error) {
      console.error('Failed to delete agent execution:', error);
      throw error;
    }
  };

  // Poll agent status for running executions
  const pollAgentStatus = async (agentId, interval = 2000) => {
    const poll = async () => {
      try {
        const status = await getAgentStatus(agentId);
        
        if (status.status === 'running' || status.status === 'pending') {
          setTimeout(poll, interval);
        }
        
        return status;
      } catch (error) {
        console.error('Polling error:', error);
        // Stop polling on error
      }
    };

    return poll();
  };

  // Clear all agent data
  const clearAgents = () => {
    dispatch({ type: AGENT_ACTIONS.CLEAR_AGENTS });
  };

  // Auto-fetch agent types on mount
  useEffect(() => {
    fetchAgentTypes().catch(console.error);
  }, []);

  // Context value
  const value = {
    // State
    ...state,
    
    // Actions
    fetchAgents,
    fetchAgentTypes,
    executeAgent,
    getAgentStatus,
    getAgentResult,
    cancelAgentExecution,
    deleteAgentExecution,
    pollAgentStatus,
    clearAgents,
    
    // Computed values
    isExecuting: (type) => !!state.executing[type],
    getExecutionsByType: (type) => state.executions.filter(ex => ex.type === type),
    getRunningExecutions: () => state.executions.filter(ex => 
      ex.status === 'running' || ex.status === 'pending'
    ),
    getCompletedExecutions: () => state.executions.filter(ex => ex.status === 'completed'),
    getFailedExecutions: () => state.executions.filter(ex => ex.status === 'failed')
  };

  return (
    <AgentContext.Provider value={value}>
      {children}
    </AgentContext.Provider>
  );
};

// Hook to use agent context
export const useAgentContext = () => {
  const context = useContext(AgentContext);
  if (!context) {
    throw new Error('useAgentContext must be used within an AgentProvider');
  }
  return context;
};

export default AgentContext;
