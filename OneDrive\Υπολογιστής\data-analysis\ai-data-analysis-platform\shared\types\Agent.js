/**
 * Agent-related type definitions and interfaces
 */

// Agent types
export const AGENT_TYPES = {
  EXECUTOR: 'executor',
  EXPRESSER: 'expresser',
  REVIEWER: 'reviewer',
  DATAFINING: 'datafining',
  STORYTELLER: 'storyteller'
};

// Agent status
export const AGENT_STATUS = {
  IDLE: 'idle',
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

// Agent execution priority
export const AGENT_PRIORITY = {
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  URGENT: 'urgent'
};

// Agent capabilities
export const AGENT_CAPABILITIES = {
  [AGENT_TYPES.EXECUTOR]: [
    'data_processing',
    'statistical_analysis',
    'pattern_detection',
    'data_validation',
    'outlier_detection'
  ],
  [AGENT_TYPES.EXPRESSER]: [
    'report_generation',
    'data_visualization',
    'formatting',
    'template_processing',
    'export_generation'
  ],
  [AGENT_TYPES.REVIEWER]: [
    'quality_assurance',
    'accuracy_validation',
    'consistency_check',
    'error_detection',
    'compliance_verification'
  ],
  [AGENT_TYPES.DATAFINING]: [
    'data_cleaning',
    'preprocessing',
    'normalization',
    'enhancement',
    'transformation'
  ],
  [AGENT_TYPES.STORYTELLER]: [
    'narrative_generation',
    'insight_interpretation',
    'business_storytelling',
    'context_analysis',
    'recommendation_generation'
  ]
};

// Agent configuration schema
export class AgentConfig {
  constructor({
    type,
    priority = AGENT_PRIORITY.NORMAL,
    timeout = 300000, // 5 minutes default
    retries = 3,
    context = {},
    options = {}
  } = {}) {
    this.type = type;
    this.priority = priority;
    this.timeout = timeout;
    this.retries = retries;
    this.context = context;
    this.options = options;
    this.createdAt = new Date();
  }

  // Validate configuration
  validate() {
    const errors = [];

    if (!this.type || !Object.values(AGENT_TYPES).includes(this.type)) {
      errors.push('Invalid or missing agent type');
    }

    if (!Object.values(AGENT_PRIORITY).includes(this.priority)) {
      errors.push('Invalid agent priority');
    }

    if (this.timeout < 1000) {
      errors.push('Timeout must be at least 1 second');
    }

    if (this.retries < 0 || this.retries > 10) {
      errors.push('Retries must be between 0 and 10');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get agent capabilities
  getCapabilities() {
    return AGENT_CAPABILITIES[this.type] || [];
  }

  // Clone configuration
  clone() {
    return new AgentConfig({
      type: this.type,
      priority: this.priority,
      timeout: this.timeout,
      retries: this.retries,
      context: { ...this.context },
      options: { ...this.options }
    });
  }
}

// Agent execution result
export class AgentResult {
  constructor({
    agentId,
    type,
    status,
    data = null,
    error = null,
    metadata = {},
    startTime = null,
    endTime = null
  } = {}) {
    this.agentId = agentId;
    this.type = type;
    this.status = status;
    this.data = data;
    this.error = error;
    this.metadata = metadata;
    this.startTime = startTime;
    this.endTime = endTime;
    this.createdAt = new Date();
  }

  // Get execution duration
  getDuration() {
    if (!this.startTime || !this.endTime) {
      return null;
    }
    return new Date(this.endTime) - new Date(this.startTime);
  }

  // Check if execution was successful
  isSuccessful() {
    return this.status === AGENT_STATUS.COMPLETED && !this.error;
  }

  // Get formatted status
  getFormattedStatus() {
    const statusMap = {
      [AGENT_STATUS.IDLE]: { label: 'Idle', color: 'gray' },
      [AGENT_STATUS.PENDING]: { label: 'Pending', color: 'yellow' },
      [AGENT_STATUS.RUNNING]: { label: 'Running', color: 'blue' },
      [AGENT_STATUS.COMPLETED]: { label: 'Completed', color: 'green' },
      [AGENT_STATUS.FAILED]: { label: 'Failed', color: 'red' },
      [AGENT_STATUS.CANCELLED]: { label: 'Cancelled', color: 'gray' }
    };

    return statusMap[this.status] || { label: this.status, color: 'gray' };
  }
}

// Agent execution context
export class AgentContext {
  constructor({
    userId,
    sessionId,
    analysisId = null,
    fileIds = [],
    previousResults = {},
    environment = 'production',
    debug = false
  } = {}) {
    this.userId = userId;
    this.sessionId = sessionId;
    this.analysisId = analysisId;
    this.fileIds = fileIds;
    this.previousResults = previousResults;
    this.environment = environment;
    this.debug = debug;
    this.createdAt = new Date();
  }

  // Add previous result
  addPreviousResult(agentType, result) {
    this.previousResults[agentType] = result;
  }

  // Get previous result
  getPreviousResult(agentType) {
    return this.previousResults[agentType] || null;
  }

  // Check if has file access
  hasFileAccess(fileId) {
    return this.fileIds.includes(fileId);
  }
}

// Agent chain configuration
export class AgentChain {
  constructor(steps = []) {
    this.steps = steps;
    this.createdAt = new Date();
  }

  // Add step to chain
  addStep(agentConfig, dependencies = []) {
    this.steps.push({
      id: `step_${this.steps.length + 1}`,
      config: agentConfig,
      dependencies,
      status: AGENT_STATUS.PENDING
    });
    return this;
  }

  // Get next executable step
  getNextStep() {
    return this.steps.find(step => {
      if (step.status !== AGENT_STATUS.PENDING) {
        return false;
      }

      // Check if all dependencies are completed
      return step.dependencies.every(depId => {
        const depStep = this.steps.find(s => s.id === depId);
        return depStep && depStep.status === AGENT_STATUS.COMPLETED;
      });
    });
  }

  // Update step status
  updateStepStatus(stepId, status, result = null) {
    const step = this.steps.find(s => s.id === stepId);
    if (step) {
      step.status = status;
      step.result = result;
      step.updatedAt = new Date();
    }
  }

  // Check if chain is complete
  isComplete() {
    return this.steps.every(step => 
      step.status === AGENT_STATUS.COMPLETED || 
      step.status === AGENT_STATUS.FAILED ||
      step.status === AGENT_STATUS.CANCELLED
    );
  }

  // Get chain progress
  getProgress() {
    const completed = this.steps.filter(step => step.status === AGENT_STATUS.COMPLETED).length;
    return this.steps.length > 0 ? (completed / this.steps.length) * 100 : 0;
  }
}

// Agent performance metrics
export class AgentMetrics {
  constructor({
    agentId,
    type,
    executionTime = 0,
    memoryUsage = 0,
    cpuUsage = 0,
    successRate = 0,
    errorRate = 0,
    throughput = 0
  } = {}) {
    this.agentId = agentId;
    this.type = type;
    this.executionTime = executionTime;
    this.memoryUsage = memoryUsage;
    this.cpuUsage = cpuUsage;
    this.successRate = successRate;
    this.errorRate = errorRate;
    this.throughput = throughput;
    this.timestamp = new Date();
  }

  // Calculate efficiency score
  getEfficiencyScore() {
    const timeScore = Math.max(0, 100 - (this.executionTime / 1000)); // Lower time = higher score
    const successScore = this.successRate * 100;
    const resourceScore = Math.max(0, 100 - (this.memoryUsage + this.cpuUsage) / 2);
    
    return (timeScore + successScore + resourceScore) / 3;
  }
}

// Export default Agent class
export default class Agent {
  constructor(type, config = {}) {
    this.type = type;
    this.config = new AgentConfig({ type, ...config });
    this.status = AGENT_STATUS.IDLE;
    this.context = null;
    this.result = null;
    this.metrics = null;
    this.createdAt = new Date();
  }

  // Set execution context
  setContext(context) {
    this.context = context instanceof AgentContext ? context : new AgentContext(context);
    return this;
  }

  // Get agent capabilities
  getCapabilities() {
    return this.config.getCapabilities();
  }

  // Check if agent can handle capability
  canHandle(capability) {
    return this.getCapabilities().includes(capability);
  }

  // Get agent type information
  getTypeInfo() {
    const typeMap = {
      [AGENT_TYPES.EXECUTOR]: {
        name: 'Executor Agent',
        description: 'Executes data analysis tasks and generates initial insights',
        icon: 'play'
      },
      [AGENT_TYPES.EXPRESSER]: {
        name: 'Expresser Agent',
        description: 'Formats and presents analysis results in various formats',
        icon: 'file-text'
      },
      [AGENT_TYPES.REVIEWER]: {
        name: 'Reviewer Agent',
        description: 'Reviews and validates analysis results for quality assurance',
        icon: 'check-circle'
      },
      [AGENT_TYPES.DATAFINING]: {
        name: 'Data Fining Agent',
        description: 'Refines and enhances data quality and structure',
        icon: 'filter'
      },
      [AGENT_TYPES.STORYTELLER]: {
        name: 'Storyteller Agent',
        description: 'Creates narrative explanations and business stories from data',
        icon: 'book'
      }
    };

    return typeMap[this.type] || {
      name: this.type,
      description: 'Unknown agent type',
      icon: 'help-circle'
    };
  }
}

// Export all constants and classes
export {
  AGENT_TYPES,
  AGENT_STATUS,
  AGENT_PRIORITY,
  AGENT_CAPABILITIES,
  AgentConfig,
  AgentResult,
  AgentContext,
  AgentChain,
  AgentMetrics
};
