import React, { useCallback, useState } from 'react';
import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  Icon,
  useColorModeValue,
  Alert,
  AlertIcon,
  Progress,
  List,
  ListItem,
  ListIcon,
} from '@chakra-ui/react';
import { useDropzone } from 'react-dropzone';
import { MdUploadFile, MdCheckCircle, MdError } from 'react-icons/md';

const FileUpload = ({ onFileUpload, isLoading }) => {
  const [uploadStatus, setUploadStatus] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);

  const bgColor = useColorModeValue('gray.50', 'navy.800');
  const borderColor = useColorModeValue('gray.300', 'gray.600');
  const textColor = useColorModeValue('gray.600', 'gray.400');

  const onDrop = useCallback(async (acceptedFiles) => {
    const file = acceptedFiles[0];
    if (file) {
      setUploadStatus('uploading');
      setUploadProgress(0);
      
      try {
        // Simulate upload progress
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => {
            if (prev >= 90) {
              clearInterval(progressInterval);
              return 90;
            }
            return prev + 10;
          });
        }, 200);

        await onFileUpload(file);
        
        clearInterval(progressInterval);
        setUploadProgress(100);
        setUploadStatus('success');
      } catch (error) {
        setUploadStatus('error');
        console.error('Upload error:', error);
      }
    }
  }, [onFileUpload]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/json': ['.json'],
    },
    maxFiles: 1,
    disabled: isLoading
  });

  return (
    <Box>
      <Box
        {...getRootProps()}
        p="40px"
        border="2px dashed"
        borderColor={isDragActive ? 'blue.400' : borderColor}
        borderRadius="md"
        bg={isDragActive ? 'blue.50' : bgColor}
        cursor={isLoading ? 'not-allowed' : 'pointer'}
        transition="all 0.2s"
        _hover={{
          borderColor: isLoading ? borderColor : 'blue.400',
          bg: isLoading ? bgColor : 'blue.50'
        }}
        opacity={isLoading ? 0.6 : 1}
      >
        <input {...getInputProps()} />
        <VStack spacing="4">
          <Icon as={MdUploadFile} boxSize="12" color="blue.400" />
          <VStack spacing="1">
            <Text fontWeight="bold" fontSize="lg">
              {isDragActive ? 'Drop your file here' : 'Upload Business Data'}
            </Text>
            <Text color={textColor} fontSize="sm">
              Drag & drop or click to select CSV, Excel, or JSON files
            </Text>
          </VStack>
          
          <Text fontSize="xs" color={textColor}>
            Max file size: 50MB
          </Text>
        </VStack>
      </Box>

      {/* Upload Progress */}
      {uploadStatus === 'uploading' && (
        <Box mt="4">
          <Text mb="2" fontSize="sm">Uploading file...</Text>
          <Progress value={uploadProgress} colorScheme="blue" size="sm" />
        </Box>
      )}

      {/* Upload Status */}
      {uploadStatus === 'success' && (
        <Alert status="success" mt="4" borderRadius="md">
          <AlertIcon />
          File uploaded successfully! AI agents are now analyzing your data.
        </Alert>
      )}

      {uploadStatus === 'error' && (
        <Alert status="error" mt="4" borderRadius="md">
          <AlertIcon />
          Upload failed. Please try again.
        </Alert>
      )}

      {/* Supported File Types */}
      <Box mt="6" p="4" bg={bgColor} borderRadius="md">
        <Text fontWeight="bold" mb="2" fontSize="sm">
          Supported File Types:
        </Text>
        <List spacing="1" fontSize="xs">
          <ListItem>
            <ListIcon as={MdCheckCircle} color="green.500" />
            CSV (.csv) - Comma-separated values
          </ListItem>
          <ListItem>
            <ListIcon as={MdCheckCircle} color="green.500" />
            Excel (.xlsx, .xls) - Microsoft Excel files
          </ListItem>
          <ListItem>
            <ListIcon as={MdCheckCircle} color="green.500" />
            JSON (.json) - JavaScript Object Notation
          </ListItem>
        </List>
      </Box>
    </Box>
  );
};

export default FileUpload;
