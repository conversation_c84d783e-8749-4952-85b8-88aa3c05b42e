const winston = require('winston');

class ErrorHandler {
  constructor() {
    this.logger = winston.createLogger({
      level: 'error',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          )
        }),
        new winston.transports.File({ 
          filename: 'logs/error.log',
          maxsize: 5242880, // 5MB
          maxFiles: 5
        })
      ]
    });
  }

  // Global error handler middleware
  handle() {
    return (error, req, res, next) => {
      // Log the error
      this.logger.error('Unhandled error:', {
        error: error.message,
        stack: error.stack,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        body: req.body,
        params: req.params,
        query: req.query,
        timestamp: new Date().toISOString()
      });

      // Determine error type and response
      const errorResponse = this.formatError(error);
      
      res.status(errorResponse.status).json({
        success: false,
        error: {
          message: errorResponse.message,
          type: errorResponse.type,
          ...(process.env.NODE_ENV === 'development' && { 
            stack: error.stack,
            details: error 
          })
        },
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
      });
    };
  }

  // Format error based on type
  formatError(error) {
    // Validation errors
    if (error.name === 'ValidationError') {
      return {
        status: 400,
        type: 'VALIDATION_ERROR',
        message: this.extractValidationMessage(error)
      };
    }

    // Mongoose errors
    if (error.name === 'MongoError' || error.name === 'MongooseError') {
      return {
        status: 500,
        type: 'DATABASE_ERROR',
        message: 'Database operation failed'
      };
    }

    // JWT errors
    if (error.name === 'JsonWebTokenError') {
      return {
        status: 401,
        type: 'AUTHENTICATION_ERROR',
        message: 'Invalid token'
      };
    }

    if (error.name === 'TokenExpiredError') {
      return {
        status: 401,
        type: 'AUTHENTICATION_ERROR',
        message: 'Token expired'
      };
    }

    // Multer errors (file upload)
    if (error.code === 'LIMIT_FILE_SIZE') {
      return {
        status: 400,
        type: 'FILE_TOO_LARGE',
        message: 'File size exceeds the allowed limit'
      };
    }

    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return {
        status: 400,
        type: 'INVALID_FILE',
        message: 'Unexpected file field'
      };
    }

    // Custom application errors
    if (error.type) {
      return {
        status: error.status || 500,
        type: error.type,
        message: error.message
      };
    }

    // Network/API errors
    if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      return {
        status: 503,
        type: 'SERVICE_UNAVAILABLE',
        message: 'External service unavailable'
      };
    }

    // Default error
    return {
      status: error.status || 500,
      type: 'INTERNAL_SERVER_ERROR',
      message: process.env.NODE_ENV === 'production' 
        ? 'An unexpected error occurred' 
        : error.message
    };
  }

  // Extract validation error messages
  extractValidationMessage(error) {
    if (error.details && Array.isArray(error.details)) {
      return error.details.map(detail => detail.message).join(', ');
    }
    
    if (error.errors) {
      return Object.values(error.errors).map(err => err.message).join(', ');
    }
    
    return error.message || 'Validation failed';
  }

  // Handle 404 errors
  notFound() {
    return (req, res) => {
      this.logger.warn('Route not found:', {
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      res.status(404).json({
        success: false,
        error: {
          message: `Route ${req.method} ${req.originalUrl} not found`,
          type: 'NOT_FOUND'
        },
        timestamp: new Date().toISOString()
      });
    };
  }

  // Async error wrapper
  asyncHandler(fn) {
    return (req, res, next) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  // Custom error classes
  static createError(message, status = 500, type = 'CUSTOM_ERROR') {
    const error = new Error(message);
    error.status = status;
    error.type = type;
    return error;
  }

  static validationError(message) {
    return this.createError(message, 400, 'VALIDATION_ERROR');
  }

  static notFoundError(resource = 'Resource') {
    return this.createError(`${resource} not found`, 404, 'NOT_FOUND');
  }

  static unauthorizedError(message = 'Unauthorized access') {
    return this.createError(message, 401, 'UNAUTHORIZED');
  }

  static forbiddenError(message = 'Access forbidden') {
    return this.createError(message, 403, 'FORBIDDEN');
  }

  static conflictError(message = 'Resource conflict') {
    return this.createError(message, 409, 'CONFLICT');
  }

  static tooManyRequestsError(message = 'Too many requests') {
    return this.createError(message, 429, 'TOO_MANY_REQUESTS');
  }

  static serviceUnavailableError(message = 'Service temporarily unavailable') {
    return this.createError(message, 503, 'SERVICE_UNAVAILABLE');
  }

  // Process uncaught exceptions and rejections
  setupProcessHandlers() {
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught Exception:', {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });
      
      // Graceful shutdown
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      this.logger.error('Unhandled Rejection:', {
        reason: reason,
        promise: promise,
        timestamp: new Date().toISOString()
      });
      
      // Don't exit for unhandled rejections, just log them
    });

    process.on('SIGTERM', () => {
      this.logger.info('SIGTERM received, shutting down gracefully');
      process.exit(0);
    });

    process.on('SIGINT', () => {
      this.logger.info('SIGINT received, shutting down gracefully');
      process.exit(0);
    });
  }
}

// Export both class and instance
const errorHandler = new ErrorHandler();

module.exports = {
  ErrorHandler,
  errorHandler,
  asyncHandler: errorHandler.asyncHandler.bind(errorHandler),
  createError: ErrorHandler.createError,
  validationError: ErrorHandler.validationError,
  notFoundError: ErrorHandler.notFoundError,
  unauthorizedError: ErrorHandler.unauthorizedError,
  forbiddenError: ErrorHandler.forbiddenError,
  conflictError: ErrorHandler.conflictError,
  tooManyRequestsError: ErrorHandler.tooManyRequestsError,
  serviceUnavailableError: ErrorHandler.serviceUnavailableError
};
