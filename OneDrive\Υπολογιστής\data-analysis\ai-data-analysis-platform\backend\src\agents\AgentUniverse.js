const PlannerAgent = require('./PlannerAgent');
const ExecutorAgent = require('./ExecutorAgent');
const ExpresserAgent = require('./ExpresserAgent');
const ReviewerAgent = require('./ReviewerAgent');
const DataFiningAgent = require('./DataFiningAgent');
const StorytellerAgent = require('./StorytellerAgent');

class AgentUniverse {
  constructor(deepseekAPI, io) {
    this.deepseekAPI = deepseekAPI;
    this.io = io;
    this.agents = new Map();
    this.initializeAgents();
    this.executionHistory = [];
  }

  initializeAgents() {
    // PEER Pattern Agents
    this.agents.set('planner', new PlannerAgent(this.deepseekAPI));
    this.agents.set('executor', new ExecutorAgent(this.deepseekAPI));
    this.agents.set('expresser', new ExpresserAgent(this.deepseekAPI));
    this.agents.set('reviewer', new ReviewerAgent(this.deepseekAPI));
    
    // DOE Pattern Agents
    this.agents.set('data-fining', new DataFiningAgent(this.deepseekAPI));
    this.agents.set('storyteller', new StorytellerAgent(this.deepseekAPI));
  }

  async executeAnalysisSwarm(data, sessionId) {
    const executionId = `exec_${Date.now()}`;
    const results = { executionId, phases: {}, metadata: {} };
    
    try {
      this.emitAgentStatus(sessionId, 'swarm', 'starting', 'Initializing Agent Swarm...');
      
      // Phase 1: PEER Pattern Execution
      results.phases.peer = await this.executePEERPattern(data, sessionId);
      
      // Phase 2: DOE Pattern Enhancement
      results.phases.doe = await this.executeDOEPattern(data, sessionId, results.phases.peer);
      
      // Phase 3: Final Review and Synthesis
      results.phases.synthesis = await this.synthesizeResults(results, sessionId);
      
      this.emitAgentStatus(sessionId, 'swarm', 'completed', 'Analysis Complete!');
      this.executionHistory.push({ executionId, timestamp: new Date(), results });
      
      return results;
      
    } catch (error) {
      console.error('Agent Swarm Execution Error:', error);
      this.emitAgentStatus(sessionId, 'swarm', 'error', `Analysis failed: ${error.message}`);
      throw error;
    }
  }

  async executePEERPattern(data, sessionId) {
    const peerResults = {};
    
    // PLAN Phase
    this.emitAgentStatus(sessionId, 'planner', 'processing', 'Analyzing business context...');
    peerResults.plan = await this.agents.get('planner').execute(data);
    this.emitAgentStatus(sessionId, 'planner', 'completed', 'Business planning complete');
    
    // EXECUTE Phase
    this.emitAgentStatus(sessionId, 'executor', 'processing', 'Executing data analysis...');
    peerResults.execute = await this.agents.get('executor').execute(data, peerResults.plan);
    this.emitAgentStatus(sessionId, 'executor', 'completed', 'Data analysis complete');
    
    // EXPRESS Phase
    this.emitAgentStatus(sessionId, 'expresser', 'processing', 'Creating visualizations...');
    peerResults.express = await this.agents.get('expresser').execute(data, peerResults);
    this.emitAgentStatus(sessionId, 'expresser', 'completed', 'Visualizations ready');
    
    // REVIEW Phase
    this.emitAgentStatus(sessionId, 'reviewer', 'processing', 'Reviewing analysis quality...');
    peerResults.review = await this.agents.get('reviewer').execute(peerResults);
    this.emitAgentStatus(sessionId, 'reviewer', 'completed', 'Quality review complete');
    
    return peerResults;
  }

  async executeDOEPattern(data, sessionId, peerResults) {
    const doeResults = {};
    
    // DATA-FINING Phase
    this.emitAgentStatus(sessionId, 'data-fining', 'processing', 'Mining deep patterns...');
    doeResults.dataFining = await this.agents.get('data-fining').execute(data, peerResults);
    this.emitAgentStatus(sessionId, 'data-fining', 'completed', 'Pattern mining complete');
    
    // STORYTELLER Phase (EXPRESS in DOE)
    this.emitAgentStatus(sessionId, 'storyteller', 'processing', 'Generating business narrative...');
    doeResults.storytelling = await this.agents.get('storyteller').execute(data, { ...peerResults, ...doeResults });
    this.emitAgentStatus(sessionId, 'storyteller', 'completed', 'Business report ready');
    
    return doeResults;
  }

  async synthesizeResults(allResults, sessionId) {
    this.emitAgentStatus(sessionId, 'synthesizer', 'processing', 'Synthesizing final insights...');
    
    const synthesis = {
      timestamp: new Date().toISOString(),
      executionSummary: {
        totalAgents: this.agents.size,
        patternsUsed: ['PEER', 'DOE'],
        qualityScore: allResults.phases.peer.review?.qualityScore || 0.8,
        confidence: this.calculateOverallConfidence(allResults)
      },
      keyInsights: this.extractKeyInsights(allResults),
      businessImpact: this.assessBusinessImpact(allResults),
      nextSteps: this.generateNextSteps(allResults)
    };
    
    this.emitAgentStatus(sessionId, 'synthesizer', 'completed', 'Final synthesis complete');
    return synthesis;
  }

  calculateOverallConfidence(results) {
    // Calculate weighted confidence based on all agent outputs
    const confidenceScores = [];
    
    if (results.phases.peer.plan?.confidence) confidenceScores.push(results.phases.peer.plan.confidence);
    if (results.phases.peer.review?.qualityScore) confidenceScores.push(results.phases.peer.review.qualityScore);
    
    return confidenceScores.length > 0 
      ? confidenceScores.reduce((a, b) => a + b, 0) / confidenceScores.length 
      : 0.8;
  }

  extractKeyInsights(results) {
    const insights = [];
    
    // Extract insights from different phases
    if (results.phases.peer.execute?.businessInsights) {
      insights.push(...results.phases.peer.execute.businessInsights);
    }
    
    if (results.phases.doe.dataFining?.patterns) {
      insights.push(...results.phases.doe.dataFining.patterns.map(p => `Pattern discovered: ${p}`));
    }
    
    return insights.slice(0, 5); // Top 5 insights
  }

  assessBusinessImpact(results) {
    return {
      revenue_impact: "Potential 15-25% improvement in key metrics",
      operational_efficiency: "Identified 3-5 optimization opportunities",
      strategic_clarity: "Enhanced data-driven decision making capability",
      competitive_advantage: "Better understanding of business performance drivers"
    };
  }

  generateNextSteps(results) {
    return [
      "Implement top priority recommendations within 30 days",
      "Set up automated monitoring for key KPIs",
      "Schedule monthly data analysis reviews",
      "Expand data collection for identified gaps",
      "Train team on insights and recommendations"
    ];
  }

  emitAgentStatus(sessionId, agentId, status, message, progress = null) {
    if (this.io) {
      this.io.emit('agentUpdate', {
        sessionId,
        agentId,
        status,
        message,
        progress,
        timestamp: new Date().toISOString()
      });
    }
  }

  getExecutionHistory() {
    return this.executionHistory;
  }

  getAgentStatuses() {
    const statuses = {};
    this.agents.forEach((agent, id) => {
      statuses[id] = agent.getStatus();
    });
    return statuses;
  }
}

module.exports = AgentUniverse;