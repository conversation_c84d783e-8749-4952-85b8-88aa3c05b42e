{"version": 3, "file": "static/css/main.ffc5d11d.css", "mappings": "AASA,KAEE,WACF,CAEA,KAKE,kDAA6D,CAC7D,aAAc,CALd,yIACgF,CAOhF,iBACF,CAEA,WAJE,gBAQF,CAJA,MAEE,YAAa,CACb,qBACF,CAGA,kBAIE,aAAc,CAHd,eAAgB,CAChB,eAAgB,CAChB,kBAEF,CAEA,GACE,gBAAiB,CACjB,eACF,CAEA,GACE,cAAe,CACf,eACF,CAEA,GACE,gBACF,CAEA,GACE,iBACF,CAEA,GACE,kBACF,CAEA,GACE,cACF,CAEA,EAEE,aAAc,CADd,iBAEF,CAGA,EACE,aAAc,CACd,oBAAqB,CACrB,yBACF,CAEA,QACE,aAAc,CACd,yBACF,CAGA,OAGE,WAAY,CACZ,iBAAkB,CAFlB,cAAe,CADf,mBAAoB,CAKpB,cAAe,CACf,eAAgB,CAEhB,YAAa,CAJb,iBAAkB,CAGlB,uBAEF,CAEA,qBACE,yBAA0B,CAC1B,kBACF,CAEA,aACE,kDAA6D,CAE7D,wCACF,CAEA,kCACE,kDAA6D,CAC7D,yCAA8C,CAC9C,0BACF,CAEA,eACE,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,oCACE,kBAAmB,CAEnB,oBAAqB,CADrB,aAEF,CAEA,YACE,kDAEF,CAEA,iCACE,kDAA6D,CAC7D,0BACF,CAEA,gBAEE,kBAAmB,CADnB,UAAY,CAEZ,wBACF,CAGA,sBAME,eAAiB,CAFjB,wBAAyB,CACzB,iBAAkB,CAJlB,mBAAoB,CACpB,cAAe,CAMf,YAAa,CALb,iBAAkB,CAIlB,uBAAyB,CAEzB,UACF,CAEA,wCACE,oBAAqB,CACrB,wCACF,CAEA,MAIE,aAAc,CAHd,aAAc,CACd,eAAgB,CAChB,iBAEF,CAGA,MACE,eAAiB,CACjB,kBAAmB,CACnB,iEAA2E,CAC3E,eAAgB,CAChB,uBACF,CAEA,YACE,uEAAiF,CACjF,0BACF,CAEA,aAEE,+BAAgC,CADhC,sBAEF,CAEA,WACE,YACF,CAEA,aAGE,kBAAmB,CADnB,4BAA6B,CAD7B,sBAGF,CAGA,WAEE,aAAc,CADd,gBAAiB,CAEjB,cACF,CAEA,MACE,YACF,CAEA,UACE,qBACF,CAEA,cACE,kBACF,CAEA,gBACE,sBACF,CAEA,iBACE,6BACF,CAEA,OACE,QACF,CAEA,OACE,UACF,CAEA,aACE,iBACF,CAEA,SACE,iBACF,CAEA,SACE,kBACF,CAEA,SACE,iBACF,CAEA,aACE,eACF,CAEA,eACE,eACF,CAEA,WACE,eACF,CAEA,YACE,UACF,CAEA,YACE,WACF,CAiBA,wBACE,GAAO,0BAA6B,CACpC,GAAK,uBAA0B,CACjC,CAEA,cACE,iCACF,CAEA,eACE,mDACF,CAEA,gBACE,6BACF,CAEA,sBACE,mCACF,CAGA,yBACE,WACE,cACF,CAEA,GACE,cACF,CAEA,GACE,iBACF,CAEA,WACE,YACF,CAEA,OAEE,cAAe,CADf,iBAEF,CACF,CAEA,yBACE,GACE,iBACF,CAEA,GACE,gBACF,CAEA,WACE,YACF,CAEA,aACE,sBACF,CAEA,aACE,sBACF,CACF,CAGA,mCACE,KACE,kDAA6D,CAC7D,aACF,CAEA,MACE,kBAAmB,CACnB,wBACF,CAEA,sBAGE,aACF,CAEA,mCALE,kBAAmB,CACnB,oBAOF,CACF,CAGA,aACE,EACE,yBAA4B,CAE5B,yBAA2B,CAD3B,oBAEF,CAEA,UACE,sBACF,CACF,CC5XA,EACE,qBAAsB,CACtB,QAAS,CACT,SACF,CAEA,KACE,sBACF,CAEA,KAIE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CACzB,aAAc,CANd,mIAEY,CAKZ,eACF,CAEA,KACE,uEAEF,CAGA,oBAEE,UAAW,CADX,SAEF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,SACE,gCACF,CAEA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,UACE,8BACF,CAEA,mBACE,GACE,2BACF,CACA,GACE,uBACF,CACF,CAEA,OACE,mDACF,CAEA,iBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAEA,QACE,4BACF,CAEA,kBACE,kBACE,uDAAmE,CACnE,uBACF,CACA,QACE,yDAAmE,CACnE,gCACF,CACA,IACE,yDAAmE,CACnE,gCACF,CACA,IACE,+BACF,CACF,CAGA,SAME,iCAAkC,CAJlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAG7B,WAAY,CADZ,UAGF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,aACE,eAAiB,CACjB,kBAAmB,CACnB,iEAA2E,CAC3E,8BACF,CAEA,mBACE,uEAAiF,CACjF,0BACF,CAEA,oBAEE,+BAAgC,CADhC,sBAEF,CAEA,kBACE,YACF,CAGA,cAEE,kBAAmB,CAEnB,kBAAmB,CAHnB,mBAAoB,CAIpB,cAAe,CACf,eAAgB,CAEhB,oBAAsB,CALtB,gBAAiB,CAIjB,wBAEF,CAEA,sBACE,wBAAyB,CACzB,aACF,CAEA,sBACE,wBAAyB,CACzB,aACF,CAEA,wBACE,wBAAyB,CACzB,aACF,CAEA,qBACE,wBAAyB,CACzB,aACF,CAEA,wBACE,wBAAyB,CACzB,aACF,CAGA,cAGE,wBAAyB,CACzB,iBAAkB,CAFlB,UAAW,CAGX,eAAgB,CAJhB,UAKF,CAEA,mBAEE,iDAA4D,CAE5D,iBAAkB,CAHlB,WAAY,CAEZ,yBAEF,CAGA,gBAME,wBAAyB,CALzB,yBAA0B,CAC1B,iBAAkB,CAClB,iBAAkB,CAClB,iBAAkB,CAClB,uBAEF,CAEA,uBAEE,wBAAyB,CADzB,oBAEF,CAEA,sBAEE,wBAAyB,CADzB,oBAEF,CAGA,iBAKE,eAAiB,CACjB,iBAAkB,CAClB,mCAAwC,CAJxC,YAAa,CACb,YAAa,CAHb,iBAAkB,CAClB,UAMF,CAEA,eACE,yBAA8B,CAG9B,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CAFf,gBAAiB,CAGjB,mBACF,CAGA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,eACF,CAEA,YACE,eAAiB,CAKjB,6BAA8B,CAJ9B,kBAAmB,CAEnB,mCAAwC,CADxC,YAAa,CAEb,uBAEF,CAEA,kBACE,oCAAyC,CACzC,0BACF,CAEA,qBACE,yBACF,CAEA,sBACE,yBACF,CAEA,qBACE,yBACF,CAEA,uBACE,yBACF,CAEA,wBACE,yBACF,CAGA,mBAEE,iBAAkB,CADlB,iBAEF,CAEA,0BAOE,kBAAmB,CAFnB,QAAS,CAJT,UAAW,CAEX,SAAU,CADV,iBAAkB,CAElB,KAAM,CAEN,SAEF,CAEA,wBAEE,kBAAmB,CADnB,iBAEF,CAEA,+BAQE,kBAAmB,CACnB,qBAAuB,CAFvB,iBAAkB,CAGlB,4BAA6B,CAT7B,UAAW,CAKX,WAAY,CAHZ,UAAW,CADX,iBAAkB,CAElB,OAAQ,CACR,UAMF,CAEA,yCACE,kBAAmB,CACnB,4BACF,CAEA,sCACE,kBAAmB,CACnB,4BACF,CAGA,gBAIE,eAAiB,CACjB,kBAAmB,CAEnB,mCAAwC,CANxC,YAAa,CACb,qBAAsB,CACtB,YAAa,CAGb,eAEF,CAEA,eAIE,YAAa,CAHb,QAAO,CAIP,qBAAsB,CACtB,QAAS,CAJT,eAAgB,CAChB,YAIF,CAEA,cAIE,oBAAqB,CADrB,kBAAmB,CAFnB,aAAc,CACd,iBAGF,CAEA,mBACE,mBAAoB,CACpB,kBAAmB,CACnB,UACF,CAEA,wBACE,qBAAsB,CACtB,kBAAmB,CAEnB,wBAAyB,CADzB,aAEF,CAEA,sBAGE,kBAAmB,CADnB,4BAA6B,CAD7B,YAGF,CAGA,YAGE,eAAiB,CADjB,wBAAyB,CAEzB,iBAAkB,CAElB,mCAAwC,CADxC,eAAgB,CAJhB,UAMF,CAEA,eACE,kBAAmB,CAInB,aAAc,CADd,eAAgB,CADhB,eAIF,CAEA,8BAHE,+BAAgC,CAJhC,iBAUF,CAEA,qBACE,kBACF,CAGA,YACE,kBACF,CAEA,YAIE,aAAc,CAHd,aAAc,CAEd,eAAgB,CADhB,iBAGF,CAEA,YAGE,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CAHf,iBAAkB,CAIlB,gCAAkC,CALlC,UAMF,CAEA,kBAEE,oBAAqB,CACrB,wCAA6C,CAF7C,YAGF,CAEA,kBACE,oBACF,CAEA,YACE,aAGF,CAEA,uBAJE,cAAe,CACf,cAOF,CAJA,WACE,aAGF,CAGA,KAEE,kBAAmB,CAGnB,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CARf,mBAAoB,CAMpB,cAAe,CACf,eAAgB,CAIhB,OAAQ,CATR,sBAAuB,CACvB,iBAAkB,CAOlB,oBAAqB,CADrB,uBAGF,CAEA,cAEE,kBAAmB,CADnB,UAEF,CAEA,aACE,kBAAmB,CACnB,UACF,CAEA,kCACE,kBACF,CAEA,eACE,kBAAmB,CACnB,aACF,CAEA,oCACE,kBACF,CAEA,aACE,kBAAmB,CACnB,UACF,CAEA,kCACE,kBACF,CAEA,YACE,kBAAmB,CACnB,UACF,CAEA,iCACE,kBACF,CAEA,aACE,sBAAuB,CACvB,wBAAyB,CACzB,aACF,CAEA,kCACE,kBAAmB,CACnB,oBACF,CAEA,QAEE,cAAe,CADf,gBAEF,CAEA,QAEE,cAAe,CADf,iBAEF,CAGA,yBACE,YACE,yBACF,CAEA,iBACE,YAAa,CACb,YACF,CAEA,cACE,aACF,CAEA,YACE,cACF,CAEA,8BAEE,gBACF,CACF,CAEA,yBACE,kBACE,YACF,CAEA,oBACE,sBACF,CAEA,gBACE,YACF,CAEA,KAEE,cAAe,CADf,gBAEF,CACF,CAGA,mCACE,KACE,wBAAyB,CACzB,aACF,CAEA,aACE,kBAAmB,CACnB,wBACF,CAEA,oBACE,2BACF,CAEA,gBACE,wBAAyB,CACzB,oBACF,CAEA,6CAEE,wBAAyB,CACzB,oBACF,CAEA,YACE,kBACF,CAEA,eACE,kBAAmB,CAEnB,2BAA4B,CAD5B,aAEF,CAEA,eACE,2BACF,CAEA,qBACE,kBACF,CAEA,YACE,kBAAmB,CACnB,oBAAqB,CACrB,aACF,CAEA,kBACE,oBACF,CAEA,gBACE,kBACF,CAEA,wBACE,kBAAmB,CAEnB,oBAAqB,CADrB,aAEF,CAEA,sBACE,kBAAmB,CACnB,wBACF,CACF,CAGA,aACE,2CAGE,sBACF,CAEA,aAEE,wBAAyB,CADzB,eAEF,CAEA,iBACE,kBAAmB,CAAnB,uBACF,CAEA,KACE,eAAiB,CACjB,UACF,CACF", "sources": ["styles/globals.css", "styles/global.css"], "sourcesContent": ["/* Global Styles for AI Data Analysis Platform */\r\n\r\n/* Reset and base styles */\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nhtml {\r\n  scroll-behavior: smooth;\r\n  height: 100%;\r\n}\r\n\r\nbody {\r\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n  color: #1e293b;\r\n  line-height: 1.6;\r\n  min-height: 100vh;\r\n  overflow-x: hidden;\r\n}\r\n\r\n#root {\r\n  min-height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* Typography */\r\nh1, h2, h3, h4, h5, h6 {\r\n  font-weight: 600;\r\n  line-height: 1.2;\r\n  margin-bottom: 0.5em;\r\n  color: #0f172a;\r\n}\r\n\r\nh1 {\r\n  font-size: 2.5rem;\r\n  font-weight: 700;\r\n}\r\n\r\nh2 {\r\n  font-size: 2rem;\r\n  font-weight: 600;\r\n}\r\n\r\nh3 {\r\n  font-size: 1.5rem;\r\n}\r\n\r\nh4 {\r\n  font-size: 1.25rem;\r\n}\r\n\r\nh5 {\r\n  font-size: 1.125rem;\r\n}\r\n\r\nh6 {\r\n  font-size: 1rem;\r\n}\r\n\r\np {\r\n  margin-bottom: 1em;\r\n  color: #475569;\r\n}\r\n\r\n/* Links */\r\na {\r\n  color: #3b82f6;\r\n  text-decoration: none;\r\n  transition: color 0.2s ease;\r\n}\r\n\r\na:hover {\r\n  color: #1d4ed8;\r\n  text-decoration: underline;\r\n}\r\n\r\n/* Buttons */\r\nbutton {\r\n  font-family: inherit;\r\n  cursor: pointer;\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 12px 24px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  transition: all 0.2s ease;\r\n  outline: none;\r\n}\r\n\r\nbutton:focus-visible {\r\n  outline: 2px solid #3b82f6;\r\n  outline-offset: 2px;\r\n}\r\n\r\n.btn-primary {\r\n  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\r\n  color: white;\r\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.btn-primary:hover:not(:disabled) {\r\n  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);\r\n  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.btn-secondary {\r\n  background: #f1f5f9;\r\n  color: #475569;\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.btn-secondary:hover:not(:disabled) {\r\n  background: #e2e8f0;\r\n  color: #334155;\r\n  border-color: #cbd5e1;\r\n}\r\n\r\n.btn-danger {\r\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\r\n  color: white;\r\n}\r\n\r\n.btn-danger:hover:not(:disabled) {\r\n  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);\r\n  transform: translateY(-1px);\r\n}\r\n\r\nbutton:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  transform: none !important;\r\n}\r\n\r\n/* Form elements */\r\ninput, textarea, select {\r\n  font-family: inherit;\r\n  font-size: 14px;\r\n  padding: 12px 16px;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 8px;\r\n  background: white;\r\n  transition: all 0.2s ease;\r\n  outline: none;\r\n  width: 100%;\r\n}\r\n\r\ninput:focus, textarea:focus, select:focus {\r\n  border-color: #3b82f6;\r\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\r\n}\r\n\r\nlabel {\r\n  display: block;\r\n  font-weight: 500;\r\n  margin-bottom: 6px;\r\n  color: #374151;\r\n}\r\n\r\n/* Cards */\r\n.card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\r\n  overflow: hidden;\r\n  transition: all 0.2s ease;\r\n}\r\n\r\n.card:hover {\r\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.card-header {\r\n  padding: 20px 24px 16px;\r\n  border-bottom: 1px solid #f1f5f9;\r\n}\r\n\r\n.card-body {\r\n  padding: 24px;\r\n}\r\n\r\n.card-footer {\r\n  padding: 16px 24px 20px;\r\n  border-top: 1px solid #f1f5f9;\r\n  background: #f8fafc;\r\n}\r\n\r\n/* Utilities */\r\n.container {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 20px;\r\n}\r\n\r\n.flex {\r\n  display: flex;\r\n}\r\n\r\n.flex-col {\r\n  flex-direction: column;\r\n}\r\n\r\n.items-center {\r\n  align-items: center;\r\n}\r\n\r\n.justify-center {\r\n  justify-content: center;\r\n}\r\n\r\n.justify-between {\r\n  justify-content: space-between;\r\n}\r\n\r\n.gap-4 {\r\n  gap: 1rem;\r\n}\r\n\r\n.gap-6 {\r\n  gap: 1.5rem;\r\n}\r\n\r\n.text-center {\r\n  text-align: center;\r\n}\r\n\r\n.text-sm {\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.text-lg {\r\n  font-size: 1.125rem;\r\n}\r\n\r\n.text-xl {\r\n  font-size: 1.25rem;\r\n}\r\n\r\n.font-medium {\r\n  font-weight: 500;\r\n}\r\n\r\n.font-semibold {\r\n  font-weight: 600;\r\n}\r\n\r\n.font-bold {\r\n  font-weight: 700;\r\n}\r\n\r\n.opacity-50 {\r\n  opacity: 0.5;\r\n}\r\n\r\n.opacity-75 {\r\n  opacity: 0.75;\r\n}\r\n\r\n/* Loading animations */\r\n@keyframes spin {\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(10px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n@keyframes slideInRight {\r\n  from { transform: translateX(100%); }\r\n  to { transform: translateX(0); }\r\n}\r\n\r\n.animate-spin {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n.animate-pulse {\r\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\r\n}\r\n\r\n.animate-fadeIn {\r\n  animation: fadeIn 0.5s ease-out;\r\n}\r\n\r\n.animate-slideInRight {\r\n  animation: slideInRight 0.3s ease-out;\r\n}\r\n\r\n/* Responsive design */\r\n@media (max-width: 768px) {\r\n  .container {\r\n    padding: 0 16px;\r\n  }\r\n  \r\n  h1 {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  h2 {\r\n    font-size: 1.75rem;\r\n  }\r\n  \r\n  .card-body {\r\n    padding: 20px;\r\n  }\r\n  \r\n  button {\r\n    padding: 10px 20px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  h1 {\r\n    font-size: 1.75rem;\r\n  }\r\n  \r\n  h2 {\r\n    font-size: 1.5rem;\r\n  }\r\n  \r\n  .card-body {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .card-header {\r\n    padding: 16px 20px 12px;\r\n  }\r\n  \r\n  .card-footer {\r\n    padding: 12px 20px 16px;\r\n  }\r\n}\r\n\r\n/* Dark mode support */\r\n@media (prefers-color-scheme: dark) {\r\n  body {\r\n    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);\r\n    color: #f1f5f9;\r\n  }\r\n  \r\n  .card {\r\n    background: #1e293b;\r\n    border: 1px solid #334155;\r\n  }\r\n  \r\n  input, textarea, select {\r\n    background: #334155;\r\n    border-color: #475569;\r\n    color: #f1f5f9;\r\n  }\r\n  \r\n  .card-footer {\r\n    background: #334155;\r\n    border-color: #475569;\r\n  }\r\n}\r\n\r\n/* Print styles */\r\n@media print {\r\n  * {\r\n    background: white !important;\r\n    color: black !important;\r\n    box-shadow: none !important;\r\n  }\r\n  \r\n  .no-print {\r\n    display: none !important;\r\n  }\r\n}\r\n", "/* Global styles for AI Data Analysis Platform */\r\n\r\n/* Reset and base styles */\r\n* {\r\n  box-sizing: border-box;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\nhtml {\r\n  scroll-behavior: smooth;\r\n}\r\n\r\nbody {\r\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',\r\n    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  background-color: #f7fafc;\r\n  color: #2d3748;\r\n  line-height: 1.6;\r\n}\r\n\r\ncode {\r\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\r\n    monospace;\r\n}\r\n\r\n/* Scrollbar styles */\r\n::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n  background: #f1f1f1;\r\n  border-radius: 4px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  background: #c1c1c1;\r\n  border-radius: 4px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: #a1a1a1;\r\n}\r\n\r\n/* Custom utility classes */\r\n.fade-in {\r\n  animation: fadeIn 0.5s ease-in-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.slide-in {\r\n  animation: slideIn 0.3s ease-out;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    transform: translateX(-100%);\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n.pulse {\r\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: .7;\r\n  }\r\n}\r\n\r\n.bounce {\r\n  animation: bounce 1s infinite;\r\n}\r\n\r\n@keyframes bounce {\r\n  0%, 20%, 53%, 80%, 100% {\r\n    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);\r\n    transform: translate3d(0,0,0);\r\n  }\r\n  40%, 43% {\r\n    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);\r\n    transform: translate3d(0, -30px, 0);\r\n  }\r\n  70% {\r\n    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);\r\n    transform: translate3d(0, -15px, 0);\r\n  }\r\n  90% {\r\n    transform: translate3d(0,-4px,0);\r\n  }\r\n}\r\n\r\n/* Loading spinner */\r\n.spinner {\r\n  border: 3px solid #f3f3f3;\r\n  border-top: 3px solid #3182ce;\r\n  border-radius: 50%;\r\n  width: 40px;\r\n  height: 40px;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Custom card styles */\r\n.custom-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\r\n  transition: all 0.2s ease-in-out;\r\n}\r\n\r\n.custom-card:hover {\r\n  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.custom-card-header {\r\n  padding: 20px 24px 16px;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.custom-card-body {\r\n  padding: 24px;\r\n}\r\n\r\n/* Status badges */\r\n.status-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  padding: 4px 12px;\r\n  border-radius: 12px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.05em;\r\n}\r\n\r\n.status-badge.pending {\r\n  background-color: #fef5e7;\r\n  color: #d69e2e;\r\n}\r\n\r\n.status-badge.running {\r\n  background-color: #ebf8ff;\r\n  color: #3182ce;\r\n}\r\n\r\n.status-badge.completed {\r\n  background-color: #f0fff4;\r\n  color: #38a169;\r\n}\r\n\r\n.status-badge.failed {\r\n  background-color: #fed7d7;\r\n  color: #e53e3e;\r\n}\r\n\r\n.status-badge.cancelled {\r\n  background-color: #edf2f7;\r\n  color: #718096;\r\n}\r\n\r\n/* Progress bars */\r\n.progress-bar {\r\n  width: 100%;\r\n  height: 8px;\r\n  background-color: #e2e8f0;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-bar-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3182ce 0%, #63b3ed 100%);\r\n  transition: width 0.3s ease;\r\n  border-radius: 4px;\r\n}\r\n\r\n/* File upload styles */\r\n.file-drop-zone {\r\n  border: 2px dashed #cbd5e0;\r\n  border-radius: 8px;\r\n  padding: 40px 20px;\r\n  text-align: center;\r\n  transition: all 0.2s ease;\r\n  background-color: #f7fafc;\r\n}\r\n\r\n.file-drop-zone.active {\r\n  border-color: #3182ce;\r\n  background-color: #ebf8ff;\r\n}\r\n\r\n.file-drop-zone:hover {\r\n  border-color: #4299e1;\r\n  background-color: #ebf8ff;\r\n}\r\n\r\n/* Data visualization styles */\r\n.chart-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 400px;\r\n  padding: 20px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.chart-tooltip {\r\n  background: rgba(0, 0, 0, 0.8);\r\n  color: white;\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n  pointer-events: none;\r\n}\r\n\r\n/* Agent dashboard styles */\r\n.agent-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n  gap: 24px;\r\n  margin-top: 24px;\r\n}\r\n\r\n.agent-card {\r\n  background: white;\r\n  border-radius: 12px;\r\n  padding: 24px;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.2s ease;\r\n  border-left: 4px solid #3182ce;\r\n}\r\n\r\n.agent-card:hover {\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.agent-card.executor {\r\n  border-left-color: #3182ce;\r\n}\r\n\r\n.agent-card.expresser {\r\n  border-left-color: #38a169;\r\n}\r\n\r\n.agent-card.reviewer {\r\n  border-left-color: #d69e2e;\r\n}\r\n\r\n.agent-card.datafining {\r\n  border-left-color: #805ad5;\r\n}\r\n\r\n.agent-card.storyteller {\r\n  border-left-color: #e53e3e;\r\n}\r\n\r\n/* Analysis results styles */\r\n.analysis-timeline {\r\n  position: relative;\r\n  padding-left: 30px;\r\n}\r\n\r\n.analysis-timeline::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: 15px;\r\n  top: 0;\r\n  bottom: 0;\r\n  width: 2px;\r\n  background: #e2e8f0;\r\n}\r\n\r\n.analysis-timeline-item {\r\n  position: relative;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.analysis-timeline-item::before {\r\n  content: '';\r\n  position: absolute;\r\n  left: -23px;\r\n  top: 8px;\r\n  width: 12px;\r\n  height: 12px;\r\n  border-radius: 50%;\r\n  background: #3182ce;\r\n  border: 3px solid white;\r\n  box-shadow: 0 0 0 2px #3182ce;\r\n}\r\n\r\n.analysis-timeline-item.completed::before {\r\n  background: #38a169;\r\n  box-shadow: 0 0 0 2px #38a169;\r\n}\r\n\r\n.analysis-timeline-item.failed::before {\r\n  background: #e53e3e;\r\n  box-shadow: 0 0 0 2px #e53e3e;\r\n}\r\n\r\n/* Chat interface styles */\r\n.chat-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 500px;\r\n  background: white;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.chat-messages {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.chat-message {\r\n  max-width: 70%;\r\n  padding: 12px 16px;\r\n  border-radius: 18px;\r\n  word-wrap: break-word;\r\n}\r\n\r\n.chat-message.user {\r\n  align-self: flex-end;\r\n  background: #3182ce;\r\n  color: white;\r\n}\r\n\r\n.chat-message.assistant {\r\n  align-self: flex-start;\r\n  background: #f7fafc;\r\n  color: #2d3748;\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.chat-input-container {\r\n  padding: 20px;\r\n  border-top: 1px solid #e2e8f0;\r\n  background: #f7fafc;\r\n}\r\n\r\n/* Table styles */\r\n.data-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  background: white;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.data-table th {\r\n  background: #f7fafc;\r\n  padding: 12px 16px;\r\n  text-align: left;\r\n  font-weight: 600;\r\n  color: #4a5568;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.data-table td {\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #e2e8f0;\r\n}\r\n\r\n.data-table tr:hover {\r\n  background: #f7fafc;\r\n}\r\n\r\n/* Form styles */\r\n.form-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-label {\r\n  display: block;\r\n  margin-bottom: 6px;\r\n  font-weight: 500;\r\n  color: #2d3748;\r\n}\r\n\r\n.form-input {\r\n  width: 100%;\r\n  padding: 10px 12px;\r\n  border: 1px solid #e2e8f0;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n  transition: border-color 0.2s ease;\r\n}\r\n\r\n.form-input:focus {\r\n  outline: none;\r\n  border-color: #3182ce;\r\n  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);\r\n}\r\n\r\n.form-input.error {\r\n  border-color: #e53e3e;\r\n}\r\n\r\n.form-error {\r\n  color: #e53e3e;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.form-help {\r\n  color: #718096;\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n}\r\n\r\n/* Button styles */\r\n.btn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 10px 20px;\r\n  border: none;\r\n  border-radius: 6px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.2s ease;\r\n  text-decoration: none;\r\n  gap: 8px;\r\n}\r\n\r\n.btn:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.btn-primary {\r\n  background: #3182ce;\r\n  color: white;\r\n}\r\n\r\n.btn-primary:hover:not(:disabled) {\r\n  background: #2c5aa0;\r\n}\r\n\r\n.btn-secondary {\r\n  background: #e2e8f0;\r\n  color: #4a5568;\r\n}\r\n\r\n.btn-secondary:hover:not(:disabled) {\r\n  background: #cbd5e0;\r\n}\r\n\r\n.btn-success {\r\n  background: #38a169;\r\n  color: white;\r\n}\r\n\r\n.btn-success:hover:not(:disabled) {\r\n  background: #2f855a;\r\n}\r\n\r\n.btn-danger {\r\n  background: #e53e3e;\r\n  color: white;\r\n}\r\n\r\n.btn-danger:hover:not(:disabled) {\r\n  background: #c53030;\r\n}\r\n\r\n.btn-outline {\r\n  background: transparent;\r\n  border: 1px solid #e2e8f0;\r\n  color: #4a5568;\r\n}\r\n\r\n.btn-outline:hover:not(:disabled) {\r\n  background: #f7fafc;\r\n  border-color: #cbd5e0;\r\n}\r\n\r\n.btn-sm {\r\n  padding: 6px 12px;\r\n  font-size: 12px;\r\n}\r\n\r\n.btn-lg {\r\n  padding: 14px 28px;\r\n  font-size: 16px;\r\n}\r\n\r\n/* Responsive design */\r\n@media (max-width: 768px) {\r\n  .agent-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .chart-container {\r\n    height: 300px;\r\n    padding: 10px;\r\n  }\r\n  \r\n  .chat-message {\r\n    max-width: 90%;\r\n  }\r\n  \r\n  .data-table {\r\n    font-size: 12px;\r\n  }\r\n  \r\n  .data-table th,\r\n  .data-table td {\r\n    padding: 8px 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .custom-card-body {\r\n    padding: 16px;\r\n  }\r\n  \r\n  .custom-card-header {\r\n    padding: 16px 16px 12px;\r\n  }\r\n  \r\n  .chat-container {\r\n    height: 400px;\r\n  }\r\n  \r\n  .btn {\r\n    padding: 8px 16px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* Dark mode support */\r\n@media (prefers-color-scheme: dark) {\r\n  body {\r\n    background-color: #1a202c;\r\n    color: #e2e8f0;\r\n  }\r\n  \r\n  .custom-card {\r\n    background: #2d3748;\r\n    border: 1px solid #4a5568;\r\n  }\r\n  \r\n  .custom-card-header {\r\n    border-bottom-color: #4a5568;\r\n  }\r\n  \r\n  .file-drop-zone {\r\n    background-color: #2d3748;\r\n    border-color: #4a5568;\r\n  }\r\n  \r\n  .file-drop-zone.active,\r\n  .file-drop-zone:hover {\r\n    background-color: #2c5aa0;\r\n    border-color: #3182ce;\r\n  }\r\n  \r\n  .data-table {\r\n    background: #2d3748;\r\n  }\r\n  \r\n  .data-table th {\r\n    background: #4a5568;\r\n    color: #e2e8f0;\r\n    border-bottom-color: #718096;\r\n  }\r\n  \r\n  .data-table td {\r\n    border-bottom-color: #4a5568;\r\n  }\r\n  \r\n  .data-table tr:hover {\r\n    background: #4a5568;\r\n  }\r\n  \r\n  .form-input {\r\n    background: #2d3748;\r\n    border-color: #4a5568;\r\n    color: #e2e8f0;\r\n  }\r\n  \r\n  .form-input:focus {\r\n    border-color: #3182ce;\r\n  }\r\n  \r\n  .chat-container {\r\n    background: #2d3748;\r\n  }\r\n  \r\n  .chat-message.assistant {\r\n    background: #4a5568;\r\n    color: #e2e8f0;\r\n    border-color: #718096;\r\n  }\r\n  \r\n  .chat-input-container {\r\n    background: #4a5568;\r\n    border-top-color: #718096;\r\n  }\r\n}\r\n\r\n/* Print styles */\r\n@media print {\r\n  .btn,\r\n  .chat-input-container,\r\n  .file-drop-zone {\r\n    display: none !important;\r\n  }\r\n  \r\n  .custom-card {\r\n    box-shadow: none;\r\n    border: 1px solid #e2e8f0;\r\n  }\r\n  \r\n  .chart-container {\r\n    break-inside: avoid;\r\n  }\r\n  \r\n  body {\r\n    background: white;\r\n    color: black;\r\n  }\r\n}\r\n"], "names": [], "sourceRoot": ""}