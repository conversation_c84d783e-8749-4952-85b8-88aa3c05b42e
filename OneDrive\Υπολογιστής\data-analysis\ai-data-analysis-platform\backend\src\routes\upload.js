const express = require('express');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const FileProcessor = require('../services/FileProcessor');
const winston = require('winston');

const router = express.Router();

// Initialize services
const fileProcessor = new FileProcessor();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console()
  ]
});

// Configure multer for file uploads
const storage = multer.memoryStorage();

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    fieldSize: 1024 * 1024 // 1MB field size limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'text/csv',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/plain',
      'application/json'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} is not supported`), false);
    }
  }
});

// Upload single file (simplified for testing)
router.post('/',
  upload.single('file'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'No file provided'
        });
      }

      logger.info('File upload initiated:', {
        fileName: req.file.originalname,
        fileSize: req.file.size,
        mimeType: req.file.mimetype
      });

      // Process the uploaded file
      const result = await fileProcessor.processFile(req.file);

      // CRITICAL FIX: Store uploaded files globally for agents to access
      global.uploadedFiles = global.uploadedFiles || {};
      global.uploadedFiles[result.fileId] = {
        filename: result.originalName,
        data: result.data,
        metadata: result.metadata,
        uploadedAt: result.uploadedAt,
        mimeType: result.mimetype
      };

      console.log(`✅ File stored globally: ${result.fileId} with ${result.data.length} records`);

      logger.info('File upload completed:', {
        fileId: result.fileId,
        processingTime: result.metadata.processingTime
      });

      // Debug: Log what we're about to send to frontend
      console.log('📤 Sending to frontend:', {
        dataLength: result.data.length,
        previewLength: result.data.slice(0, 5).length,
        sampleRecord: result.data[0]
      });

      res.status(201).json({
        success: true,
        message: 'File uploaded and processed successfully',
        data: {
          fileId: result.fileId,
          filename: result.filename,
          originalName: result.originalName,
          size: result.size,
          mimeType: result.mimetype,
          uploadedAt: result.uploadedAt,
          metadata: {
            rows: result.metadata.rows,
            columns: result.metadata.columns,
            dataTypes: result.metadata.dataTypes,
            processingTime: result.metadata.processingTime
          },
          data: result.data, // Return full dataset for analysis
          preview: result.data.slice(0, 5) // First 5 rows for preview
        }
      });
    } catch (error) {
      logger.error('File upload failed:', {
        error: error.message,
        fileName: req.file?.originalname
      });

      res.status(500).json({
        success: false,
        message: 'File upload failed',
        error: error.message
      });
    }
  }
);

// Get file information (simplified)
router.get('/:fileId', async (req, res) => {
  try {
    const { fileId } = req.params;
    
    // This would normally fetch from database, but for testing we'll return a simple response
    res.json({
      success: true,
      message: 'File info retrieved',
      data: {
        fileId,
        status: 'processed',
        message: 'File processing completed'
      }
    });
  } catch (error) {
    logger.error('Failed to get file info:', {
      error: error.message,
      fileId: req.params.fileId
    });

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve file information',
      error: error.message
    });
  }
});

// Get upload status (simplified)
router.get('/status/:fileId', async (req, res) => {
  try {
    const { fileId } = req.params;
    
    res.json({
      success: true,
      data: {
        fileId,
        status: 'completed',
        progress: 100,
        message: 'File processing completed successfully'
      }
    });
  } catch (error) {
    logger.error('Failed to get upload status:', {
      error: error.message,
      fileId: req.params.fileId
    });

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve upload status',
      error: error.message
    });
  }
});

module.exports = router;