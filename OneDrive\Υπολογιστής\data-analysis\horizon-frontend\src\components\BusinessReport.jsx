import React from 'react';
import {
  <PERSON>,
  VStack,
  HStack,
  Text,
  <PERSON>ing,
  Badge,
  Alert,
  AlertIcon,
  List,
  ListItem,
  ListIcon,
  Divider,
  Icon,
  useColorModeValue,
  Flex,
  SimpleGrid,
} from '@chakra-ui/react';

// Horizon UI Components
import Card from 'components/card/Card.js';
import IconBox from 'components/icons/IconBox.js';

import {
  MdArrowUpward,
  MdArrowDownward,
  MdWarning,
  MdLightbulb,
  MdFlag,
  MdInsights,
  MdTrendingUp,
  MdCheckCircle,
  MdAssignment,
  MdBusiness,
} from 'react-icons/md';

const BusinessReport = ({ agentResults }) => {
  const bgColor = useColorModeValue('white', 'navy.800');
  const textColor = useColorModeValue('secondaryGray.900', 'white');
  
  const hasCompletedAgents = Object.values(agentResults).some(
    state => state.status === 'completed'
  );

  // Extract real insights from agent results
  const extractRealInsights = () => {
    const insights = [];
    const recommendations = [];

    // Extract insights from executor agent
    if (agentResults.executor?.result?.businessInsights) {
      insights.push(...agentResults.executor.result.businessInsights);
    }

    // Extract insights from storyteller agent
    if (agentResults.storyteller?.result?.insights) {
      insights.push(...agentResults.storyteller.result.insights);
    }

    // Extract insights from datafining agent
    if (agentResults.datafining?.result?.patterns) {
      agentResults.datafining.result.patterns.forEach(pattern => {
        insights.push(`Pattern discovered: ${pattern}`);
      });
    }

    // Generate recommendations based on real data
    if (agentResults.executor?.result?.kpiCalculations) {
      const kpis = agentResults.executor.result.kpiCalculations;

      // Check for growth opportunities
      Object.entries(kpis.growth_rates || {}).forEach(([metric, rate]) => {
        if (rate > 0.1) {
          recommendations.push({
            type: 'opportunity',
            icon: MdTrendingUp,
            color: 'green',
            title: `${metric} Growth Opportunity`,
            description: `${metric} shows ${(rate * 100).toFixed(1)}% growth rate. Consider scaling this area.`,
          });
        } else if (rate < -0.05) {
          recommendations.push({
            type: 'warning',
            icon: MdWarning,
            color: 'orange',
            title: `${metric} Decline Alert`,
            description: `${metric} declined by ${Math.abs(rate * 100).toFixed(1)}%. Requires immediate attention.`,
          });
        }
      });
    }

    // Default recommendations if no real data
    if (recommendations.length === 0) {
      recommendations.push({
        type: 'insight',
        icon: MdInsights,
        color: 'blue',
        title: 'Data Analysis Complete',
        description: 'Your data has been processed successfully. Review the metrics above for insights.',
      });
    }

    return { insights, recommendations };
  };

  const { insights: realInsights, recommendations: realRecommendations } = extractRealInsights();

  if (!hasCompletedAgents) {
    return (
      <Alert status="info">
        <AlertIcon />
        Business intelligence report will be generated once AI agents complete their analysis.
      </Alert>
    );
  }

  return (
    <VStack spacing="6" align="stretch">
      {/* Enhanced Executive Summary */}
      <Card p="6">
        <Flex direction="column">
          <Flex align="center" mb="20px">
            <IconBox
              w='56px'
              h='56px'
              bg='linear-gradient(90deg, #4318FF 0%, #9F7AEA 100%)'
              icon={<Icon w='32px' h='32px' as={MdBusiness} color='white' />}
              me="20px"
            />
            <Box>
              <Text
                color={textColor}
                fontSize="xl"
                fontWeight="700"
                lineHeight="100%"
              >
                Executive Summary
              </Text>
              <Text
                color="secondaryGray.600"
                fontSize="sm"
                fontWeight="500"
              >
                AI-Powered Business Intelligence Report
              </Text>
            </Box>
          </Flex>

          <Card bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" p="6">
            <Text color="white" fontSize="sm" lineHeight="1.6" fontWeight="500">
              {realInsights.length > 0
                ? `Analysis of your business data reveals: ${realInsights[0] || 'Data processing completed successfully.'}`
                : 'Your business data has been analyzed by our AI agents. Review the detailed metrics and insights below for comprehensive business intelligence.'
              }
            </Text>
          </Card>
        </Flex>
      </Card>

      {/* Enhanced Key Findings */}
      <Card p="6">
        <Flex align="center" mb="20px">
          <IconBox
            w='56px'
            h='56px'
            bg='linear-gradient(90deg, #FFE066 0%, #FF6B6B 100%)'
            icon={<Icon w='32px' h='32px' as={MdInsights} color='white' />}
            me="20px"
          />
          <Box>
            <Text
              color={textColor}
              fontSize="xl"
              fontWeight="700"
              lineHeight="100%"
            >
              Key Findings
            </Text>
            <Text
              color="secondaryGray.600"
              fontSize="sm"
              fontWeight="500"
            >
              Critical insights from data analysis
            </Text>
          </Box>
        </Flex>

        <VStack spacing="3" align="stretch">
          {(realInsights.length > 0 ? realInsights : ['Data analysis completed successfully', 'All AI agents have processed your data', 'Review the metrics above for detailed insights']).map((finding, index) => (
            <Card key={index} bg="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" p="4">
              <Flex align="center">
                <Icon as={MdCheckCircle} color="white" w="20px" h="20px" me="15px" />
                <Text color="white" fontSize="sm" fontWeight="500">
                  {finding}
                </Text>
              </Flex>
            </Card>
          ))}
        </VStack>
      </Card>

      {/* Enhanced AI Recommendations */}
      <Card p="6">
        <Flex align="center" mb="20px">
          <IconBox
            w='56px'
            h='56px'
            bg='linear-gradient(90deg, #4ECDC4 0%, #44A08D 100%)'
            icon={<Icon w='32px' h='32px' as={MdTrendingUp} color='white' />}
            me="20px"
          />
          <Box>
            <Text
              color={textColor}
              fontSize="xl"
              fontWeight="700"
              lineHeight="100%"
            >
              AI-Powered Recommendations
            </Text>
            <Text
              color="secondaryGray.600"
              fontSize="sm"
              fontWeight="500"
            >
              Strategic insights for business growth
            </Text>
          </Box>
        </Flex>

        <SimpleGrid columns={{ base: 1, md: 2 }} spacing="4">
          {realRecommendations.map((rec, index) => {
            const gradients = [
              'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
              'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
            ];

            return (
              <Card key={index} bg={gradients[index % gradients.length]} p="5">
                <VStack align="start" spacing="3">
                  <Flex align="center" w="100%">
                    <Icon as={rec.icon} color="white" w="24px" h="24px" me="12px" />
                    <Box flex="1">
                      <Text color="white" fontSize="sm" fontWeight="700">
                        {rec.title}
                      </Text>
                      <Badge
                        bg="rgba(255,255,255,0.2)"
                        color="white"
                        size="sm"
                        borderRadius="full"
                        px="2"
                      >
                        {rec.type}
                      </Badge>
                    </Box>
                  </Flex>
                  <Text color="white" fontSize="xs" lineHeight="1.5">
                    {rec.description}
                  </Text>
                </VStack>
              </Card>
            );
          })}
        </SimpleGrid>
      </Card>

      {/* Enhanced Agent Contributions */}
      <Card p="6">
        <Flex align="center" mb="20px">
          <IconBox
            w='56px'
            h='56px'
            bg='linear-gradient(90deg, #FF6B6B 0%, #4ECDC4 100%)'
            icon={<Icon w='32px' h='32px' as={MdInsights} color='white' />}
            me="20px"
          />
          <Box>
            <Text
              color={textColor}
              fontSize="xl"
              fontWeight="700"
              lineHeight="100%"
            >
              AI Agent Contributions
            </Text>
            <Text
              color="secondaryGray.600"
              fontSize="sm"
              fontWeight="500"
            >
              Multi-agent analysis results
            </Text>
          </Box>
        </Flex>

        <SimpleGrid columns={{ base: 1, md: 2 }} spacing="4">
          {agentResults.executor?.status === 'completed' && (
            <Card bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" p="4">
              <VStack align="start" spacing="3">
                <Flex align="center">
                  <Icon as={MdInsights} color="white" w="20px" h="20px" me="10px" />
                  <Text color="white" fontSize="sm" fontWeight="700">
                    Executor Agent - Statistical Analysis
                  </Text>
                </Flex>
                <Text color="white" fontSize="xs">
                  {agentResults.executor?.result?.metadata?.recordsProcessed
                    ? `Processed ${agentResults.executor.result.metadata.recordsProcessed} records with ${(agentResults.executor.result.metadata.dataQuality * 100).toFixed(1)}% data quality.`
                    : 'Performed comprehensive statistical analysis and identified key performance metrics.'
                  }
                </Text>
              </VStack>
            </Card>
          )}

          {agentResults.datafining?.status === 'completed' && (
            <Card bg="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" p="4">
              <VStack align="start" spacing="3">
                <Flex align="center">
                  <Icon as={MdFlag} color="white" w="20px" h="20px" me="10px" />
                  <Text color="white" fontSize="sm" fontWeight="700">
                    Data Mining Agent - Pattern Discovery
                  </Text>
                </Flex>
                <Text color="white" fontSize="xs">
                  Discovered seasonal patterns and product performance correlations
                  that inform strategic recommendations.
                </Text>
              </VStack>
            </Card>
          )}

          {agentResults.storyteller?.status === 'completed' && (
            <Card bg="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" p="4">
              <VStack align="start" spacing="3">
                <Flex align="center">
                  <Icon as={MdBusiness} color="white" w="20px" h="20px" me="10px" />
                  <Text color="white" fontSize="sm" fontWeight="700">
                    Storyteller Agent - Business Narrative
                  </Text>
                </Flex>
                <Text color="white" fontSize="xs">
                  Generated actionable business insights and recommendations
                  based on comprehensive data analysis.
                </Text>
              </VStack>
            </Card>
          )}

          {agentResults.reviewer?.status === 'completed' && (
            <Card bg="linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)" p="4">
              <VStack align="start" spacing="3">
                <Flex align="center">
                  <Icon as={MdCheckCircle} color="white" w="20px" h="20px" me="10px" />
                  <Text color="white" fontSize="sm" fontWeight="700">
                    Reviewer Agent - Quality Assurance
                  </Text>
                </Flex>
                <Text color="white" fontSize="xs">
                  Validated all analysis results and ensured accuracy of findings
                  and recommendations.
                </Text>
              </VStack>
            </Card>
          )}
        </SimpleGrid>
      </Card>

      {/* Enhanced Next Steps */}
      <Card p="6">
        <Flex align="center" mb="20px">
          <IconBox
            w='56px'
            h='56px'
            bg='linear-gradient(90deg, #FFE066 0%, #FF6B6B 100%)'
            icon={<Icon w='32px' h='32px' as={MdAssignment} color='white' />}
            me="20px"
          />
          <Box>
            <Text
              color={textColor}
              fontSize="xl"
              fontWeight="700"
              lineHeight="100%"
            >
              Recommended Next Steps
            </Text>
            <Text
              color="secondaryGray.600"
              fontSize="sm"
              fontWeight="500"
            >
              Action items for business optimization
            </Text>
          </Box>
        </Flex>

        <VStack spacing="3" align="stretch">
          {[
            "Focus marketing budget on Product A to maximize ROI",
            "Investigate customer acquisition cost increases",
            "Review March operational expenses for cost optimization",
            "Prepare for seasonal peaks in Q1 and Q2"
          ].map((step, index) => (
            <Card key={index} bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" p="4">
              <Flex align="center">
                <Text color="white" fontSize="lg" fontWeight="700" me="15px">
                  {index + 1}.
                </Text>
                <Text color="white" fontSize="sm" fontWeight="500">
                  {step}
                </Text>
              </Flex>
            </Card>
          ))}
        </VStack>
      </Card>
    </VStack>
  );
};

export default BusinessReport;
