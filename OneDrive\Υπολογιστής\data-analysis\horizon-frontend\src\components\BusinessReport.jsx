import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Badge,
  Alert,
  AlertIcon,
  List,
  ListItem,
  ListIcon,
  Divider,
  Icon,
  useColorModeValue,
  Flex,
  SimpleGrid,
} from '@chakra-ui/react';

// Horizon UI Components
import Card from 'components/card/Card.js';
import IconBox from 'components/icons/IconBox.js';

import {
  MdArrowUpward,
  MdArrowDownward,
  MdWarning,
  MdLightbulb,
  MdFlag,
  MdInsights,
  MdTrendingUp,
  MdCheckCircle,
  MdAssignment,
  MdBusiness,
} from 'react-icons/md';

const BusinessReport = ({ agentResults }) => {
  const bgColor = useColorModeValue('white', 'navy.800');
  const textColor = useColorModeValue('secondaryGray.900', 'white');
  
  const hasCompletedAgents = Object.values(agentResults).some(
    state => state.status === 'completed'
  );

  // Mock business insights - these would come from agent results
  const mockRecommendations = [
    {
      type: 'opportunity',
      icon: MdTrendingUp,
      color: 'green',
      title: 'Revenue Growth Opportunity',
      description: 'Product A shows 34% higher profit margin than average. Consider increasing marketing focus.',
    },
    {
      type: 'warning',
      icon: MdWarning,
      color: 'orange',
      title: 'Declining Customer Acquisition',
      description: 'Customer acquisition dropped 3.2% this quarter. Review marketing strategies.',
    },
    {
      type: 'insight',
      icon: MdInsights,
      color: 'blue',
      title: 'Seasonal Pattern Detected',
      description: 'Sales peak in January and June. Plan inventory and marketing accordingly.',
    },    {
      type: 'action',
      icon: MdFlag,
      color: 'purple',
      title: 'Cost Optimization',
      description: 'Expenses in March were 15% above average. Investigate operational efficiency.',
    },
  ];

  const mockKeyFindings = [
    'Total revenue increased by 12.5% compared to previous period',
    'Product A accounts for 33% of total sales with highest profit margin',
    'Customer acquisition costs increased by 8% while volume decreased',
    'Seasonal trends show strong Q1 and Q2 performance',
    'Operational expenses peaked in March requiring investigation',
  ];

  if (!hasCompletedAgents) {
    return (
      <Alert status="info">
        <AlertIcon />
        Business intelligence report will be generated once AI agents complete their analysis.
      </Alert>
    );
  }

  return (
    <VStack spacing="6" align="stretch">
      {/* Enhanced Executive Summary */}
      <Card p="6">
        <Flex direction="column">
          <Flex align="center" mb="20px">
            <IconBox
              w='56px'
              h='56px'
              bg='linear-gradient(90deg, #4318FF 0%, #9F7AEA 100%)'
              icon={<Icon w='32px' h='32px' as={MdBusiness} color='white' />}
              me="20px"
            />
            <Box>
              <Text
                color={textColor}
                fontSize="xl"
                fontWeight="700"
                lineHeight="100%"
              >
                Executive Summary
              </Text>
              <Text
                color="secondaryGray.600"
                fontSize="sm"
                fontWeight="500"
              >
                AI-Powered Business Intelligence Report
              </Text>
            </Box>
          </Flex>

          <Card bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" p="6">
            <Text color="white" fontSize="sm" lineHeight="1.6" fontWeight="500">
              Your business data reveals strong overall performance with revenue growth of 12.5%.
              Product A dominates the market share with exceptional profit margins. However,
              customer acquisition trends require attention, and operational costs show opportunities
              for optimization.
            </Text>
          </Card>
        </Flex>
      </Card>

      {/* Enhanced Key Findings */}
      <Card p="6">
        <Flex align="center" mb="20px">
          <IconBox
            w='56px'
            h='56px'
            bg='linear-gradient(90deg, #FFE066 0%, #FF6B6B 100%)'
            icon={<Icon w='32px' h='32px' as={MdInsights} color='white' />}
            me="20px"
          />
          <Box>
            <Text
              color={textColor}
              fontSize="xl"
              fontWeight="700"
              lineHeight="100%"
            >
              Key Findings
            </Text>
            <Text
              color="secondaryGray.600"
              fontSize="sm"
              fontWeight="500"
            >
              Critical insights from data analysis
            </Text>
          </Box>
        </Flex>

        <VStack spacing="3" align="stretch">
          {mockKeyFindings.map((finding, index) => (
            <Card key={index} bg="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" p="4">
              <Flex align="center">
                <Icon as={MdCheckCircle} color="white" w="20px" h="20px" me="15px" />
                <Text color="white" fontSize="sm" fontWeight="500">
                  {finding}
                </Text>
              </Flex>
            </Card>
          ))}
        </VStack>
      </Card>

      {/* Enhanced AI Recommendations */}
      <Card p="6">
        <Flex align="center" mb="20px">
          <IconBox
            w='56px'
            h='56px'
            bg='linear-gradient(90deg, #4ECDC4 0%, #44A08D 100%)'
            icon={<Icon w='32px' h='32px' as={MdTrendingUp} color='white' />}
            me="20px"
          />
          <Box>
            <Text
              color={textColor}
              fontSize="xl"
              fontWeight="700"
              lineHeight="100%"
            >
              AI-Powered Recommendations
            </Text>
            <Text
              color="secondaryGray.600"
              fontSize="sm"
              fontWeight="500"
            >
              Strategic insights for business growth
            </Text>
          </Box>
        </Flex>

        <SimpleGrid columns={{ base: 1, md: 2 }} spacing="4">
          {mockRecommendations.map((rec, index) => {
            const gradients = [
              'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
              'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
            ];

            return (
              <Card key={index} bg={gradients[index % gradients.length]} p="5">
                <VStack align="start" spacing="3">
                  <Flex align="center" w="100%">
                    <Icon as={rec.icon} color="white" w="24px" h="24px" me="12px" />
                    <Box flex="1">
                      <Text color="white" fontSize="sm" fontWeight="700">
                        {rec.title}
                      </Text>
                      <Badge
                        bg="rgba(255,255,255,0.2)"
                        color="white"
                        size="sm"
                        borderRadius="full"
                        px="2"
                      >
                        {rec.type}
                      </Badge>
                    </Box>
                  </Flex>
                  <Text color="white" fontSize="xs" lineHeight="1.5">
                    {rec.description}
                  </Text>
                </VStack>
              </Card>
            );
          })}
        </SimpleGrid>
      </Card>

      {/* Enhanced Agent Contributions */}
      <Card p="6">
        <Flex align="center" mb="20px">
          <IconBox
            w='56px'
            h='56px'
            bg='linear-gradient(90deg, #FF6B6B 0%, #4ECDC4 100%)'
            icon={<Icon w='32px' h='32px' as={MdInsights} color='white' />}
            me="20px"
          />
          <Box>
            <Text
              color={textColor}
              fontSize="xl"
              fontWeight="700"
              lineHeight="100%"
            >
              AI Agent Contributions
            </Text>
            <Text
              color="secondaryGray.600"
              fontSize="sm"
              fontWeight="500"
            >
              Multi-agent analysis results
            </Text>
          </Box>
        </Flex>

        <SimpleGrid columns={{ base: 1, md: 2 }} spacing="4">
          {agentResults.executor?.status === 'completed' && (
            <Card bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" p="4">
              <VStack align="start" spacing="3">
                <Flex align="center">
                  <Icon as={MdInsights} color="white" w="20px" h="20px" me="10px" />
                  <Text color="white" fontSize="sm" fontWeight="700">
                    Executor Agent - Statistical Analysis
                  </Text>
                </Flex>
                <Text color="white" fontSize="xs">
                  Performed comprehensive statistical analysis revealing 12.5% revenue growth
                  and identified key performance metrics.
                </Text>
              </VStack>
            </Card>
          )}

          {agentResults.datafining?.status === 'completed' && (
            <Card bg="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" p="4">
              <VStack align="start" spacing="3">
                <Flex align="center">
                  <Icon as={MdFlag} color="white" w="20px" h="20px" me="10px" />
                  <Text color="white" fontSize="sm" fontWeight="700">
                    Data Mining Agent - Pattern Discovery
                  </Text>
                </Flex>
                <Text color="white" fontSize="xs">
                  Discovered seasonal patterns and product performance correlations
                  that inform strategic recommendations.
                </Text>
              </VStack>
            </Card>
          )}

          {agentResults.storyteller?.status === 'completed' && (
            <Card bg="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" p="4">
              <VStack align="start" spacing="3">
                <Flex align="center">
                  <Icon as={MdBusiness} color="white" w="20px" h="20px" me="10px" />
                  <Text color="white" fontSize="sm" fontWeight="700">
                    Storyteller Agent - Business Narrative
                  </Text>
                </Flex>
                <Text color="white" fontSize="xs">
                  Generated actionable business insights and recommendations
                  based on comprehensive data analysis.
                </Text>
              </VStack>
            </Card>
          )}

          {agentResults.reviewer?.status === 'completed' && (
            <Card bg="linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)" p="4">
              <VStack align="start" spacing="3">
                <Flex align="center">
                  <Icon as={MdCheckCircle} color="white" w="20px" h="20px" me="10px" />
                  <Text color="white" fontSize="sm" fontWeight="700">
                    Reviewer Agent - Quality Assurance
                  </Text>
                </Flex>
                <Text color="white" fontSize="xs">
                  Validated all analysis results and ensured accuracy of findings
                  and recommendations.
                </Text>
              </VStack>
            </Card>
          )}
        </SimpleGrid>
      </Card>

      {/* Enhanced Next Steps */}
      <Card p="6">
        <Flex align="center" mb="20px">
          <IconBox
            w='56px'
            h='56px'
            bg='linear-gradient(90deg, #FFE066 0%, #FF6B6B 100%)'
            icon={<Icon w='32px' h='32px' as={MdAssignment} color='white' />}
            me="20px"
          />
          <Box>
            <Text
              color={textColor}
              fontSize="xl"
              fontWeight="700"
              lineHeight="100%"
            >
              Recommended Next Steps
            </Text>
            <Text
              color="secondaryGray.600"
              fontSize="sm"
              fontWeight="500"
            >
              Action items for business optimization
            </Text>
          </Box>
        </Flex>

        <VStack spacing="3" align="stretch">
          {[
            "Focus marketing budget on Product A to maximize ROI",
            "Investigate customer acquisition cost increases",
            "Review March operational expenses for cost optimization",
            "Prepare for seasonal peaks in Q1 and Q2"
          ].map((step, index) => (
            <Card key={index} bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" p="4">
              <Flex align="center">
                <Text color="white" fontSize="lg" fontWeight="700" me="15px">
                  {index + 1}.
                </Text>
                <Text color="white" fontSize="sm" fontWeight="500">
                  {step}
                </Text>
              </Flex>
            </Card>
          ))}
        </VStack>
      </Card>
    </VStack>
  );
};

export default BusinessReport;
