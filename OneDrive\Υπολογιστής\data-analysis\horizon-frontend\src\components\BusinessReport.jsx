import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Badge,
  Alert,
  AlertIcon,
  List,
  ListItem,
  ListIcon,
  Divider,
  Icon,
  useColorModeValue,
} from '@chakra-ui/react';
import {
  MdArrowUpward,
  MdArrowDownward,
  MdWarning,
  MdLightbulb,
  MdFlag,
  MdInsights,
  MdTrendingUp,
} from 'react-icons/md';

const BusinessReport = ({ agentResults }) => {
  const bgColor = useColorModeValue('white', 'navy.800');
  const textColor = useColorModeValue('secondaryGray.900', 'white');
  
  const hasCompletedAgents = Object.values(agentResults).some(
    state => state.status === 'completed'
  );

  // Mock business insights - these would come from agent results
  const mockRecommendations = [
    {
      type: 'opportunity',
      icon: MdTrendingUp,
      color: 'green',
      title: 'Revenue Growth Opportunity',
      description: 'Product A shows 34% higher profit margin than average. Consider increasing marketing focus.',
    },
    {
      type: 'warning',
      icon: MdWarning,
      color: 'orange',
      title: 'Declining Customer Acquisition',
      description: 'Customer acquisition dropped 3.2% this quarter. Review marketing strategies.',
    },
    {
      type: 'insight',
      icon: MdInsights,
      color: 'blue',
      title: 'Seasonal Pattern Detected',
      description: 'Sales peak in January and June. Plan inventory and marketing accordingly.',
    },    {
      type: 'action',
      icon: MdFlag,
      color: 'purple',
      title: 'Cost Optimization',
      description: 'Expenses in March were 15% above average. Investigate operational efficiency.',
    },
  ];

  const mockKeyFindings = [
    'Total revenue increased by 12.5% compared to previous period',
    'Product A accounts for 33% of total sales with highest profit margin',
    'Customer acquisition costs increased by 8% while volume decreased',
    'Seasonal trends show strong Q1 and Q2 performance',
    'Operational expenses peaked in March requiring investigation',
  ];

  if (!hasCompletedAgents) {
    return (
      <Alert status="info">
        <AlertIcon />
        Business intelligence report will be generated once AI agents complete their analysis.
      </Alert>
    );
  }

  return (
    <VStack spacing="6" align="stretch">
      {/* Executive Summary */}
      <Box>
        <Heading size="md" mb="3" color={textColor}>
          Executive Summary
        </Heading>
        <Text fontSize="sm" color="gray.600" lineHeight="1.6">
          Your business data reveals strong overall performance with revenue growth of 12.5%. 
          Product A dominates the market share with exceptional profit margins. However, 
          customer acquisition trends require attention, and operational costs show opportunities 
          for optimization.
        </Text>
      </Box>

      <Divider />

      {/* Key Findings */}
      <Box>
        <Heading size="sm" mb="3" color={textColor}>
          Key Findings
        </Heading>
        <List spacing="2">
          {mockKeyFindings.map((finding, index) => (
            <ListItem key={index} fontSize="sm">
              <ListIcon as={MdLightbulb} color="blue.500" />
              {finding}
            </ListItem>
          ))}
        </List>
      </Box>

      <Divider />

      {/* AI Recommendations */}
      <Box>
        <Heading size="sm" mb="4" color={textColor}>
          AI-Powered Recommendations
        </Heading>
        <VStack spacing="4" align="stretch">
          {mockRecommendations.map((rec, index) => (
            <Box
              key={index}
              p="4"
              border="1px solid"
              borderColor="gray.200"
              borderRadius="8px"
              bg={`${rec.color}.50`}
              borderLeftWidth="4px"
              borderLeftColor={`${rec.color}.400`}
            >              <VStack align="start" spacing="2">                <HStack>
                  <Icon as={rec.icon} color={`${rec.color}.500`} boxSize="4" />
                  <Text fontWeight="bold" fontSize="sm" color={textColor}>
                    {rec.title}
                  </Text>
                  <Badge colorScheme={rec.color} size="sm">
                    {rec.type}
                  </Badge>
                </HStack>
                <Text fontSize="xs" color="gray.600">
                  {rec.description}
                </Text>
              </VStack>
            </Box>
          ))}
        </VStack>
      </Box>

      <Divider />

      {/* Agent Contributions */}
      <Box>
        <Heading size="sm" mb="3" color={textColor}>
          AI Agent Contributions
        </Heading>
        <VStack spacing="3" align="stretch">
          {agentResults.executor?.status === 'completed' && (
            <Box p="3" bg="blue.50" borderRadius="6px">
              <Text fontWeight="bold" fontSize="xs" color="blue.800">
                Executor Agent - Statistical Analysis
              </Text>
              <Text fontSize="xs" color="blue.700">
                Performed comprehensive statistical analysis revealing 12.5% revenue growth and identified key performance metrics.
              </Text>
            </Box>
          )}
          
          {agentResults.datafining?.status === 'completed' && (
            <Box p="3" bg="orange.50" borderRadius="6px">
              <Text fontWeight="bold" fontSize="xs" color="orange.800">
                Data Mining Agent - Pattern Discovery
              </Text>
              <Text fontSize="xs" color="orange.700">
                Discovered seasonal patterns and product performance correlations that inform strategic recommendations.
              </Text>
            </Box>
          )}
          
          {agentResults.storyteller?.status === 'completed' && (
            <Box p="3" bg="teal.50" borderRadius="6px">
              <Text fontWeight="bold" fontSize="xs" color="teal.800">
                Storyteller Agent - Business Narrative
              </Text>
              <Text fontSize="xs" color="teal.700">
                Generated actionable business insights and recommendations based on comprehensive data analysis.
              </Text>
            </Box>
          )}
          
          {agentResults.reviewer?.status === 'completed' && (
            <Box p="3" bg="green.50" borderRadius="6px">
              <Text fontWeight="bold" fontSize="xs" color="green.800">
                Reviewer Agent - Quality Assurance
              </Text>
              <Text fontSize="xs" color="green.700">
                Validated all analysis results and ensured accuracy of findings and recommendations.
              </Text>
            </Box>
          )}
        </VStack>
      </Box>

      {/* Next Steps */}
      <Box
        p="4"
        bg="purple.50"
        borderRadius="8px"
        border="1px solid"
        borderColor="purple.200"
      >
        <Heading size="sm" mb="2" color="purple.800">
          Recommended Next Steps
        </Heading>
        <List spacing="1" fontSize="xs">
          <ListItem color="purple.700">
            1. Focus marketing budget on Product A to maximize ROI
          </ListItem>
          <ListItem color="purple.700">
            2. Investigate customer acquisition cost increases
          </ListItem>
          <ListItem color="purple.700">
            3. Review March operational expenses for cost optimization
          </ListItem>
          <ListItem color="purple.700">
            4. Prepare for seasonal peaks in Q1 and Q2
          </ListItem>
        </List>
      </Box>
    </VStack>
  );
};

export default BusinessReport;
