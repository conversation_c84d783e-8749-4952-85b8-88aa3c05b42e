/**
 * Agent types and related constants
 */

// Core agent types
export const AGENT_TYPES = {
  EXECUTOR: 'executor',
  EXPRESSER: 'expresser',
  REVIEWER: 'reviewer',
  DATAFINING: 'datafining',
  STORYTELLER: 'storyteller'
};

// Agent execution states
export const AGENT_STATES = {
  IDLE: 'idle',
  INITIALIZING: 'initializing',
  PENDING: 'pending',
  RUNNING: 'running',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  TIMEOUT: 'timeout'
};

// Agent execution priorities
export const AGENT_PRIORITIES = {
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  URGENT: 'urgent'
};

// Agent execution modes
export const AGENT_MODES = {
  SYNC: 'sync',
  ASYNC: 'async',
  BATCH: 'batch',
  STREAM: 'stream'
};

// Agent capability categories
export const AGENT_CAPABILITY_CATEGORIES = {
  DATA_PROCESSING: 'data_processing',
  ANALYSIS: 'analysis',
  VISUALIZATION: 'visualization',
  REPORTING: 'reporting',
  VALIDATION: 'validation',
  TRANSFORMATION: 'transformation',
  PREDICTION: 'prediction',
  OPTIMIZATION: 'optimization'
};

// Specific agent capabilities
export const AGENT_CAPABILITIES = {
  // Executor Agent Capabilities
  EXECUTOR: {
    DATA_INGESTION: 'data_ingestion',
    DATA_CLEANING: 'data_cleaning',
    STATISTICAL_ANALYSIS: 'statistical_analysis',
    PATTERN_DETECTION: 'pattern_detection',
    OUTLIER_DETECTION: 'outlier_detection',
    CORRELATION_ANALYSIS: 'correlation_analysis',
    TREND_ANALYSIS: 'trend_analysis',
    CLUSTERING: 'clustering',
    CLASSIFICATION: 'classification',
    REGRESSION: 'regression'
  },

  // Expresser Agent Capabilities
  EXPRESSER: {
    REPORT_GENERATION: 'report_generation',
    CHART_CREATION: 'chart_creation',
    DASHBOARD_BUILDING: 'dashboard_building',
    DATA_FORMATTING: 'data_formatting',
    TEMPLATE_PROCESSING: 'template_processing',
    EXPORT_GENERATION: 'export_generation',
    PRESENTATION_CREATION: 'presentation_creation',
    INFOGRAPHIC_GENERATION: 'infographic_generation'
  },

  // Reviewer Agent Capabilities
  REVIEWER: {
    QUALITY_ASSURANCE: 'quality_assurance',
    ACCURACY_VALIDATION: 'accuracy_validation',
    CONSISTENCY_CHECK: 'consistency_check',
    COMPLETENESS_VALIDATION: 'completeness_validation',
    ERROR_DETECTION: 'error_detection',
    BIAS_DETECTION: 'bias_detection',
    COMPLIANCE_CHECK: 'compliance_check',
    BENCHMARK_COMPARISON: 'benchmark_comparison'
  },

  // Data Fining Agent Capabilities
  DATAFINING: {
    DATA_PROFILING: 'data_profiling',
    SCHEMA_INFERENCE: 'schema_inference',
    DATA_TYPE_DETECTION: 'data_type_detection',
    MISSING_VALUE_HANDLING: 'missing_value_handling',
    DUPLICATE_DETECTION: 'duplicate_detection',
    DATA_NORMALIZATION: 'data_normalization',
    FEATURE_ENGINEERING: 'feature_engineering',
    DATA_ENHANCEMENT: 'data_enhancement'
  },

  // Storyteller Agent Capabilities
  STORYTELLER: {
    NARRATIVE_GENERATION: 'narrative_generation',
    INSIGHT_INTERPRETATION: 'insight_interpretation',
    BUSINESS_CONTEXT_ANALYSIS: 'business_context_analysis',
    RECOMMENDATION_GENERATION: 'recommendation_generation',
    EXECUTIVE_SUMMARY_CREATION: 'executive_summary_creation',
    STORY_STRUCTURING: 'story_structuring',
    AUDIENCE_ADAPTATION: 'audience_adaptation',
    ACTION_ITEM_GENERATION: 'action_item_generation'
  }
};

// Agent communication protocols
export const AGENT_PROTOCOLS = {
  REQUEST_RESPONSE: 'request_response',
  PUBLISH_SUBSCRIBE: 'publish_subscribe',
  EVENT_DRIVEN: 'event_driven',
  PIPELINE: 'pipeline',
  WORKFLOW: 'workflow'
};

// Agent error types
export const AGENT_ERROR_TYPES = {
  INITIALIZATION_ERROR: 'initialization_error',
  EXECUTION_ERROR: 'execution_error',
  TIMEOUT_ERROR: 'timeout_error',
  VALIDATION_ERROR: 'validation_error',
  CONFIGURATION_ERROR: 'configuration_error',
  RESOURCE_ERROR: 'resource_error',
  COMMUNICATION_ERROR: 'communication_error',
  DATA_ERROR: 'data_error',
  PERMISSION_ERROR: 'permission_error'
};

// Agent performance metrics
export const AGENT_METRICS = {
  EXECUTION_TIME: 'execution_time',
  MEMORY_USAGE: 'memory_usage',
  CPU_USAGE: 'cpu_usage',
  SUCCESS_RATE: 'success_rate',
  ERROR_RATE: 'error_rate',
  THROUGHPUT: 'throughput',
  LATENCY: 'latency',
  ACCURACY: 'accuracy',
  QUALITY_SCORE: 'quality_score'
};

// Agent configuration keys
export const AGENT_CONFIG_KEYS = {
  MAX_EXECUTION_TIME: 'max_execution_time',
  MAX_MEMORY_USAGE: 'max_memory_usage',
  RETRY_ATTEMPTS: 'retry_attempts',
  RETRY_DELAY: 'retry_delay',
  BATCH_SIZE: 'batch_size',
  CONCURRENCY_LIMIT: 'concurrency_limit',
  DEBUG_MODE: 'debug_mode',
  LOGGING_LEVEL: 'logging_level',
  CACHE_ENABLED: 'cache_enabled',
  PERSISTENCE_ENABLED: 'persistence_enabled'
};

// Agent event types
export const AGENT_EVENTS = {
  CREATED: 'agent_created',
  STARTED: 'agent_started',
  PROGRESS: 'agent_progress',
  COMPLETED: 'agent_completed',
  FAILED: 'agent_failed',
  CANCELLED: 'agent_cancelled',
  PAUSED: 'agent_paused',
  RESUMED: 'agent_resumed',
  ERROR: 'agent_error',
  WARNING: 'agent_warning'
};

// Agent workflow patterns
export const AGENT_PATTERNS = {
  PEER: 'peer', // PEER pattern - agents work as equals
  DOE: 'doe', // DOE pattern - Data, Operation, Evaluation
  PIPELINE: 'pipeline', // Sequential execution
  PARALLEL: 'parallel', // Parallel execution
  MAP_REDUCE: 'map_reduce', // Map-reduce pattern
  MASTER_WORKER: 'master_worker', // Master-worker pattern
  OBSERVER: 'observer', // Observer pattern
  CHAIN_OF_RESPONSIBILITY: 'chain_of_responsibility'
};

// Agent resource requirements
export const AGENT_RESOURCE_TYPES = {
  CPU: 'cpu',
  MEMORY: 'memory',
  STORAGE: 'storage',
  NETWORK: 'network',
  GPU: 'gpu',
  DATABASE: 'database',
  EXTERNAL_API: 'external_api'
};

// Agent scaling strategies
export const AGENT_SCALING = {
  MANUAL: 'manual',
  AUTO_SCALE_UP: 'auto_scale_up',
  AUTO_SCALE_DOWN: 'auto_scale_down',
  ELASTIC: 'elastic',
  SCHEDULED: 'scheduled',
  LOAD_BASED: 'load_based'
};

// Agent security levels
export const AGENT_SECURITY_LEVELS = {
  PUBLIC: 'public',
  INTERNAL: 'internal',
  CONFIDENTIAL: 'confidential',
  RESTRICTED: 'restricted',
  TOP_SECRET: 'top_secret'
};

// Agent deployment environments
export const AGENT_ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  TESTING: 'testing',
  STAGING: 'staging',
  PRODUCTION: 'production',
  SANDBOX: 'sandbox'
};

// Agent versioning
export const AGENT_VERSIONS = {
  ALPHA: 'alpha',
  BETA: 'beta',
  RC: 'release_candidate',
  STABLE: 'stable',
  LTS: 'long_term_support',
  DEPRECATED: 'deprecated'
};

// Default agent configurations
export const DEFAULT_AGENT_CONFIG = {
  [AGENT_TYPES.EXECUTOR]: {
    timeout: 300000, // 5 minutes
    retries: 3,
    priority: AGENT_PRIORITIES.NORMAL,
    mode: AGENT_MODES.ASYNC,
    resources: {
      [AGENT_RESOURCE_TYPES.CPU]: 'medium',
      [AGENT_RESOURCE_TYPES.MEMORY]: 'medium'
    }
  },
  [AGENT_TYPES.EXPRESSER]: {
    timeout: 180000, // 3 minutes
    retries: 2,
    priority: AGENT_PRIORITIES.NORMAL,
    mode: AGENT_MODES.ASYNC,
    resources: {
      [AGENT_RESOURCE_TYPES.CPU]: 'low',
      [AGENT_RESOURCE_TYPES.MEMORY]: 'low'
    }
  },
  [AGENT_TYPES.REVIEWER]: {
    timeout: 120000, // 2 minutes
    retries: 2,
    priority: AGENT_PRIORITIES.HIGH,
    mode: AGENT_MODES.SYNC,
    resources: {
      [AGENT_RESOURCE_TYPES.CPU]: 'low',
      [AGENT_RESOURCE_TYPES.MEMORY]: 'low'
    }
  },
  [AGENT_TYPES.DATAFINING]: {
    timeout: 600000, // 10 minutes
    retries: 3,
    priority: AGENT_PRIORITIES.HIGH,
    mode: AGENT_MODES.ASYNC,
    resources: {
      [AGENT_RESOURCE_TYPES.CPU]: 'high',
      [AGENT_RESOURCE_TYPES.MEMORY]: 'high'
    }
  },
  [AGENT_TYPES.STORYTELLER]: {
    timeout: 240000, // 4 minutes
    retries: 2,
    priority: AGENT_PRIORITIES.NORMAL,
    mode: AGENT_MODES.ASYNC,
    resources: {
      [AGENT_RESOURCE_TYPES.CPU]: 'medium',
      [AGENT_RESOURCE_TYPES.MEMORY]: 'medium'
    }
  }
};

// Agent type metadata
export const AGENT_TYPE_METADATA = {
  [AGENT_TYPES.EXECUTOR]: {
    name: 'Executor Agent',
    description: 'Performs data analysis and computation tasks',
    icon: 'play-circle',
    color: '#3182ce',
    category: 'computation',
    complexity: 'high'
  },
  [AGENT_TYPES.EXPRESSER]: {
    name: 'Expresser Agent',
    description: 'Creates visualizations and formatted reports',
    icon: 'bar-chart',
    color: '#38a169',
    category: 'presentation',
    complexity: 'medium'
  },
  [AGENT_TYPES.REVIEWER]: {
    name: 'Reviewer Agent',
    description: 'Validates and ensures quality of results',
    icon: 'check-circle',
    color: '#d69e2e',
    category: 'validation',
    complexity: 'low'
  },
  [AGENT_TYPES.DATAFINING]: {
    name: 'Data Fining Agent',
    description: 'Processes and refines raw data',
    icon: 'filter',
    color: '#805ad5',
    category: 'preprocessing',
    complexity: 'high'
  },
  [AGENT_TYPES.STORYTELLER]: {
    name: 'Storyteller Agent',
    description: 'Generates narratives and business insights',
    icon: 'book-open',
    color: '#e53e3e',
    category: 'interpretation',
    complexity: 'medium'
  }
};

// Agent compatibility matrix
export const AGENT_COMPATIBILITY = {
  [AGENT_TYPES.EXECUTOR]: [
    AGENT_TYPES.DATAFINING,
    AGENT_TYPES.EXPRESSER,
    AGENT_TYPES.REVIEWER,
    AGENT_TYPES.STORYTELLER
  ],
  [AGENT_TYPES.EXPRESSER]: [
    AGENT_TYPES.EXECUTOR,
    AGENT_TYPES.REVIEWER,
    AGENT_TYPES.STORYTELLER
  ],
  [AGENT_TYPES.REVIEWER]: [
    AGENT_TYPES.EXECUTOR,
    AGENT_TYPES.EXPRESSER,
    AGENT_TYPES.DATAFINING,
    AGENT_TYPES.STORYTELLER
  ],
  [AGENT_TYPES.DATAFINING]: [
    AGENT_TYPES.EXECUTOR,
    AGENT_TYPES.REVIEWER
  ],
  [AGENT_TYPES.STORYTELLER]: [
    AGENT_TYPES.EXECUTOR,
    AGENT_TYPES.EXPRESSER,
    AGENT_TYPES.REVIEWER
  ]
};

// Utility functions
export const getAgentDisplayName = (type) => {
  return AGENT_TYPE_METADATA[type]?.name || type;
};

export const getAgentCapabilities = (type) => {
  return Object.values(AGENT_CAPABILITIES[type.toUpperCase()] || {});
};

export const isAgentCompatible = (type1, type2) => {
  return AGENT_COMPATIBILITY[type1]?.includes(type2) || false;
};

export const getDefaultConfig = (type) => {
  return { ...DEFAULT_AGENT_CONFIG[type] } || {};
};

export default {
  AGENT_TYPES,
  AGENT_STATES,
  AGENT_PRIORITIES,
  AGENT_MODES,
  AGENT_CAPABILITY_CATEGORIES,
  AGENT_CAPABILITIES,
  AGENT_PROTOCOLS,
  AGENT_ERROR_TYPES,
  AGENT_METRICS,
  AGENT_CONFIG_KEYS,
  AGENT_EVENTS,
  AGENT_PATTERNS,
  AGENT_RESOURCE_TYPES,
  AGENT_SCALING,
  AGENT_SECURITY_LEVELS,
  AGENT_ENVIRONMENTS,
  AGENT_VERSIONS,
  DEFAULT_AGENT_CONFIG,
  AGENT_TYPE_METADATA,
  AGENT_COMPATIBILITY,
  getAgentDisplayName,
  getAgentCapabilities,
  isAgentCompatible,
  getDefaultConfig
};
