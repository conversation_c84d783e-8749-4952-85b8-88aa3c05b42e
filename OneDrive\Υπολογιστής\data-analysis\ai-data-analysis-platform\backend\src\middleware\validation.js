const Joi = require('joi');
const { validationError } = require('./errorHandler');

class ValidationMiddleware {
  constructor() {
    // Common validation schemas
    this.schemas = {
      // File upload validation
      fileUpload: Joi.object({
        file: Joi.object({
          originalname: Joi.string().required(),
          mimetype: Joi.string().valid(
            'text/csv',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'text/plain',
            'application/json'
          ).required(),
          size: Joi.number().max(50 * 1024 * 1024).required() // 50MB max
        }).required()
      }),

      // Analysis request validation
      analysisRequest: Joi.object({
        analysisType: Joi.string().valid(
          'comprehensive',
          'quick',
          'financial',
          'customer',
          'operational'
        ).default('comprehensive'),
        options: Joi.object({
          includeForecasting: Joi.boolean().default(true),
          includeTrends: Joi.boolean().default(true),
          includeInsights: Joi.boolean().default(true),
          includeRecommendations: Joi.boolean().default(true)
        }).default({})
      }),

      // Agent task validation
      agentTask: Joi.object({
        agentType: Joi.string().valid(
          'planner',
          'executor',
          'expresser',
          'reviewer',
          'dataFining',
          'storyteller'
        ).required(),
        taskData: Joi.object().required(),
        priority: Joi.string().valid('low', 'medium', 'high').default('medium'),
        timeout: Joi.number().min(1000).max(300000).default(30000) // 1s to 5min
      }),

      // DeepSeek API request validation
      deepseekRequest: Joi.object({
        prompt: Joi.string().min(1).max(10000).required(),
        model: Joi.string().default('deepseek-chat'),
        temperature: Joi.number().min(0).max(2).default(0.7),
        max_tokens: Joi.number().min(1).max(4096).default(2048),
        top_p: Joi.number().min(0).max(1).default(0.9)
      }),

      // User authentication validation
      userLogin: Joi.object({
        email: Joi.string().email().required(),
        password: Joi.string().min(6).required()
      }),

      userRegister: Joi.object({
        email: Joi.string().email().required(),
        password: Joi.string().min(6).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/),
        name: Joi.string().min(2).max(50).required()
      }),

      // Report generation validation
      reportRequest: Joi.object({
        analysisId: Joi.string().uuid().required(),
        reportType: Joi.string().valid(
          'executive',
          'detailed',
          'technical',
          'summary'
        ).default('executive'),
        format: Joi.string().valid('json', 'pdf', 'html').default('json'),
        sections: Joi.array().items(
          Joi.string().valid(
            'summary',
            'metrics',
            'trends',
            'insights',
            'recommendations',
            'risks'
          )
        ).default(['summary', 'metrics', 'insights', 'recommendations'])
      }),

      // Data export validation
      exportRequest: Joi.object({
        analysisId: Joi.string().uuid().required(),
        format: Joi.string().valid('csv', 'xlsx', 'json').required(),
        includeRaw: Joi.boolean().default(false),
        includeResults: Joi.boolean().default(true)
      }),

      // Pagination validation
      pagination: Joi.object({
        page: Joi.number().integer().min(1).default(1),
        limit: Joi.number().integer().min(1).max(100).default(20),
        sortBy: Joi.string().default('createdAt'),
        sortOrder: Joi.string().valid('asc', 'desc').default('desc')
      }),

      // Search validation
      search: Joi.object({
        query: Joi.string().min(1).max(500),
        filters: Joi.object({
          status: Joi.array().items(Joi.string().valid('pending', 'processing', 'completed', 'failed')),
          dateFrom: Joi.date(),
          dateTo: Joi.date(),
          fileType: Joi.array().items(Joi.string()),
          analysisType: Joi.array().items(Joi.string())
        }).default({})
      })
    };
  }

  // Generic validation middleware factory
  validate(schema, source = 'body') {
    return (req, res, next) => {
      try {
        const data = this.getDataFromSource(req, source);
        const { error, value } = schema.validate(data, {
          abortEarly: false,
          stripUnknown: true,
          allowUnknown: false
        });

        if (error) {
          const errorMessage = error.details
            .map(detail => detail.message)
            .join(', ');
          throw validationError(errorMessage);
        }

        // Replace the data with validated and sanitized values
        this.setDataToSource(req, source, value);
        next();
      } catch (err) {
        next(err);
      }
    };
  }

  // Specific validation methods
  validateFileUpload() {
    return (req, res, next) => {
      try {
        if (!req.file) {
          throw validationError('No file uploaded');
        }

        const { error } = this.schemas.fileUpload.validate({ file: req.file });
        if (error) {
          throw validationError(error.details[0].message);
        }

        next();
      } catch (err) {
        next(err);
      }
    };
  }

  validateAnalysisRequest() {
    return this.validate(this.schemas.analysisRequest, 'body');
  }

  validateAgentTask() {
    return this.validate(this.schemas.agentTask, 'body');
  }

  validateDeepSeekRequest() {
    return this.validate(this.schemas.deepseekRequest, 'body');
  }

  validateUserLogin() {
    return this.validate(this.schemas.userLogin, 'body');
  }

  validateUserRegister() {
    return this.validate(this.schemas.userRegister, 'body');
  }

  validateReportRequest() {
    return this.validate(this.schemas.reportRequest, 'body');
  }

  validateExportRequest() {
    return this.validate(this.schemas.exportRequest, 'body');
  }

  validatePagination() {
    return this.validate(this.schemas.pagination, 'query');
  }

  validateSearch() {
    return this.validate(this.schemas.search, 'query');
  }

  // Validate UUID parameter
  validateUUIDParam(paramName = 'id') {
    const schema = Joi.string().uuid().required();
    return (req, res, next) => {
      try {
        const { error } = schema.validate(req.params[paramName]);
        if (error) {
          throw validationError(`Invalid ${paramName} parameter`);
        }
        next();
      } catch (err) {
        next(err);
      }
    };
  }

  // Validate request headers
  validateHeaders(requiredHeaders = []) {
    return (req, res, next) => {
      try {
        const missingHeaders = requiredHeaders.filter(
          header => !req.headers[header.toLowerCase()]
        );

        if (missingHeaders.length > 0) {
          throw validationError(`Missing required headers: ${missingHeaders.join(', ')}`);
        }

        next();
      } catch (err) {
        next(err);
      }
    };
  }

  // Validate content type
  validateContentType(allowedTypes = ['application/json']) {
    return (req, res, next) => {
      try {
        const contentType = req.headers['content-type'];
        
        if (!contentType) {
          throw validationError('Content-Type header is required');
        }

        const isAllowed = allowedTypes.some(type => 
          contentType.toLowerCase().includes(type.toLowerCase())
        );

        if (!isAllowed) {
          throw validationError(
            `Invalid Content-Type. Allowed: ${allowedTypes.join(', ')}`
          );
        }

        next();
      } catch (err) {
        next(err);
      }
    };
  }

  // Sanitize input data
  sanitizeInput() {
    return (req, res, next) => {
      try {
        // Sanitize strings in body, query, and params
        this.sanitizeObject(req.body);
        this.sanitizeObject(req.query);
        this.sanitizeObject(req.params);
        next();
      } catch (err) {
        next(err);
      }
    };
  }

  sanitizeObject(obj) {
    if (!obj || typeof obj !== 'object') return;

    Object.keys(obj).forEach(key => {
      if (typeof obj[key] === 'string') {
        // Remove potentially dangerous characters
        obj[key] = obj[key]
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=\s*"[^"]*"/gi, '')
          .replace(/on\w+\s*=\s*'[^']*'/gi, '')
          .trim();
      } else if (typeof obj[key] === 'object') {
        this.sanitizeObject(obj[key]);
      }
    });
  }

  // Custom validation for complex business rules
  validateBusinessRules() {
    return (req, res, next) => {
      try {
        // Add custom business validation logic here
        // For example, check if user has permission for the requested operation
        
        next();
      } catch (err) {
        next(err);
      }
    };
  }

  // Helper methods
  getDataFromSource(req, source) {
    switch (source) {
      case 'body':
        return req.body;
      case 'query':
        return req.query;
      case 'params':
        return req.params;
      case 'headers':
        return req.headers;
      default:
        return req.body;
    }
  }

  setDataToSource(req, source, data) {
    switch (source) {
      case 'body':
        req.body = data;
        break;
      case 'query':
        req.query = data;
        break;
      case 'params':
        req.params = data;
        break;
      case 'headers':
        req.headers = data;
        break;
      default:
        req.body = data;
    }
  }

  // Create dynamic validation schema
  createSchema(rules) {
    return Joi.object(rules);
  }

  // Validate array of items
  validateArray(itemSchema, options = {}) {
    const schema = Joi.array().items(itemSchema);
    
    if (options.min) schema.min(options.min);
    if (options.max) schema.max(options.max);
    if (options.unique) schema.unique();

    return this.validate(schema);
  }

  // Conditional validation
  validateConditional(condition, schema) {
    return (req, res, next) => {
      try {
        if (condition(req)) {
          return this.validate(schema)(req, res, next);
        }
        next();
      } catch (err) {
        next(err);
      }
    };
  }
}

// Export both class and instance
const validationMiddleware = new ValidationMiddleware();

module.exports = {
  ValidationMiddleware,
  validationMiddleware,
  
  // Convenience exports
  validateFileUpload: validationMiddleware.validateFileUpload.bind(validationMiddleware),
  validateAnalysisRequest: validationMiddleware.validateAnalysisRequest.bind(validationMiddleware),
  validateAgentTask: validationMiddleware.validateAgentTask.bind(validationMiddleware),
  validateDeepSeekRequest: validationMiddleware.validateDeepSeekRequest.bind(validationMiddleware),
  validateUserLogin: validationMiddleware.validateUserLogin.bind(validationMiddleware),
  validateUserRegister: validationMiddleware.validateUserRegister.bind(validationMiddleware),
  validateReportRequest: validationMiddleware.validateReportRequest.bind(validationMiddleware),
  validateExportRequest: validationMiddleware.validateExportRequest.bind(validationMiddleware),
  validatePagination: validationMiddleware.validatePagination.bind(validationMiddleware),
  validateSearch: validationMiddleware.validateSearch.bind(validationMiddleware),
  validateUUIDParam: validationMiddleware.validateUUIDParam.bind(validationMiddleware),
  validateHeaders: validationMiddleware.validateHeaders.bind(validationMiddleware),
  validateContentType: validationMiddleware.validateContentType.bind(validationMiddleware),
  sanitizeInput: validationMiddleware.sanitizeInput.bind(validationMiddleware)
};
