const express = require('express');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');
const XLSX = require('xlsx');
const csv = require('csv-parser');
const { Readable } = require('stream');

const router = express.Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'text/csv',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/plain',
      'application/json'
    ];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`File type ${file.mimetype} is not supported`), false);
    }
  }
});

// File processing functions
async function parseCSV(buffer) {
  return new Promise((resolve, reject) => {
    const results = [];
    const stream = Readable.from(buffer.toString());

    stream
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', reject);
  });
}

function parseExcel(buffer) {
  const workbook = XLSX.read(buffer, { type: 'buffer' });
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  return XLSX.utils.sheet_to_json(worksheet);
}

function parseJSON(buffer) {
  return JSON.parse(buffer.toString());
}

// Enhanced file upload route with real data processing
router.post('/', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file provided'
      });
    }

    console.log('Processing file:', req.file.originalname, 'Type:', req.file.mimetype);

    let parsedData = [];
    const fileId = uuidv4();

    // Parse file based on type
    try {
      switch (req.file.mimetype) {
        case 'text/csv':
          parsedData = await parseCSV(req.file.buffer);
          break;
        case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
        case 'application/vnd.ms-excel':
          parsedData = parseExcel(req.file.buffer);
          break;
        case 'application/json':
          parsedData = parseJSON(req.file.buffer);
          break;
        default:
          throw new Error(`Unsupported file type: ${req.file.mimetype}`);
      }

      // Store parsed data in memory (in production, use database)
      global.uploadedFiles = global.uploadedFiles || {};
      global.uploadedFiles[fileId] = {
        filename: req.file.originalname,
        data: parsedData,
        uploadedAt: new Date().toISOString(),
        size: req.file.size,
        mimeType: req.file.mimetype
      };

      console.log(`Successfully parsed ${parsedData.length} records from ${req.file.originalname}`);

      const response = {
        success: true,
        message: 'File uploaded and processed successfully',
        data: {
          fileId,
          filename: req.file.originalname,
          size: req.file.size,
          mimeType: req.file.mimetype,
          uploadedAt: new Date().toISOString(),
          recordCount: parsedData.length,
          columns: parsedData.length > 0 ? Object.keys(parsedData[0]) : [],
          preview: parsedData.slice(0, 3) // First 3 records for preview
        }
      };

      res.status(201).json(response);
    } catch (parseError) {
      console.error('File parsing error:', parseError);
      res.status(400).json({
        success: false,
        message: 'Failed to parse file',
        error: parseError.message
      });
    }
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      success: false,
      message: 'File upload failed',
      error: error.message
    });
  }
});

// Get file status
router.get('/:fileId', (req, res) => {
  res.json({
    success: true,
    data: {
      fileId: req.params.fileId,
      status: 'processed',
      message: 'File ready for analysis'
    }
  });
});

module.exports = router;
