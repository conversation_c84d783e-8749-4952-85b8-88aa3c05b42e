version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - FRONTEND_URL=http://localhost:3000
    volumes:
      - ./uploads:/app/uploads
    depends_on:
      - redis
    networks:
      - ai-platform

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=http://localhost:5000
    depends_on:
      - backend
    networks:
      - ai-platform

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-platform

volumes:
  redis_data:

networks:
  ai-platform:
    driver: bridge