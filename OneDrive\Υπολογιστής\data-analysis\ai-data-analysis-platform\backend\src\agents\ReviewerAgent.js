class ReviewerAgent {
  constructor(deepseekAPI) {
    this.deepseekAPI = deepseekAPI;
    this.agentId = 'reviewer';
    this.pattern = 'PEER';
    this.status = 'idle';
    this.capabilities = [
      'quality_assessment',
      'data_validation',
      'result_verification',
      'accuracy_check',
      'completeness_review'
    ];
  }

  async execute(analysisResults = {}) {
    this.status = 'processing';
    
    try {
      // Step 1: Validate data quality
      const dataQualityAssessment = await this.assessDataQuality(analysisResults);
      
      // Step 2: Review analysis accuracy
      const accuracyReview = await this.reviewAnalysisAccuracy(analysisResults);
      
      // Step 3: Check completeness
      const completenessCheck = await this.checkCompleteness(analysisResults);
      
      // Step 4: Validate visualizations
      const visualValidation = await this.validateVisualizations(analysisResults);
      
      // Step 5: Generate quality score
      const qualityScore = await this.calculateQualityScore(
        dataQualityAssessment,
        accuracyReview,
        completenessCheck,
        visualValidation
      );
      
      // Step 6: Generate recommendations
      const recommendations = await this.generateQualityRecommendations(
        dataQualityAssessment,
        accuracyReview,
        completenessCheck
      );
      
      this.status = 'completed';
      
      return {
        dataQualityAssessment,
        accuracyReview,
        completenessCheck,
        visualValidation,
        qualityScore,
        recommendations,
        validation: {
          passed: qualityScore >= 0.7,
          issues: this.identifyIssues(dataQualityAssessment, accuracyReview, completenessCheck),
          warnings: this.generateWarnings(qualityScore)
        },
        metadata: {
          agentId: this.agentId,
          pattern: this.pattern,
          timestamp: new Date().toISOString(),
          reviewDuration: this.getReviewDuration(),
          confidence: this.calculateConfidence(qualityScore)
        }
      };
      
    } catch (error) {
      this.status = 'error';
      console.error('Reviewer Agent Error:', error);
      throw error;
    }
  }

  async assessDataQuality(analysisResults) {
    const assessment = {
      overall_score: 0,
      data_completeness: 0,
      data_consistency: 0,
      outlier_detection: [],
      missing_values: {},
      data_types: {},
      recommendations: []
    };

    // Extract data from analysis results
    const data = this.extractDataFromResults(analysisResults);
    
    if (!data || data.length === 0) {
      assessment.overall_score = 0;
      assessment.recommendations.push('No data available for quality assessment');
      return assessment;
    }

    // Check data completeness
    assessment.data_completeness = this.calculateDataCompleteness(data);
    
    // Check data consistency
    assessment.data_consistency = this.checkDataConsistency(data);
    
    // Detect outliers
    assessment.outlier_detection = this.detectOutliers(data);
    
    // Check missing values
    assessment.missing_values = this.analyzeMissingValues(data);
    
    // Validate data types
    assessment.data_types = this.validateDataTypes(data);
    
    // Calculate overall score
    assessment.overall_score = (
      assessment.data_completeness * 0.3 +
      assessment.data_consistency * 0.3 +
      (1 - Math.min(assessment.outlier_detection.length / data.length, 0.2)) * 0.4
    );

    // Generate recommendations
    if (assessment.data_completeness < 0.9) {
      assessment.recommendations.push('Improve data completeness - missing values detected');
    }
    if (assessment.data_consistency < 0.8) {
      assessment.recommendations.push('Address data consistency issues');
    }
    if (assessment.outlier_detection.length > data.length * 0.05) {
      assessment.recommendations.push('Review and validate outlier values');
    }

    return assessment;
  }

  async reviewAnalysisAccuracy(analysisResults) {
    const review = {
      statistical_accuracy: 0,
      calculation_validation: {},
      forecast_reliability: 0,
      correlation_validity: 0,
      trend_accuracy: 0,
      issues: [],
      confidence_level: 0
    };

    // Validate statistical calculations
    if (analysisResults.execute?.statisticalAnalysis) {
      review.statistical_accuracy = this.validateStatisticalCalculations(
        analysisResults.execute.statisticalAnalysis
      );
    }

    // Validate KPI calculations
    if (analysisResults.execute?.kpiCalculations) {
      review.calculation_validation = this.validateKPICalculations(
        analysisResults.execute.kpiCalculations
      );
    }

    // Review forecast reliability
    if (analysisResults.execute?.forecasts) {
      review.forecast_reliability = this.assessForecastReliability(
        analysisResults.execute.forecasts
      );
    }

    // Validate correlations
    if (analysisResults.execute?.statisticalAnalysis?.correlations) {
      review.correlation_validity = this.validateCorrelations(
        analysisResults.execute.statisticalAnalysis.correlations
      );
    }

    // Check trend accuracy
    if (analysisResults.execute?.trendAnalysis) {
      review.trend_accuracy = this.validateTrendAnalysis(
        analysisResults.execute.trendAnalysis
      );
    }

    // Calculate overall confidence
    review.confidence_level = (
      review.statistical_accuracy * 0.25 +
      review.forecast_reliability * 0.25 +
      review.correlation_validity * 0.25 +
      review.trend_accuracy * 0.25
    );

    return review;
  }

  async checkCompleteness(analysisResults) {
    const completeness = {
      score: 0,
      missing_components: [],
      required_analyses: {
        business_context: false,
        kpi_analysis: false,
        statistical_analysis: false,
        visualizations: false,
        recommendations: false
      },
      coverage_assessment: {}
    };

    // Check business context
    if (analysisResults.plan?.businessContext) {
      completeness.required_analyses.business_context = true;
    } else {
      completeness.missing_components.push('Business context analysis');
    }

    // Check KPI analysis
    if (analysisResults.execute?.kpiCalculations) {
      completeness.required_analyses.kpi_analysis = true;
    } else {
      completeness.missing_components.push('KPI calculations');
    }

    // Check statistical analysis
    if (analysisResults.execute?.statisticalAnalysis) {
      completeness.required_analyses.statistical_analysis = true;
    } else {
      completeness.missing_components.push('Statistical analysis');
    }

    // Check visualizations
    if (analysisResults.express?.charts) {
      completeness.required_analyses.visualizations = true;
    } else {
      completeness.missing_components.push('Data visualizations');
    }

    // Check recommendations
    if (analysisResults.execute?.businessInsights || analysisResults.plan?.strategy) {
      completeness.required_analyses.recommendations = true;
    } else {
      completeness.missing_components.push('Business recommendations');
    }

    // Calculate completeness score
    const completedAnalyses = Object.values(completeness.required_analyses).filter(Boolean).length;
    completeness.score = completedAnalyses / Object.keys(completeness.required_analyses).length;

    // Assess coverage
    completeness.coverage_assessment = this.assessAnalysisCoverage(analysisResults);

    return completeness;
  }

  async validateVisualizations(analysisResults) {
    const validation = {
      chart_count: 0,
      chart_types: [],
      visualization_quality: 0,
      accessibility: 0,
      effectiveness: 0,
      issues: []
    };

    if (!analysisResults.express?.charts) {
      validation.issues.push('No visualizations generated');
      return validation;
    }

    const charts = analysisResults.express.charts;
    validation.chart_count = charts.length;
    validation.chart_types = [...new Set(charts.map(chart => chart.type))];

    // Assess visualization quality
    validation.visualization_quality = this.assessVisualizationQuality(charts);
    
    // Check accessibility
    validation.accessibility = this.checkVisualizationAccessibility(charts);
    
    // Evaluate effectiveness
    validation.effectiveness = this.evaluateVisualizationEffectiveness(charts);

    // Identify issues
    if (validation.chart_count < 3) {
      validation.issues.push('Insufficient number of visualizations');
    }
    if (validation.chart_types.length < 2) {
      validation.issues.push('Limited variety in chart types');
    }
    if (validation.visualization_quality < 0.7) {
      validation.issues.push('Visualization quality below standards');
    }

    return validation;
  }

  async calculateQualityScore(dataQuality, accuracy, completeness, visual) {
    const weights = {
      data_quality: 0.3,
      accuracy: 0.3,
      completeness: 0.25,
      visual: 0.15
    };

    const score = (
      dataQuality.overall_score * weights.data_quality +
      accuracy.confidence_level * weights.accuracy +
      completeness.score * weights.completeness +
      visual.visualization_quality * weights.visual
    );

    return Math.max(0, Math.min(1, score));
  }

  async generateQualityRecommendations(dataQuality, accuracy, completeness) {
    const recommendations = [];

    // Data quality recommendations
    if (dataQuality.overall_score < 0.8) {
      recommendations.push({
        category: 'Data Quality',
        priority: 'High',
        recommendation: 'Improve data collection and validation processes',
        details: dataQuality.recommendations
      });
    }

    // Accuracy recommendations
    if (accuracy.confidence_level < 0.7) {
      recommendations.push({
        category: 'Analysis Accuracy',
        priority: 'High',
        recommendation: 'Review and validate analytical methods',
        details: accuracy.issues
      });
    }

    // Completeness recommendations
    if (completeness.score < 0.8) {
      recommendations.push({
        category: 'Analysis Completeness',
        priority: 'Medium',
        recommendation: 'Complete missing analysis components',
        details: completeness.missing_components
      });
    }

    // General recommendations
    recommendations.push({
      category: 'General',
      priority: 'Low',
      recommendation: 'Consider expanding analysis scope for deeper insights',
      details: ['Add industry benchmarking', 'Include competitive analysis', 'Enhance forecasting models']
    });

    return recommendations;
  }

  // Utility methods
  extractDataFromResults(analysisResults) {
    // Try to extract original data from metadata or reconstruct from results
    if (analysisResults.metadata?.originalData) {
      return analysisResults.metadata.originalData;
    }
    
    // If no original data, create synthetic data for validation
    return this.createSyntheticDataForValidation(analysisResults);
  }

  calculateDataCompleteness(data) {
    if (!data || data.length === 0) return 0;
    
    const totalCells = data.length * Object.keys(data[0]).length;
    let filledCells = 0;
    
    data.forEach(row => {
      Object.values(row).forEach(value => {
        if (value !== null && value !== undefined && value !== '') {
          filledCells++;
        }
      });
    });
    
    return filledCells / totalCells;
  }

  checkDataConsistency(data) {
    if (!data || data.length === 0) return 0;
    
    let consistencyScore = 1;
    const columns = Object.keys(data[0]);
    
    columns.forEach(column => {
      const values = data.map(row => row[column]);
      const types = [...new Set(values.map(v => typeof v))];
      
      // Penalize columns with mixed data types
      if (types.length > 2) {
        consistencyScore -= 0.1;
      }
    });
    
    return Math.max(0, consistencyScore);
  }

  detectOutliers(data) {
    const outliers = [];
    const numericColumns = Object.keys(data[0]).filter(key => 
      data.some(row => typeof row[key] === 'number')
    );
    
    numericColumns.forEach(column => {
      const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
      const mean = values.reduce((a, b) => a + b, 0) / values.length;
      const stdDev = Math.sqrt(values.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / values.length);
      
      values.forEach((value, index) => {
        if (Math.abs(value - mean) > 2 * stdDev) {
          outliers.push({
            column,
            index,
            value,
            standardDeviations: Math.abs(value - mean) / stdDev
          });
        }
      });
    });
    
    return outliers;
  }

  analyzeMissingValues(data) {
    const missing = {};
    const columns = Object.keys(data[0]);
    
    columns.forEach(column => {
      const missingCount = data.filter(row => 
        row[column] === null || row[column] === undefined || row[column] === ''
      ).length;
      
      missing[column] = {
        count: missingCount,
        percentage: (missingCount / data.length) * 100
      };
    });
    
    return missing;
  }

  validateDataTypes(data) {
    const validation = {};
    const columns = Object.keys(data[0]);
    
    columns.forEach(column => {
      const types = [...new Set(data.map(row => typeof row[column]))];
      const values = data.map(row => row[column]);
      
      validation[column] = {
        detected_types: types,
        is_consistent: types.length <= 2, // Allow null/undefined as second type
        sample_values: values.slice(0, 5),
        recommended_type: this.getRecommendedType(values)
      };
    });
    
    return validation;
  }

  validateStatisticalCalculations(statisticalAnalysis) {
    let score = 1;
    
    // Check if basic statistics are present
    if (!statisticalAnalysis.descriptive_stats) {
      score -= 0.3;
    }
    
    // Validate correlation values
    if (statisticalAnalysis.correlations) {
      statisticalAnalysis.correlations.forEach(corr => {
        if (Math.abs(corr.correlation) > 1) {
          score -= 0.2; // Invalid correlation value
        }
      });
    }
    
    return Math.max(0, score);
  }

  validateKPICalculations(kpiCalculations) {
    const validation = {};
    
    Object.entries(kpiCalculations).forEach(([kpi, calculation]) => {
      validation[kpi] = {
        has_current_value: calculation.current_value !== undefined,
        has_trend: calculation.trend !== undefined,
        has_growth_rate: calculation.growth_rate !== undefined,
        values_are_numeric: typeof calculation.current_value === 'number',
        is_valid: true
      };
      
      // Check for invalid values
      if (isNaN(calculation.current_value) || !isFinite(calculation.current_value)) {
        validation[kpi].is_valid = false;
      }
    });
    
    return validation;
  }

  assessForecastReliability(forecasts) {
    if (!forecasts.forecasts || forecasts.forecasts.length === 0) {
      return 0;
    }
    
    let totalReliability = 0;
    
    forecasts.forecasts.forEach(forecast => {
      let reliability = forecast.confidence_score || 0.5;
      
      // Adjust based on forecast characteristics
      if (forecast.trend === 'stable') {
        reliability += 0.1; // Stable trends are more predictable
      }
      
      if (forecast.growth_rate && Math.abs(forecast.growth_rate) > 1) {
        reliability -= 0.2; // Extreme growth rates are less reliable
      }
      
      totalReliability += Math.max(0, Math.min(1, reliability));
    });
    
    return totalReliability / forecasts.forecasts.length;
  }

  validateCorrelations(correlations) {
    if (!correlations || correlations.length === 0) {
      return 0;
    }
    
    let validCorrelations = 0;
    
    correlations.forEach(corr => {
      if (Math.abs(corr.correlation) <= 1 && !isNaN(corr.correlation)) {
        validCorrelations++;
      }
    });
    
    return validCorrelations / correlations.length;
  }

  validateTrendAnalysis(trendAnalysis) {
    if (!trendAnalysis || Object.keys(trendAnalysis).length === 0) {
      return 0;
    }
    
    let validTrends = 0;
    const trends = Object.values(trendAnalysis);
    
    trends.forEach(trend => {
      if (trend.direction && ['increasing', 'decreasing', 'stable'].includes(trend.direction)) {
        validTrends++;
      }
    });
    
    return validTrends / trends.length;
  }

  assessAnalysisCoverage(analysisResults) {
    const coverage = {
      business_understanding: 0,
      data_exploration: 0,
      pattern_recognition: 0,
      predictive_analysis: 0,
      actionable_insights: 0
    };
    
    // Business understanding
    if (analysisResults.plan?.businessContext) {
      coverage.business_understanding = 0.8;
    }
    
    // Data exploration
    if (analysisResults.execute?.statisticalAnalysis) {
      coverage.data_exploration = 0.9;
    }
    
    // Pattern recognition
    if (analysisResults.execute?.trendAnalysis) {
      coverage.pattern_recognition = 0.7;
    }
    
    // Predictive analysis
    if (analysisResults.execute?.forecasts) {
      coverage.predictive_analysis = 0.8;
    }
    
    // Actionable insights
    if (analysisResults.execute?.businessInsights) {
      coverage.actionable_insights = 0.7;
    }
    
    return coverage;
  }

  assessVisualizationQuality(charts) {
    if (!charts || charts.length === 0) return 0;
    
    let qualityScore = 0;
    
    charts.forEach(chart => {
      let chartScore = 0.5; // Base score
      
      // Check for title and subtitle
      if (chart.title) chartScore += 0.1;
      if (chart.subtitle) chartScore += 0.1;
      
      // Check for appropriate chart type
      if (chart.type && ['bar', 'line', 'area', 'scatter', 'pie'].includes(chart.type)) {
        chartScore += 0.1;
      }
      
      // Check for data presence
      if (chart.data && chart.data.length > 0) {
        chartScore += 0.2;
      }
      
      // Check for insights
      if (chart.insights && chart.insights.length > 0) {
        chartScore += 0.1;
      }
      
      qualityScore += Math.min(1, chartScore);
    });
    
    return qualityScore / charts.length;
  }

  checkVisualizationAccessibility(charts) {
    // Basic accessibility checks
    let accessibilityScore = 0.8; // Base score assuming good practices
    
    charts.forEach(chart => {
      // Check for color variety (simplified check)
      if (chart.config && chart.config.lines && chart.config.lines.length > 5) {
        accessibilityScore -= 0.1; // Too many colors can be confusing
      }
    });
    
    return Math.max(0, accessibilityScore);
  }

  evaluateVisualizationEffectiveness(charts) {
    if (!charts || charts.length === 0) return 0;
    
    let effectiveness = 0;
    
    // Check for variety in chart types
    const chartTypes = [...new Set(charts.map(chart => chart.type))];
    effectiveness += Math.min(0.3, chartTypes.length * 0.1);
    
    // Check for insights per chart
    const chartsWithInsights = charts.filter(chart => chart.insights && chart.insights.length > 0);
    effectiveness += (chartsWithInsights.length / charts.length) * 0.4;
    
    // Check for appropriate data representation
    effectiveness += 0.3; // Assume good practices
    
    return effectiveness;
  }

  identifyIssues(dataQuality, accuracy, completeness) {
    const issues = [];
    
    if (dataQuality.overall_score < 0.7) {
      issues.push({
        type: 'data_quality',
        severity: 'high',
        message: 'Data quality issues detected',
        details: dataQuality.recommendations
      });
    }
    
    if (accuracy.confidence_level < 0.6) {
      issues.push({
        type: 'accuracy',
        severity: 'high',
        message: 'Analysis accuracy concerns',
        details: accuracy.issues
      });
    }
    
    if (completeness.score < 0.7) {
      issues.push({
        type: 'completeness',
        severity: 'medium',
        message: 'Analysis incomplete',
        details: completeness.missing_components
      });
    }
    
    return issues;
  }

  generateWarnings(qualityScore) {
    const warnings = [];
    
    if (qualityScore < 0.5) {
      warnings.push('Critical: Analysis quality below acceptable standards');
    } else if (qualityScore < 0.7) {
      warnings.push('Warning: Analysis quality could be improved');
    }
    
    return warnings;
  }

  createSyntheticDataForValidation(analysisResults) {
    // Create minimal synthetic data for validation purposes
    const syntheticData = [];
    
    for (let i = 0; i < 10; i++) {
      syntheticData.push({
        id: i,
        value: Math.random() * 100,
        category: `Category ${i % 3}`,
        date: new Date(2023, i % 12, 1).toISOString()
      });
    }
    
    return syntheticData;
  }

  getRecommendedType(values) {
    const numericValues = values.filter(v => typeof v === 'number' && !isNaN(v));
    const stringValues = values.filter(v => typeof v === 'string');
    
    if (numericValues.length > stringValues.length) {
      return 'number';
    } else {
      return 'string';
    }
  }

  calculateConfidence(qualityScore) {
    return Math.max(0.5, qualityScore);
  }

  getReviewDuration() {
    return '2-3 minutes';
  }

  getStatus() {
    return {
      agentId: this.agentId,
      status: this.status,
      pattern: this.pattern,
      capabilities: this.capabilities,
      last_execution: this.lastExecution || null
    };
  }
}

module.exports = ReviewerAgent;
