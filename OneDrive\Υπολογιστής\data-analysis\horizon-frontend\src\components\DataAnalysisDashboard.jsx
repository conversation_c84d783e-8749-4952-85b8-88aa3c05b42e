import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Heading,
  Text,
  useColorModeValue,
  SimpleGrid,
  Flex,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  Progress,
  Badge,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
} from '@chakra-ui/react';
import FileUpload from './FileUpload';
import AgentStatus from './AgentStatus';
import AnalysisResults from './AnalysisResults';
import BusinessReport from './BusinessReport';

const DataAnalysisDashboard = () => {
  const [analysisData, setAnalysisData] = useState(null);
  const [agentStates, setAgentStates] = useState({});
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const bgColor = useColorModeValue('white', 'navy.700');
  const textColor = useColorModeValue('secondaryGray.900', 'white');

  const handleFileUpload = async (file) => {
    try {
      setUploadedFile(file);
      setIsAnalyzing(true);
      
      // Here we'll integrate with the backend API
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch('http://localhost:5000/api/upload', {
        method: 'POST',
        body: formData,
      });
      
      const result = await response.json();
      
      if (result.success) {
        // Start agent processing
        await startAgentAnalysis(result.data.fileId);
      }
    } catch (error) {
      console.error('Upload failed:', error);
      setIsAnalyzing(false);
    }
  };

  const startAgentAnalysis = async (fileId) => {
    try {
      // Simulate agent swarm processing
      const agents = ['executor', 'expresser', 'reviewer', 'datafining', 'storyteller'];
      
      for (const agentType of agents) {
        setAgentStates(prev => ({
          ...prev,
          [agentType]: { status: 'running', progress: 0 }
        }));

        // Execute agent
        const response = await fetch('http://localhost:5000/api/agents/execute', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: agentType,
            data: { fileId },
            context: { businessDomain: 'general' },
            options: { includeRecommendations: true }
          }),
        });

        const result = await response.json();
        
        setAgentStates(prev => ({
          ...prev,
          [agentType]: { 
            status: result.success ? 'completed' : 'failed', 
            progress: 100,
            result: result.data?.result 
          }
        }));
      }
      
      setIsAnalyzing(false);
    } catch (error) {
      console.error('Agent analysis failed:', error);
      setIsAnalyzing(false);
    }
  };

  return (
    <Box pt="80px" px="20px">
      {/* Header */}
      <Box mb="20px">
        <Heading size="lg" color={textColor} mb="4px">
          AI Data Analysis Platform
        </Heading>
        <Text color="secondaryGray.600" fontSize="md" me="6px" mb="5px">
          Transform your business data into actionable insights with DeepSeek AI agents
        </Text>
      </Box>

      {/* Upload Section */}
      <Grid templateColumns="1fr" gap="20px" mb="20px">
        <Box
          bg={bgColor}
          borderRadius="20px"
          p="20px"
          boxShadow="0px 18px 40px rgba(112, 144, 176, 0.12)"
        >
          <Heading size="md" mb="15px" color={textColor}>
            Upload Your Data
          </Heading>
          <FileUpload onFileUpload={handleFileUpload} isLoading={isAnalyzing} />
        </Box>
      </Grid>

      {/* Agent Status Section */}
      {(uploadedFile || Object.keys(agentStates).length > 0) && (
        <Grid templateColumns="1fr" gap="20px" mb="20px">
          <Box
            bg={bgColor}
            borderRadius="20px"
            p="20px"
            boxShadow="0px 18px 40px rgba(112, 144, 176, 0.12)"
          >
            <Heading size="md" mb="15px" color={textColor}>
              AI Agent Swarm Processing
            </Heading>
            <AgentStatus agentStates={agentStates} />
          </Box>
        </Grid>
      )}

      {/* Analysis Results */}
      {!isAnalyzing && Object.keys(agentStates).length > 0 && (
        <Grid templateColumns={{ base: "1fr", lg: "2fr 1fr" }} gap="20px" mb="20px">
          <Box
            bg={bgColor}
            borderRadius="20px"
            p="20px"
            boxShadow="0px 18px 40px rgba(112, 144, 176, 0.12)"
          >
            <Heading size="md" mb="15px" color={textColor}>
              Analysis Results & Visualizations
            </Heading>
            <AnalysisResults data={analysisData} agentResults={agentStates} />
          </Box>
          
          <Box
            bg={bgColor}
            borderRadius="20px"
            p="20px"
            boxShadow="0px 18px 40px rgba(112, 144, 176, 0.12)"
          >
            <Heading size="md" mb="15px" color={textColor}>
              Business Intelligence Report
            </Heading>
            <BusinessReport agentResults={agentStates} />
          </Box>
        </Grid>
      )}
    </Box>
  );
};

export default DataAnalysisDashboard;
