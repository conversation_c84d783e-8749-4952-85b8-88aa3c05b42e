{"name": "horizon-ui-chakra", "version": "2.0.0", "private": true, "dependencies": {"@babel/preset-react": "^7.18.6", "@chakra-ui/icons": "^2.0.19", "@chakra-ui/react": "2.6.1", "@chakra-ui/system": "2.5.7", "@chakra-ui/theme-tools": "^1.3.6", "@emotion/cache": "^11.12.0", "@emotion/react": "^11.12.0", "@emotion/styled": "^11.12.0", "@tanstack/react-table": "^8.19.3", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "ajv": "^8.17.1", "apexcharts": "3.50.0", "framer-motion": "^11.3.7", "npm-force-resolutions": "^0.0.10", "react": "19.0.0", "react-apexcharts": "1.4.1", "react-calendar": "^5.0.0", "react-custom-scrollbars-2": "^4.5.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-icons": "^5.2.1", "react-is": "^18.3.1", "react-router-dom": "^6.25.1", "react-scripts": "^5.0.1", "recharts": "^2.15.3", "stylis": "^4.3.2", "stylis-plugin-rtl": "^2.0.2", "web-vitals": "^4.2.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "predeploy": "npm run build", "deploy": "gh-pages -d build", "preinstall": "npx npm-force-resolutions"}, "resolutions": {"react-error-overlay": "6.0.9"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"anymatch": "^3.1.3", "chokidar": "^3.6.0", "gh-pages": "^6.1.1", "micromatch": "^4.0.7"}}