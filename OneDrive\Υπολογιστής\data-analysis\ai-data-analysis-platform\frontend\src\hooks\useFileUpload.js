import { useState, useCallback, useRef } from 'react';
import api from '../services/api';

/**
 * Custom hook for handling file uploads
 */
export const useFileUpload = () => {
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [uploadError, setUploadError] = useState(null);
  const abortControllerRef = useRef(null);

  // Upload single file
  const uploadFile = useCallback(async (file, options = {}) => {
    try {
      setUploading(true);
      setUploadError(null);
      
      // Create abort controller for cancellation
      abortControllerRef.current = new AbortController();
      
      // Create form data
      const formData = new FormData();
      formData.append('file', file);
      
      if (options.category) {
        formData.append('category', options.category);
      }
      
      if (options.tags && options.tags.length > 0) {
        formData.append('tags', JSON.stringify(options.tags));
      }
      
      if (options.metadata) {
        formData.append('metadata', JSON.stringify(options.metadata));
      }

      // Set up progress tracking
      const fileId = `${file.name}-${Date.now()}`;
      setUploadProgress(prev => ({ ...prev, [fileId]: 0 }));

      const response = await api.post('/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        signal: abortControllerRef.current.signal,
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(prev => ({ ...prev, [fileId]: progress }));
        }
      });

      const uploadedFile = response.data.data;
      
      // Add to uploaded files list
      setUploadedFiles(prev => [uploadedFile, ...prev]);
      
      // Clear progress
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[fileId];
        return newProgress;
      });

      return uploadedFile;
    } catch (error) {
      if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
        console.log('Upload cancelled');
        return null;
      }
      
      const errorMessage = error.response?.data?.message || 'Upload failed';
      setUploadError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setUploading(false);
      abortControllerRef.current = null;
    }
  }, []);

  // Upload multiple files
  const uploadFiles = useCallback(async (files, options = {}) => {
    const results = [];
    const errors = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        const result = await uploadFile(file, options);
        if (result) {
          results.push(result);
        }
      } catch (error) {
        errors.push({ file: file.name, error: error.message });
      }
    }

    if (errors.length > 0) {
      console.warn('Some files failed to upload:', errors);
    }

    return { results, errors };
  }, [uploadFile]);

  // Upload files with batch endpoint
  const uploadBatch = useCallback(async (files, options = {}) => {
    try {
      setUploading(true);
      setUploadError(null);

      // Create abort controller
      abortControllerRef.current = new AbortController();

      // Create form data
      const formData = new FormData();
      
      files.forEach((file, index) => {
        formData.append(`files`, file);
      });

      if (options.category) {
        formData.append('category', options.category);
      }
      
      if (options.tags && options.tags.length > 0) {
        formData.append('tags', JSON.stringify(options.tags));
      }

      // Set up progress tracking
      const batchId = `batch-${Date.now()}`;
      setUploadProgress(prev => ({ ...prev, [batchId]: 0 }));

      const response = await api.post('/upload/batch', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        signal: abortControllerRef.current.signal,
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(prev => ({ ...prev, [batchId]: progress }));
        }
      });

      const uploadedFiles = response.data.data.files;
      
      // Add to uploaded files list
      setUploadedFiles(prev => [...uploadedFiles, ...prev]);
      
      // Clear progress
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[batchId];
        return newProgress;
      });

      return {
        files: uploadedFiles,
        successCount: response.data.data.successCount,
        errorCount: response.data.data.errorCount,
        errors: response.data.data.errors || []
      };
    } catch (error) {
      if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
        console.log('Batch upload cancelled');
        return null;
      }
      
      const errorMessage = error.response?.data?.message || 'Batch upload failed';
      setUploadError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setUploading(false);
      abortControllerRef.current = null;
    }
  }, []);

  // Cancel upload
  const cancelUpload = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setUploading(false);
      setUploadProgress({});
    }
  }, []);

  // Fetch uploaded files
  const fetchUploadedFiles = useCallback(async (filters = {}) => {
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value);
        }
      });

      const response = await api.get(`/upload?${queryParams.toString()}`);
      const files = response.data.data.files || [];
      
      setUploadedFiles(files);
      return response.data.data;
    } catch (error) {
      console.error('Failed to fetch uploaded files:', error);
      throw error;
    }
  }, []);

  // Get file info
  const getFileInfo = useCallback(async (fileId) => {
    try {
      const response = await api.get(`/upload/${fileId}/info`);
      return response.data.data;
    } catch (error) {
      console.error('Failed to get file info:', error);
      throw error;
    }
  }, []);

  // Delete file
  const deleteFile = useCallback(async (fileId) => {
    try {
      await api.delete(`/upload/${fileId}`);
      
      // Remove from uploaded files list
      setUploadedFiles(prev => prev.filter(file => file.fileId !== fileId));
      
      return true;
    } catch (error) {
      console.error('Failed to delete file:', error);
      throw error;
    }
  }, []);

  // Delete multiple files
  const deleteFiles = useCallback(async (fileIds) => {
    const results = [];
    
    for (const fileId of fileIds) {
      try {
        await deleteFile(fileId);
        results.push({ fileId, success: true });
      } catch (error) {
        results.push({ fileId, success: false, error: error.message });
      }
    }
    
    return results;
  }, [deleteFile]);

  // Validate file
  const validateFile = useCallback(async (fileId) => {
    try {
      const response = await api.post(`/upload/${fileId}/validate`);
      return response.data.data;
    } catch (error) {
      console.error('Failed to validate file:', error);
      throw error;
    }
  }, []);

  // Get upload statistics
  const getUploadStats = useCallback(() => {
    const total = uploadedFiles.length;
    const totalSize = uploadedFiles.reduce((sum, file) => sum + (file.size || 0), 0);
    
    const fileTypes = uploadedFiles.reduce((acc, file) => {
      const type = file.type || 'unknown';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    const categories = uploadedFiles.reduce((acc, file) => {
      const category = file.category || 'uncategorized';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    return {
      total,
      totalSize,
      averageSize: total > 0 ? totalSize / total : 0,
      fileTypes,
      categories,
      recentUploads: uploadedFiles.slice(0, 5)
    };
  }, [uploadedFiles]);

  // Filter files
  const filterFiles = useCallback((filters) => {
    let filtered = [...uploadedFiles];

    if (filters.type) {
      filtered = filtered.filter(file => file.type === filters.type);
    }

    if (filters.category) {
      filtered = filtered.filter(file => file.category === filters.category);
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(file => 
        file.originalName.toLowerCase().includes(searchTerm) ||
        (file.tags || []).some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      filtered = filtered.filter(file => new Date(file.uploadedAt) >= fromDate);
    }

    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo);
      filtered = filtered.filter(file => new Date(file.uploadedAt) <= toDate);
    }

    return filtered;
  }, [uploadedFiles]);

  // Get file by ID
  const getFileById = useCallback((fileId) => {
    return uploadedFiles.find(file => file.fileId === fileId);
  }, [uploadedFiles]);

  // Clear upload error
  const clearError = useCallback(() => {
    setUploadError(null);
  }, []);

  return {
    // State
    uploadedFiles,
    uploading,
    uploadProgress,
    uploadError,
    
    // Upload operations
    uploadFile,
    uploadFiles,
    uploadBatch,
    cancelUpload,
    
    // File management
    fetchUploadedFiles,
    getFileInfo,
    deleteFile,
    deleteFiles,
    validateFile,
    
    // Utilities
    getUploadStats,
    filterFiles,
    getFileById,
    clearError,
    
    // Computed properties
    hasUploadedFiles: uploadedFiles.length > 0,
    totalUploadedFiles: uploadedFiles.length,
    isUploading: uploading,
    hasUploadError: !!uploadError,
    uploadProgressValues: Object.values(uploadProgress),
    averageUploadProgress: Object.values(uploadProgress).length > 0 
      ? Object.values(uploadProgress).reduce((sum, val) => sum + val, 0) / Object.values(uploadProgress).length 
      : 0
  };
};

export default useFileUpload;
