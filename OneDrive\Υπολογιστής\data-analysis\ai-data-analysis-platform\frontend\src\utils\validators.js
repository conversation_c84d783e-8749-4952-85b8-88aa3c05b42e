/**
 * Validation utilities for frontend forms and data
 */

// Email validation
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Password validation
export const validatePassword = (password) => {
  const errors = [];
  
  if (!password) {
    errors.push('Password is required');
    return { isValid: false, errors };
  }

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    isValid: errors.length === 0,
    errors,
    strength: calculatePasswordStrength(password)
  };
};

// Calculate password strength
const calculatePasswordStrength = (password) => {
  let score = 0;
  
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/[a-z]/.test(password)) score += 1;
  if (/[0-9]/.test(password)) score += 1;
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;
  if (password.length >= 16) score += 1;

  if (score <= 2) return 'weak';
  if (score <= 4) return 'medium';
  if (score <= 6) return 'strong';
  return 'very-strong';
};

// File validation
export const validateFile = (file, options = {}) => {
  const errors = [];
  
  const {
    maxSize = 50 * 1024 * 1024, // 50MB default
    allowedTypes = [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/json',
      'text/plain'
    ],
    required = false
  } = options;

  if (required && !file) {
    errors.push('File is required');
    return { isValid: false, errors };
  }

  if (!file) {
    return { isValid: true, errors: [] };
  }

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size must be less than ${formatFileSize(maxSize)}`);
  }

  // Check file type
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    const allowedExtensions = allowedTypes.map(type => getFileExtension(type)).join(', ');
    errors.push(`File type not supported. Allowed types: ${allowedExtensions}`);
  }

  // Check for empty files
  if (file.size === 0) {
    errors.push('File cannot be empty');
  }

  return {
    isValid: errors.length === 0,
    errors,
    fileInfo: {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    }
  };
};

// Get file extension from MIME type
const getFileExtension = (mimeType) => {
  const typeMap = {
    'text/csv': 'CSV',
    'application/vnd.ms-excel': 'XLS',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'XLSX',
    'application/json': 'JSON',
    'text/plain': 'TXT'
  };
  return typeMap[mimeType] || mimeType;
};

// Format file size helper
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// URL validation
export const isValidURL = (url) => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Phone number validation (basic)
export const isValidPhone = (phone) => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
};

// Required field validation
export const isRequired = (value, fieldName = 'Field') => {
  if (value === null || value === undefined || value === '') {
    return { isValid: false, error: `${fieldName} is required` };
  }
  return { isValid: true, error: null };
};

// Minimum length validation
export const minLength = (value, min, fieldName = 'Field') => {
  if (!value || value.length < min) {
    return { 
      isValid: false, 
      error: `${fieldName} must be at least ${min} characters long` 
    };
  }
  return { isValid: true, error: null };
};

// Maximum length validation
export const maxLength = (value, max, fieldName = 'Field') => {
  if (value && value.length > max) {
    return { 
      isValid: false, 
      error: `${fieldName} must be no more than ${max} characters long` 
    };
  }
  return { isValid: true, error: null };
};

// Number validation
export const isValidNumber = (value, options = {}) => {
  const { min, max, integer = false, positive = false } = options;
  
  const num = parseFloat(value);
  
  if (isNaN(num)) {
    return { isValid: false, error: 'Must be a valid number' };
  }

  if (integer && !Number.isInteger(num)) {
    return { isValid: false, error: 'Must be a whole number' };
  }

  if (positive && num < 0) {
    return { isValid: false, error: 'Must be a positive number' };
  }

  if (min !== undefined && num < min) {
    return { isValid: false, error: `Must be at least ${min}` };
  }

  if (max !== undefined && num > max) {
    return { isValid: false, error: `Must be no more than ${max}` };
  }

  return { isValid: true, error: null };
};

// Date validation
export const isValidDate = (date, options = {}) => {
  const { min, max, future = false, past = false } = options;
  
  const dateObj = new Date(date);
  
  if (isNaN(dateObj.getTime())) {
    return { isValid: false, error: 'Must be a valid date' };
  }

  const now = new Date();
  
  if (future && dateObj <= now) {
    return { isValid: false, error: 'Must be a future date' };
  }

  if (past && dateObj >= now) {
    return { isValid: false, error: 'Must be a past date' };
  }

  if (min && dateObj < new Date(min)) {
    return { isValid: false, error: `Must be after ${new Date(min).toLocaleDateString()}` };
  }

  if (max && dateObj > new Date(max)) {
    return { isValid: false, error: `Must be before ${new Date(max).toLocaleDateString()}` };
  }

  return { isValid: true, error: null };
};

// Form validation helper
export const validateForm = (data, rules) => {
  const errors = {};
  let isValid = true;

  Object.keys(rules).forEach(field => {
    const value = data[field];
    const fieldRules = Array.isArray(rules[field]) ? rules[field] : [rules[field]];
    
    for (const rule of fieldRules) {
      const result = rule(value);
      if (!result.isValid) {
        errors[field] = result.error;
        isValid = false;
        break; // Stop at first error for this field
      }
    }
  });

  return { isValid, errors };
};

// Analysis configuration validation
export const validateAnalysisConfig = (config) => {
  const errors = [];

  if (!config.fileIds || config.fileIds.length === 0) {
    errors.push('At least one file must be selected');
  }

  if (!config.analysisType) {
    errors.push('Analysis type is required');
  }

  if (config.agents && config.agents.length === 0) {
    errors.push('At least one agent must be selected');
  }

  // Validate agent-specific settings
  if (config.settings) {
    if (config.settings.maxDuration && config.settings.maxDuration < 60000) {
      errors.push('Maximum duration must be at least 1 minute');
    }

    if (config.settings.confidence && (config.settings.confidence < 0 || config.settings.confidence > 1)) {
      errors.push('Confidence level must be between 0 and 1');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Agent execution validation
export const validateAgentExecution = (type, data, options = {}) => {
  const errors = [];

  if (!type) {
    errors.push('Agent type is required');
  }

  const validTypes = ['executor', 'expresser', 'reviewer', 'datafining', 'storyteller'];
  if (type && !validTypes.includes(type)) {
    errors.push('Invalid agent type');
  }

  if (!data) {
    errors.push('Data is required for agent execution');
  }

  // Type-specific validations
  switch (type) {
    case 'executor':
      if (data && !data.dataset && !data.fileId) {
        errors.push('Executor agent requires dataset or file ID');
      }
      break;
    
    case 'expresser':
      if (data && !data.analysisResult) {
        errors.push('Expresser agent requires analysis result');
      }
      break;
    
    case 'reviewer':
      if (data && !data.analysisResult && !data.reportContent) {
        errors.push('Reviewer agent requires analysis result or report content');
      }
      break;

    case 'datafining':
      if (data && !data.rawData && !data.fileId) {
        errors.push('Data Fining agent requires raw data or file ID');
      }
      break;

    case 'storyteller':
      if (data && !data.insights && !data.analysisResult) {
        errors.push('Storyteller agent requires insights or analysis result');
      }
      break;
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Sanitize input to prevent XSS
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  return input
    .replace(/[<>]/g, '') // Remove < and >
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
};

// Validate JSON string
export const isValidJSON = (str) => {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
};

// Custom validation rule builder
export const createValidationRule = (validator, errorMessage) => {
  return (value) => {
    const isValid = validator(value);
    return {
      isValid,
      error: isValid ? null : errorMessage
    };
  };
};

// Common validation rules
export const validationRules = {
  required: (fieldName) => (value) => isRequired(value, fieldName),
  email: createValidationRule(isValidEmail, 'Must be a valid email address'),
  url: createValidationRule(isValidURL, 'Must be a valid URL'),
  phone: createValidationRule(isValidPhone, 'Must be a valid phone number'),
  minLength: (min, fieldName) => (value) => minLength(value, min, fieldName),
  maxLength: (max, fieldName) => (value) => maxLength(value, max, fieldName),
  number: (options) => (value) => isValidNumber(value, options),
  date: (options) => (value) => isValidDate(value, options),
  password: (value) => {
    const result = validatePassword(value);
    return {
      isValid: result.isValid,
      error: result.errors.join(', ')
    };
  },
  file: (options) => (file) => validateFile(file, options)
};

export default {
  isValidEmail,
  validatePassword,
  validateFile,
  isValidURL,
  isValidPhone,
  isRequired,
  minLength,
  maxLength,
  isValidNumber,
  isValidDate,
  validateForm,
  validateAnalysisConfig,
  validateAgentExecution,
  sanitizeInput,
  isValidJSON,
  createValidationRule,
  validationRules
};
