import React, { useState, useCallback } from 'react';
import {
  Box,
  VStack,
  HStack,
  Button,
  Text,
  Progress,
  Alert,
  AlertIcon,
  Grid,
  GridItem,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Badge,
  IconButton,
  Tooltip,
  Select,
  Input,
  Textarea,
  FormControl,
  FormLabel,
  FormHelperText,
  Divider,
  List,
  ListItem,
  ListIcon
} from '@chakra-ui/react';
import { 
  FiUpload, 
  FiX, 
  FiFile, 
  FiCheckCircle,
  FiAlertCircle,
  FiTrash2,
  FiInfo
} from 'react-icons/fi';
import { useFileUpload } from '../hooks/useFileUpload';
import { formatFileSize, formatDate } from '../utils/formatters';

const FileUpload = ({ onUploadSuccess, onError }) => {
  const {
    uploadedFiles,
    uploading,
    uploadProgress,
    uploadError,
    uploadFile,
    uploadBatch,
    cancelUpload,
    deleteFile,
    getFileInfo,
    clearError,
    hasUploadedFiles,
    getUploadStats
  } = useFileUpload();
  const [dragActive, setDragActive] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploadOptions, setUploadOptions] = useState({
    category: 'business_data',
    tags: '',
    description: ''
  });
  const [showUploadOptions, setShowUploadOptions] = useState(false);

  // Simple toast replacement
  const showMessage = (message, type = 'info') => {
    if (type === 'success' && onUploadSuccess) {
      onUploadSuccess(message);
    } else if (type === 'error' && onError) {
      onError(message);
    }
    console.log(`[${type.toUpperCase()}]: ${message}`);
  };

  // Supported file types
  const SUPPORTED_TYPES = {
    'text/csv': 'CSV',
    'application/vnd.ms-excel': 'Excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',
    'application/json': 'JSON',
    'text/plain': 'Text'
  };

  const CATEGORIES = [
    { value: 'business_data', label: 'Business Data' },
    { value: 'financial_data', label: 'Financial Data' },
    { value: 'sales_data', label: 'Sales Data' },
    { value: 'customer_data', label: 'Customer Data' },
    { value: 'operational_data', label: 'Operational Data' },
    { value: 'other', label: 'Other' }
  ];

  // Handle file selection
  const handleFileSelect = useCallback((files) => {
    const validFiles = Array.from(files).filter(file => {
      if (!SUPPORTED_TYPES[file.type]) {
        toast({
          title: 'Unsupported File Type',
          description: `${file.name} is not a supported file type`,
          status: 'warning',
          duration: 3000,
          isClosable: true
        });
        return false;
      }

      if (file.size > 50 * 1024 * 1024) { // 50MB limit
        toast({
          title: 'File Too Large',
          description: `${file.name} exceeds the 50MB limit`,
          status: 'warning',
          duration: 3000,
          isClosable: true
        });
        return false;
      }

      return true;
    });

    setSelectedFiles(prev => [...prev, ...validFiles]);
  }, [toast]);

  // Handle drag events
  const handleDrag = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  }, [handleFileSelect]);

  // Handle file input change
  const handleInputChange = useCallback((e) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files);
    }
  }, [handleFileSelect]);

  // Remove selected file
  const removeSelectedFile = useCallback((index) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  }, []);

  // Upload files
  const handleUpload = useCallback(async () => {
    if (selectedFiles.length === 0) return;

    try {
      clearError();

      const options = {
        category: uploadOptions.category,
        tags: uploadOptions.tags ? uploadOptions.tags.split(',').map(tag => tag.trim()) : [],
        metadata: {
          description: uploadOptions.description,
          uploadedAt: new Date().toISOString()
        }
      };

      let result;
      if (selectedFiles.length === 1) {
        result = await uploadFile(selectedFiles[0], options);
        if (result) {
          onUploadSuccess && onUploadSuccess([result]);
        }
      } else {
        result = await uploadBatch(selectedFiles, options);
        if (result) {
          onUploadSuccess && onUploadSuccess(result.files);
          
          if (result.errorCount > 0) {
            toast({
              title: 'Partial Upload Success',
              description: `${result.successCount} files uploaded, ${result.errorCount} failed`,
              status: 'warning',
              duration: 5000,
              isClosable: true
            });
          }
        }
      }

      // Clear selected files on successful upload
      setSelectedFiles([]);
      setShowUploadOptions(false);
      
    } catch (error) {
      onError && onError(error);
    }
  }, [selectedFiles, uploadOptions, uploadFile, uploadBatch, clearError, onUploadSuccess, onError, toast]);

  // Cancel upload
  const handleCancel = useCallback(() => {
    cancelUpload();
    setSelectedFiles([]);
  }, [cancelUpload]);

  // Delete uploaded file
  const handleDeleteFile = useCallback(async (fileId) => {
    try {
      await deleteFile(fileId);
      toast({
        title: 'File Deleted',
        description: 'File has been successfully deleted',
        status: 'success',
        duration: 3000,
        isClosable: true
      });
    } catch (error) {
      toast({
        title: 'Delete Failed',
        description: error.message || 'Failed to delete file',
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    }
  }, [deleteFile, toast]);

  const uploadStats = getUploadStats();

  return (
    <VStack spacing={6} align="stretch">
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <Heading size="md">Upload Data Files</Heading>
        </CardHeader>
        <CardBody>
          <VStack spacing={4}>
            {/* Drag and Drop Area */}
            <Box
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              border="2px dashed"
              borderColor={dragActive ? 'blue.500' : 'gray.300'}
              borderRadius="md"
              p={8}
              textAlign="center"
              bg={dragActive ? 'blue.50' : 'gray.50'}
              cursor="pointer"
              transition="all 0.2s"
              _hover={{ borderColor: 'blue.400', bg: 'blue.50' }}
            >
              <input
                type="file"
                id="file-upload"
                multiple
                accept=".csv,.xlsx,.xls,.json,.txt"
                onChange={handleInputChange}
                style={{ display: 'none' }}
              />
              <VStack spacing={3}>
                <FiUpload size={40} color="#3182CE" />
                <Text fontSize="lg" fontWeight="medium">
                  Drop files here or click to browse
                </Text>
                <Text fontSize="sm" color="gray.600">
                  Supported formats: CSV, Excel, JSON, TXT (Max 50MB each)
                </Text>
                <Button
                  as="label"
                  htmlFor="file-upload"
                  colorScheme="blue"
                  size="sm"
                  cursor="pointer"
                >
                  Browse Files
                </Button>
              </VStack>
            </Box>

            {/* Selected Files */}
            {selectedFiles.length > 0 && (
              <Box w="full">
                <Text fontWeight="medium" mb={3}>
                  Selected Files ({selectedFiles.length})
                </Text>
                <VStack spacing={2} align="stretch">
                  {selectedFiles.map((file, index) => (
                    <HStack key={index} p={3} bg="gray.50" borderRadius="md">
                      <FiFile />
                      <VStack align="start" spacing={0} flex={1}>
                        <Text fontSize="sm" fontWeight="medium">
                          {file.name}
                        </Text>
                        <Text fontSize="xs" color="gray.600">
                          {formatFileSize(file.size)} • {SUPPORTED_TYPES[file.type]}
                        </Text>
                      </VStack>
                      <IconButton
                        icon={<FiX />}
                        size="sm"
                        variant="ghost"
                        colorScheme="red"
                        onClick={() => removeSelectedFile(index)}
                        aria-label="Remove file"
                      />
                    </HStack>
                  ))}
                </VStack>

                {/* Upload Options */}
                <Box mt={4}>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowUploadOptions(!showUploadOptions)}
                  >
                    {showUploadOptions ? 'Hide' : 'Show'} Upload Options
                  </Button>
                </Box>

                {showUploadOptions && (
                  <Grid templateColumns="repeat(2, 1fr)" gap={4} mt={4}>
                    <GridItem>
                      <FormControl>
                        <FormLabel fontSize="sm">Category</FormLabel>
                        <Select
                          size="sm"
                          value={uploadOptions.category}
                          onChange={(e) => setUploadOptions(prev => ({
                            ...prev,
                            category: e.target.value
                          }))}
                        >
                          {CATEGORIES.map(cat => (
                            <option key={cat.value} value={cat.value}>
                              {cat.label}
                            </option>
                          ))}
                        </Select>
                      </FormControl>
                    </GridItem>
                    <GridItem>
                      <FormControl>
                        <FormLabel fontSize="sm">Tags</FormLabel>
                        <Input
                          size="sm"
                          placeholder="sales, revenue, quarterly"
                          value={uploadOptions.tags}
                          onChange={(e) => setUploadOptions(prev => ({
                            ...prev,
                            tags: e.target.value
                          }))}
                        />
                        <FormHelperText fontSize="xs">
                          Comma-separated tags
                        </FormHelperText>
                      </FormControl>
                    </GridItem>
                    <GridItem colSpan={2}>
                      <FormControl>
                        <FormLabel fontSize="sm">Description</FormLabel>
                        <Textarea
                          size="sm"
                          placeholder="Brief description of the data..."
                          value={uploadOptions.description}
                          onChange={(e) => setUploadOptions(prev => ({
                            ...prev,
                            description: e.target.value
                          }))}
                          rows={2}
                        />
                      </FormControl>
                    </GridItem>
                  </Grid>
                )}

                {/* Upload Button */}
                <HStack spacing={2} mt={4}>
                  <Button
                    colorScheme="blue"
                    onClick={handleUpload}
                    isLoading={uploading}
                    loadingText="Uploading..."
                    disabled={selectedFiles.length === 0}
                  >
                    Upload {selectedFiles.length} File{selectedFiles.length !== 1 ? 's' : ''}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleCancel}
                    disabled={!uploading}
                  >
                    Cancel
                  </Button>
                </HStack>
              </Box>
            )}

            {/* Upload Progress */}
            {uploading && Object.keys(uploadProgress).length > 0 && (
              <Box w="full">
                <Text fontSize="sm" fontWeight="medium" mb={2}>
                  Upload Progress
                </Text>
                {Object.entries(uploadProgress).map(([key, progress]) => (
                  <Box key={key} mb={2}>
                    <HStack justify="space-between" mb={1}>
                      <Text fontSize="sm">{key}</Text>
                      <Text fontSize="sm">{progress}%</Text>
                    </HStack>
                    <Progress value={progress} colorScheme="blue" size="sm" />
                  </Box>
                ))}
              </Box>
            )}

            {/* Upload Error */}
            {uploadError && (
              <Alert status="error">
                <AlertIcon />
                {uploadError}
              </Alert>
            )}
          </VStack>
        </CardBody>
      </Card>

      {/* Uploaded Files */}
      {hasUploadedFiles && (
        <Card>
          <CardHeader>
            <HStack justify="space-between">
              <Heading size="md">Uploaded Files</Heading>
              <Badge colorScheme="blue">{uploadStats.total} files</Badge>
            </HStack>
          </CardHeader>
          <CardBody>
            <VStack spacing={4} align="stretch">
              {/* Stats */}
              <Grid templateColumns="repeat(auto-fit, minmax(150px, 1fr))" gap={4}>
                <Box p={3} bg="blue.50" borderRadius="md" textAlign="center">
                  <Text fontSize="2xl" fontWeight="bold" color="blue.600">
                    {uploadStats.total}
                  </Text>
                  <Text fontSize="sm" color="blue.600">
                    Total Files
                  </Text>
                </Box>
                <Box p={3} bg="green.50" borderRadius="md" textAlign="center">
                  <Text fontSize="2xl" fontWeight="bold" color="green.600">
                    {formatFileSize(uploadStats.totalSize)}
                  </Text>
                  <Text fontSize="sm" color="green.600">
                    Total Size
                  </Text>
                </Box>
                <Box p={3} bg="purple.50" borderRadius="md" textAlign="center">
                  <Text fontSize="2xl" fontWeight="bold" color="purple.600">
                    {Object.keys(uploadStats.fileTypes).length}
                  </Text>
                  <Text fontSize="sm" color="purple.600">
                    File Types
                  </Text>
                </Box>
              </Grid>

              <Divider />

              {/* Recent Files */}
              <Box>
                <Text fontWeight="medium" mb={3}>Recent Uploads</Text>
                <List spacing={2}>
                  {uploadStats.recentUploads.map((file) => (
                    <ListItem key={file.fileId}>
                      <HStack justify="space-between" p={3} bg="gray.50" borderRadius="md">
                        <HStack>
                          <ListIcon as={FiFile} color="blue.500" />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="medium">
                              {file.originalName}
                            </Text>
                            <HStack spacing={2}>
                              <Text fontSize="xs" color="gray.600">
                                {formatFileSize(file.size)}
                              </Text>
                              <Text fontSize="xs" color="gray.600">
                                {formatDate(file.uploadedAt)}
                              </Text>
                              {file.category && (
                                <Badge size="sm" colorScheme="blue">
                                  {file.category.replace('_', ' ')}
                                </Badge>
                              )}
                            </HStack>
                          </VStack>
                        </HStack>
                        <HStack>
                          <Tooltip label="File Info">
                            <IconButton
                              icon={<FiInfo />}
                              size="sm"
                              variant="ghost"
                              onClick={() => getFileInfo(file.fileId)}
                              aria-label="File info"
                            />
                          </Tooltip>
                          <Tooltip label="Delete File">
                            <IconButton
                              icon={<FiTrash2 />}
                              size="sm"
                              variant="ghost"
                              colorScheme="red"
                              onClick={() => handleDeleteFile(file.fileId)}
                              aria-label="Delete file"
                            />
                          </Tooltip>
                        </HStack>
                      </HStack>
                    </ListItem>
                  ))}
                </List>
              </Box>
            </VStack>
          </CardBody>
        </Card>
      )}
    </VStack>
  );
};

export default FileUpload;
