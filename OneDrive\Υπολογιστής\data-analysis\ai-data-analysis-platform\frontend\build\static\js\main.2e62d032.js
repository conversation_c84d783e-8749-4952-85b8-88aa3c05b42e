/*! For license information please see main.2e62d032.js.LICENSE.txt */
(()=>{var e={10:()=>{},11:(e,t,n)=>{"use strict";var r=n(955),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function l(e){return r.isMemo(e)?s:a[e.$$typeof]||i}a[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[r.Memo]=s;var u=Object.defineProperty,c=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,h=Object.getPrototypeOf,p=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(p){var i=h(n);i&&i!==p&&e(t,i,r)}var s=c(n);d&&(s=s.concat(d(n)));for(var a=l(t),m=l(n),y=0;y<s.length;++y){var g=s[y];if(!o[g]&&(!r||!r[g])&&(!m||!m[g])&&(!a||!a[g])){var v=f(n,g);try{u(t,g,v)}catch(b){}}}}return t}},61:(e,t,n)=>{"use strict";n.d(t,{t:()=>s});var r=n(994),i=n(678),o=n(206),s=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!o.S$&&(null==(t=window)?void 0:t.addEventListener)){var n=function(){return e()};return window.addEventListener("online",n,!1),window.addEventListener("offline",n,!1),function(){window.removeEventListener("online",n),window.removeEventListener("offline",n)}}},t}(0,r.A)(t,e);var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},n.setEventListener=function(e){var t,n=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e((function(e){"boolean"===typeof e?n.setOnline(e):n.onOnline()}))},n.setOnline=function(e){this.online=e,e&&this.onOnline()},n.onOnline=function(){this.listeners.forEach((function(e){e()}))},n.isOnline=function(){return"boolean"===typeof this.online?this.online:"undefined"===typeof navigator||"undefined"===typeof navigator.onLine||navigator.onLine},t}(i.Q))},102:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(null,arguments)}n.d(t,{A:()=>r})},175:(e,t,n)=>{"use strict";n.d(t,{j:()=>i});var r=n(206),i=new(function(){function e(){this.queue=[],this.transactions=0,this.notifyFn=function(e){e()},this.batchNotifyFn=function(e){e()}}var t=e.prototype;return t.batch=function(e){var t;this.transactions++;try{t=e()}finally{this.transactions--,this.transactions||this.flush()}return t},t.schedule=function(e){var t=this;this.transactions?this.queue.push(e):(0,r.G6)((function(){t.notifyFn(e)}))},t.batchCalls=function(e){var t=this;return function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];t.schedule((function(){e.apply(void 0,r)}))}},t.flush=function(){var e=this,t=this.queue;this.queue=[],t.length&&(0,r.G6)((function(){e.batchNotifyFn((function(){t.forEach((function(t){e.notifyFn(t)}))}))}))},t.setNotifyFunction=function(e){this.notifyFn=e},t.setBatchNotifyFunction=function(e){this.batchNotifyFn=e},e}())},206:(e,t,n)=>{"use strict";n.d(t,{BH:()=>v,Cp:()=>y,F$:()=>p,G6:()=>k,HN:()=>l,MK:()=>f,Od:()=>m,S$:()=>i,Zw:()=>s,b_:()=>d,f8:()=>b,gn:()=>a,j3:()=>u,jY:()=>C,lQ:()=>o,nJ:()=>h,vh:()=>c,yy:()=>E});var r=n(102),i="undefined"===typeof window;function o(){}function s(e,t){return"function"===typeof e?e(t):e}function a(e){return"number"===typeof e&&e>=0&&e!==1/0}function l(e){return Array.isArray(e)?e:[e]}function u(e,t){return Math.max(e+(t||0)-Date.now(),0)}function c(e,t,n){return S(e)?"function"===typeof t?(0,r.A)({},n,{queryKey:e,queryFn:t}):(0,r.A)({},t,{queryKey:e}):e}function d(e,t,n){return S(e)?[(0,r.A)({},t,{queryKey:e}),n]:[e||{},t]}function f(e,t){var n=e.active,r=e.exact,i=e.fetching,o=e.inactive,s=e.predicate,a=e.queryKey,l=e.stale;if(S(a))if(r){if(t.queryHash!==p(a,t.options))return!1}else if(!y(t.queryKey,a))return!1;var u=function(e,t){return!0===e&&!0===t||null==e&&null==t?"all":!1===e&&!1===t?"none":(null!=e?e:!t)?"active":"inactive"}(n,o);if("none"===u)return!1;if("all"!==u){var c=t.isActive();if("active"===u&&!c)return!1;if("inactive"===u&&c)return!1}return("boolean"!==typeof l||t.isStale()===l)&&(("boolean"!==typeof i||t.isFetching()===i)&&!(s&&!s(t)))}function h(e,t){var n=e.exact,r=e.fetching,i=e.predicate,o=e.mutationKey;if(S(o)){if(!t.options.mutationKey)return!1;if(n){if(m(t.options.mutationKey)!==m(o))return!1}else if(!y(t.options.mutationKey,o))return!1}return("boolean"!==typeof r||"loading"===t.state.status===r)&&!(i&&!i(t))}function p(e,t){return((null==t?void 0:t.queryKeyHashFn)||m)(e)}function m(e){var t,n=l(e);return t=n,JSON.stringify(t,(function(e,t){return w(t)?Object.keys(t).sort().reduce((function(e,n){return e[n]=t[n],e}),{}):t}))}function y(e,t){return g(l(e),l(t))}function g(e,t){return e===t||typeof e===typeof t&&(!(!e||!t||"object"!==typeof e||"object"!==typeof t)&&!Object.keys(t).some((function(n){return!g(e[n],t[n])})))}function v(e,t){if(e===t)return e;var n=Array.isArray(e)&&Array.isArray(t);if(n||w(e)&&w(t)){for(var r=n?e.length:Object.keys(e).length,i=n?t:Object.keys(t),o=i.length,s=n?[]:{},a=0,l=0;l<o;l++){var u=n?l:i[l];s[u]=v(e[u],t[u]),s[u]===e[u]&&a++}return r===o&&a===r?e:s}return t}function b(e,t){if(e&&!t||t&&!e)return!1;for(var n in e)if(e[n]!==t[n])return!1;return!0}function w(e){if(!x(e))return!1;var t=e.constructor;if("undefined"===typeof t)return!0;var n=t.prototype;return!!x(n)&&!!n.hasOwnProperty("isPrototypeOf")}function x(e){return"[object Object]"===Object.prototype.toString.call(e)}function S(e){return"string"===typeof e||Array.isArray(e)}function E(e){return new Promise((function(t){setTimeout(t,e)}))}function k(e){Promise.resolve().then(e).catch((function(e){return setTimeout((function(){throw e}))}))}function C(){if("function"===typeof AbortController)return new AbortController}},226:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),a=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,y={};function g(e,t,n){this.props=e,this.context=t,this.refs=y,this.updater=n||p}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=y,this.updater=n||p}g.prototype.isReactComponent={},g.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},g.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=g.prototype;var w=b.prototype=new v;w.constructor=b,m(w,g.prototype),w.isPureReactComponent=!0;var x=Array.isArray,S=Object.prototype.hasOwnProperty,E={current:null},k={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var i,o={},s=null,a=null;if(null!=t)for(i in void 0!==t.ref&&(a=t.ref),void 0!==t.key&&(s=""+t.key),t)S.call(t,i)&&!k.hasOwnProperty(i)&&(o[i]=t[i]);var l=arguments.length-2;if(1===l)o.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(i in l=e.defaultProps)void 0===o[i]&&(o[i]=l[i]);return{$$typeof:n,type:e,key:s,ref:a,props:o,_owner:E.current}}function P(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var A=/\/+/g;function T(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function R(e,t,i,o,s){var a=typeof e;"undefined"!==a&&"boolean"!==a||(e=null);var l=!1;if(null===e)l=!0;else switch(a){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return s=s(l=e),e=""===o?"."+T(l,0):o,x(s)?(i="",null!=e&&(i=e.replace(A,"$&/")+"/"),R(s,t,i,"",(function(e){return e}))):null!=s&&(P(s)&&(s=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(s,i+(!s.key||l&&l.key===s.key?"":(""+s.key).replace(A,"$&/")+"/")+e)),t.push(s)),1;if(l=0,o=""===o?".":o+":",x(e))for(var u=0;u<e.length;u++){var c=o+T(a=e[u],u);l+=R(a,t,i,c,s)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(a=e.next()).done;)l+=R(a=a.value,t,i,c=o+T(a,u++),s);else if("object"===a)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function _(e,t,n){if(null==e)return e;var r=[],i=0;return R(e,r,"","",(function(e){return t.call(n,e,i++)})),r}function O(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var j={current:null},N={transition:null},L={ReactCurrentDispatcher:j,ReactCurrentBatchConfig:N,ReactCurrentOwner:E};function D(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:_,forEach:function(e,t,n){_(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return _(e,(function(){t++})),t},toArray:function(e){return _(e,(function(e){return e}))||[]},only:function(e){if(!P(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=g,t.Fragment=i,t.Profiler=s,t.PureComponent=b,t.StrictMode=o,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=L,t.act=D,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=m({},e.props),o=e.key,s=e.ref,a=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,a=E.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)S.call(t,u)&&!k.hasOwnProperty(u)&&(i[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)i.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];i.children=l}return{$$typeof:n,type:e.type,key:o,ref:s,props:i,_owner:a}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:a,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=P,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=N.transition;N.transition={};try{e()}finally{N.transition=t}},t.unstable_act=D,t.useCallback=function(e,t){return j.current.useCallback(e,t)},t.useContext=function(e){return j.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return j.current.useDeferredValue(e)},t.useEffect=function(e,t){return j.current.useEffect(e,t)},t.useId=function(){return j.current.useId()},t.useImperativeHandle=function(e,t,n){return j.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return j.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return j.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return j.current.useMemo(e,t)},t.useReducer=function(e,t,n){return j.current.useReducer(e,t,n)},t.useRef=function(e){return j.current.useRef(e)},t.useState=function(e){return j.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return j.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return j.current.useTransition()},t.version="18.3.1"},241:(e,t,n)=>{"use strict";var r=n(643),i=Symbol.for("react.element"),o=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,o={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)s.call(t,r)&&!l.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:i,type:e,key:u,ref:c,props:o,_owner:a.current}}t.Fragment=o,t.jsx=u,t.jsxs=u},261:(e,t,n)=>{"use strict";e.exports=n(498)},306:(e,t,n)=>{"use strict";var r=n(643),i=n(261);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var s=new Set,a={};function l(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(a[e]=t,e=0;e<t.length;e++)s.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,h={},p={};function m(e,t,n,r,i,o,s){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var y={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){y[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];y[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){y[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){y[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){y[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){y[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){y[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){y[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){y[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var g=/[\-:]([a-z])/g;function v(e){return e[1].toUpperCase()}function b(e,t,n,r){var i=y.hasOwnProperty(t)?y[t]:null;(null!==i?0!==i.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,i,r)&&(n=null),r||null===i?function(e){return!!d.call(p,e)||!d.call(h,e)&&(f.test(e)?p[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=null===n?3!==i.type&&"":n:(t=i.attributeName,r=i.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(g,v);y[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(g,v);y[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(g,v);y[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){y[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),y.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){y[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,x=Symbol.for("react.element"),S=Symbol.for("react.portal"),E=Symbol.for("react.fragment"),k=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),P=Symbol.for("react.provider"),A=Symbol.for("react.context"),T=Symbol.for("react.forward_ref"),R=Symbol.for("react.suspense"),_=Symbol.for("react.suspense_list"),O=Symbol.for("react.memo"),j=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var N=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var L=Symbol.iterator;function D(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=L&&e[L]||e["@@iterator"])?e:null}var F,M=Object.assign;function I(e){if(void 0===F)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);F=t&&t[1]||""}return"\n"+F+e}var U=!1;function B(e,t){if(!e||U)return"";U=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var i=u.stack.split("\n"),o=r.stack.split("\n"),s=i.length-1,a=o.length-1;1<=s&&0<=a&&i[s]!==o[a];)a--;for(;1<=s&&0<=a;s--,a--)if(i[s]!==o[a]){if(1!==s||1!==a)do{if(s--,0>--a||i[s]!==o[a]){var l="\n"+i[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=s&&0<=a);break}}}finally{U=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?I(e):""}function V(e){switch(e.tag){case 5:return I(e.type);case 16:return I("Lazy");case 13:return I("Suspense");case 19:return I("SuspenseList");case 0:case 2:case 15:return e=B(e.type,!1);case 11:return e=B(e.type.render,!1);case 1:return e=B(e.type,!0);default:return""}}function z(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case E:return"Fragment";case S:return"Portal";case C:return"Profiler";case k:return"StrictMode";case R:return"Suspense";case _:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case A:return(e.displayName||"Context")+".Consumer";case P:return(e._context.displayName||"Context")+".Provider";case T:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case O:return null!==(t=e.displayName||null)?t:z(e.type)||"Memo";case j:t=e._payload,e=e._init;try{return z(e(t))}catch(n){}}return null}function q(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return z(t);case 8:return t===k?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function Q(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function H(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function $(e){e._valueTracker||(e._valueTracker=function(e){var t=H(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function W(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=H(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Y(e,t){var n=t.checked;return M({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function G(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=Q(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function J(e,t){X(e,t);var n=Q(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,Q(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Q(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return M({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ie(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:Q(n)}}function oe(e,t){var n=Q(t.value),r=Q(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function se(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ae(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ae(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function ye(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}Object.keys(he).forEach((function(e){pe.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]}))}));var ge=M({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ge[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function xe(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,Ee=null,ke=null;function Ce(e){if(e=bi(e)){if("function"!==typeof Se)throw Error(o(280));var t=e.stateNode;t&&(t=xi(t),Se(e.stateNode,e.type,t))}}function Pe(e){Ee?ke?ke.push(e):ke=[e]:Ee=e}function Ae(){if(Ee){var e=Ee,t=ke;if(ke=Ee=null,Ce(e),t)for(e=0;e<t.length;e++)Ce(t[e])}}function Te(e,t){return e(t)}function Re(){}var _e=!1;function Oe(e,t,n){if(_e)return e(t,n);_e=!0;try{return Te(e,t,n)}finally{_e=!1,(null!==Ee||null!==ke)&&(Re(),Ae())}}function je(e,t){var n=e.stateNode;if(null===n)return null;var r=xi(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var Ne=!1;if(c)try{var Le={};Object.defineProperty(Le,"passive",{get:function(){Ne=!0}}),window.addEventListener("test",Le,Le),window.removeEventListener("test",Le,Le)}catch(ce){Ne=!1}function De(e,t,n,r,i,o,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Fe=!1,Me=null,Ie=!1,Ue=null,Be={onError:function(e){Fe=!0,Me=e}};function Ve(e,t,n,r,i,o,s,a,l){Fe=!1,Me=null,De.apply(Be,arguments)}function ze(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function qe(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Qe(e){if(ze(e)!==e)throw Error(o(188))}function He(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=ze(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var s=i.alternate;if(null===s){if(null!==(r=i.return)){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Qe(i),e;if(s===r)return Qe(i),t;s=s.sibling}throw Error(o(188))}if(n.return!==r.return)n=i,r=s;else{for(var a=!1,l=i.child;l;){if(l===n){a=!0,n=i,r=s;break}if(l===r){a=!0,r=i,n=s;break}l=l.sibling}if(!a){for(l=s.child;l;){if(l===n){a=!0,n=s,r=i;break}if(l===r){a=!0,r=s,n=i;break}l=l.sibling}if(!a)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e))?$e(e):null}function $e(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=$e(e);if(null!==t)return t;e=e.sibling}return null}var We=i.unstable_scheduleCallback,Ke=i.unstable_cancelCallback,Ye=i.unstable_shouldYield,Ge=i.unstable_requestPaint,Xe=i.unstable_now,Je=i.unstable_getCurrentPriorityLevel,Ze=i.unstable_ImmediatePriority,et=i.unstable_UserBlockingPriority,tt=i.unstable_NormalPriority,nt=i.unstable_LowPriority,rt=i.unstable_IdlePriority,it=null,ot=null;var st=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(at(e)/lt|0)|0},at=Math.log,lt=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=268435455&n;if(0!==s){var a=s&~i;0!==a?r=dt(a):0!==(o&=s)&&(r=dt(o))}else 0!==(s=n&~i)?r=dt(s):0!==o&&(r=dt(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&i)&&((i=r&-r)>=(o=t&-t)||16===i&&0!==(4194240&o)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)i=1<<(n=31-st(t)),r|=e[n],t&=~i;return r}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function yt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-st(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-st(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var bt=0;function wt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var xt,St,Et,kt,Ct,Pt=!1,At=[],Tt=null,Rt=null,_t=null,Ot=new Map,jt=new Map,Nt=[],Lt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Dt(e,t){switch(e){case"focusin":case"focusout":Tt=null;break;case"dragenter":case"dragleave":Rt=null;break;case"mouseover":case"mouseout":_t=null;break;case"pointerover":case"pointerout":Ot.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":jt.delete(t.pointerId)}}function Ft(e,t,n,r,i,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},null!==t&&(null!==(t=bi(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==i&&-1===t.indexOf(i)&&t.push(i),e)}function Mt(e){var t=vi(e.target);if(null!==t){var n=ze(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=qe(n)))return e.blockedOn=t,void Ct(e.priority,(function(){Et(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function It(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Yt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=bi(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function Ut(e,t,n){It(e)&&n.delete(t)}function Bt(){Pt=!1,null!==Tt&&It(Tt)&&(Tt=null),null!==Rt&&It(Rt)&&(Rt=null),null!==_t&&It(_t)&&(_t=null),Ot.forEach(Ut),jt.forEach(Ut)}function Vt(e,t){e.blockedOn===t&&(e.blockedOn=null,Pt||(Pt=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Bt)))}function zt(e){function t(t){return Vt(t,e)}if(0<At.length){Vt(At[0],e);for(var n=1;n<At.length;n++){var r=At[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Tt&&Vt(Tt,e),null!==Rt&&Vt(Rt,e),null!==_t&&Vt(_t,e),Ot.forEach(t),jt.forEach(t),n=0;n<Nt.length;n++)(r=Nt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Nt.length&&null===(n=Nt[0]).blockedOn;)Mt(n),null===n.blockedOn&&Nt.shift()}var qt=w.ReactCurrentBatchConfig,Qt=!0;function Ht(e,t,n,r){var i=bt,o=qt.transition;qt.transition=null;try{bt=1,Wt(e,t,n,r)}finally{bt=i,qt.transition=o}}function $t(e,t,n,r){var i=bt,o=qt.transition;qt.transition=null;try{bt=4,Wt(e,t,n,r)}finally{bt=i,qt.transition=o}}function Wt(e,t,n,r){if(Qt){var i=Yt(e,t,n,r);if(null===i)Qr(e,t,r,Kt,n),Dt(e,r);else if(function(e,t,n,r,i){switch(t){case"focusin":return Tt=Ft(Tt,e,t,n,r,i),!0;case"dragenter":return Rt=Ft(Rt,e,t,n,r,i),!0;case"mouseover":return _t=Ft(_t,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Ot.set(o,Ft(Ot.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,jt.set(o,Ft(jt.get(o)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r))r.stopPropagation();else if(Dt(e,r),4&t&&-1<Lt.indexOf(e)){for(;null!==i;){var o=bi(i);if(null!==o&&xt(o),null===(o=Yt(e,t,n,r))&&Qr(e,t,r,Kt,n),o===i)break;i=o}null!==i&&r.stopPropagation()}else Qr(e,t,r,null,n)}}var Kt=null;function Yt(e,t,n,r){if(Kt=null,null!==(e=vi(e=xe(r))))if(null===(t=ze(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=qe(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Gt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Je()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Xt=null,Jt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Jt,r=n.length,i="value"in Xt?Xt.value:Xt.textContent,o=i.length;for(e=0;e<r&&n[e]===i[e];e++);var s=r-e;for(t=1;t<=s&&n[r-t]===i[o-t];t++);return Zt=i.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function on(e){function t(t,n,r,i,o){for(var s in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=i,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(s)&&(t=e[s],this[s]=t?t(i):i[s]);return this.isDefaultPrevented=(null!=i.defaultPrevented?i.defaultPrevented:!1===i.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return M(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var sn,an,ln,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=on(un),dn=M({},un,{view:0,detail:0}),fn=on(dn),hn=M({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(sn=e.screenX-ln.screenX,an=e.screenY-ln.screenY):an=sn=0,ln=e),sn)},movementY:function(e){return"movementY"in e?e.movementY:an}}),pn=on(hn),mn=on(M({},hn,{dataTransfer:0})),yn=on(M({},dn,{relatedTarget:0})),gn=on(M({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=M({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=on(vn),wn=on(M({},un,{data:0})),xn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},En={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function kn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=En[e])&&!!t[e]}function Cn(){return kn}var Pn=M({},dn,{key:function(e){if(e.key){var t=xn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),An=on(Pn),Tn=on(M({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Rn=on(M({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cn})),_n=on(M({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),On=M({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),jn=on(On),Nn=[9,13,27,32],Ln=c&&"CompositionEvent"in window,Dn=null;c&&"documentMode"in document&&(Dn=document.documentMode);var Fn=c&&"TextEvent"in window&&!Dn,Mn=c&&(!Ln||Dn&&8<Dn&&11>=Dn),In=String.fromCharCode(32),Un=!1;function Bn(e,t){switch(e){case"keyup":return-1!==Nn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Vn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var zn=!1;var qn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Qn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!qn[e.type]:"textarea"===t}function Hn(e,t,n,r){Pe(r),0<(t=$r(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var $n=null,Wn=null;function Kn(e){Ir(e,0)}function Yn(e){if(W(wi(e)))return e}function Gn(e,t){if("change"===e)return t}var Xn=!1;if(c){var Jn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Jn=Zn}else Jn=!1;Xn=Jn&&(!document.documentMode||9<document.documentMode)}function tr(){$n&&($n.detachEvent("onpropertychange",nr),Wn=$n=null)}function nr(e){if("value"===e.propertyName&&Yn(Wn)){var t=[];Hn(t,Wn,e,xe(e)),Oe(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Wn=n,($n=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ir(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Yn(Wn)}function or(e,t){if("click"===e)return Yn(t)}function sr(e,t){if("input"===e||"change"===e)return Yn(t)}var ar="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(ar(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!d.call(t,i)||!ar(e[i],t[i]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&hr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=void 0===r.end?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=cr(n,o);var s=cr(n,r);i&&s&&(1!==e.rangeCount||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&((t=t.createRange()).setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,yr=null,gr=null,vr=null,br=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==yr||yr!==K(r)||("selectionStart"in(r=yr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},vr&&lr(vr,r)||(vr=r,0<(r=$r(gr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=yr)))}function xr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:xr("Animation","AnimationEnd"),animationiteration:xr("Animation","AnimationIteration"),animationstart:xr("Animation","AnimationStart"),transitionend:xr("Transition","TransitionEnd")},Er={},kr={};function Cr(e){if(Er[e])return Er[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in kr)return Er[e]=n[t];return e}c&&(kr=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Pr=Cr("animationend"),Ar=Cr("animationiteration"),Tr=Cr("animationstart"),Rr=Cr("transitionend"),_r=new Map,Or="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function jr(e,t){_r.set(e,t),l(t,[e])}for(var Nr=0;Nr<Or.length;Nr++){var Lr=Or[Nr];jr(Lr.toLowerCase(),"on"+(Lr[0].toUpperCase()+Lr.slice(1)))}jr(Pr,"onAnimationEnd"),jr(Ar,"onAnimationIteration"),jr(Tr,"onAnimationStart"),jr("dblclick","onDoubleClick"),jr("focusin","onFocus"),jr("focusout","onBlur"),jr(Rr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Dr));function Mr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,i,s,a,l,u){if(Ve.apply(this,arguments),Fe){if(!Fe)throw Error(o(198));var c=Me;Fe=!1,Me=null,Ie||(Ie=!0,Ue=c)}}(r,t,void 0,e),e.currentTarget=null}function Ir(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==o&&i.isPropagationStopped())break e;Mr(i,a,u),o=l}else for(s=0;s<r.length;s++){if(l=(a=r[s]).instance,u=a.currentTarget,a=a.listener,l!==o&&i.isPropagationStopped())break e;Mr(i,a,u),o=l}}}if(Ie)throw e=Ue,Ie=!1,Ue=null,e}function Ur(e,t){var n=t[mi];void 0===n&&(n=t[mi]=new Set);var r=e+"__bubble";n.has(r)||(qr(t,e,2,!1),n.add(r))}function Br(e,t,n){var r=0;t&&(r|=4),qr(n,e,r,t)}var Vr="_reactListening"+Math.random().toString(36).slice(2);function zr(e){if(!e[Vr]){e[Vr]=!0,s.forEach((function(t){"selectionchange"!==t&&(Fr.has(t)||Br(t,!1,e),Br(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Vr]||(t[Vr]=!0,Br("selectionchange",!1,t))}}function qr(e,t,n,r){switch(Gt(t)){case 1:var i=Ht;break;case 4:i=$t;break;default:i=Wt}n=i.bind(null,t,n,e),i=void 0,!Ne||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(i=!0),r?void 0!==i?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):void 0!==i?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Qr(e,t,n,r,i){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var s=r.tag;if(3===s||4===s){var a=r.stateNode.containerInfo;if(a===i||8===a.nodeType&&a.parentNode===i)break;if(4===s)for(s=r.return;null!==s;){var l=s.tag;if((3===l||4===l)&&((l=s.stateNode.containerInfo)===i||8===l.nodeType&&l.parentNode===i))return;s=s.return}for(;null!==a;){if(null===(s=vi(a)))return;if(5===(l=s.tag)||6===l){r=o=s;continue e}a=a.parentNode}}r=r.return}Oe((function(){var r=o,i=xe(n),s=[];e:{var a=_r.get(e);if(void 0!==a){var l=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=An;break;case"focusin":u="focus",l=yn;break;case"focusout":u="blur",l=yn;break;case"beforeblur":case"afterblur":l=yn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Rn;break;case Pr:case Ar:case Tr:l=gn;break;case Rr:l=_n;break;case"scroll":l=fn;break;case"wheel":l=jn;break;case"copy":case"cut":case"paste":l=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=Tn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==a?a+"Capture":null:a;c=[];for(var h,p=r;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==f&&(null!=(m=je(p,f))&&c.push(Hr(p,m,h)))),d)break;p=p.return}0<c.length&&(a=new l(a,u,null,n,i),s.push({event:a,listeners:c}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(a="mouseover"===e||"pointerover"===e)||n===we||!(u=n.relatedTarget||n.fromElement)||!vi(u)&&!u[pi])&&(l||a)&&(a=i.window===i?i:(a=i.ownerDocument)?a.defaultView||a.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?vi(u):null)&&(u!==(d=ze(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=pn,m="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(c=Tn,m="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==l?a:wi(l),h=null==u?a:wi(u),(a=new c(m,p+"leave",l,n,i)).target=d,a.relatedTarget=h,m=null,vi(i)===r&&((c=new c(f,p+"enter",u,n,i)).target=h,c.relatedTarget=d,m=c),d=m,l&&u)e:{for(f=u,p=0,h=c=l;h;h=Wr(h))p++;for(h=0,m=f;m;m=Wr(m))h++;for(;0<p-h;)c=Wr(c),p--;for(;0<h-p;)f=Wr(f),h--;for(;p--;){if(c===f||null!==f&&c===f.alternate)break e;c=Wr(c),f=Wr(f)}c=null}else c=null;null!==l&&Kr(s,a,l,c,!1),null!==u&&null!==d&&Kr(s,d,u,c,!0)}if("select"===(l=(a=r?wi(r):window).nodeName&&a.nodeName.toLowerCase())||"input"===l&&"file"===a.type)var y=Gn;else if(Qn(a))if(Xn)y=sr;else{y=ir;var g=rr}else(l=a.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===a.type||"radio"===a.type)&&(y=or);switch(y&&(y=y(e,r))?Hn(s,y,n,i):(g&&g(e,a,r),"focusout"===e&&(g=a._wrapperState)&&g.controlled&&"number"===a.type&&ee(a,"number",a.value)),g=r?wi(r):window,e){case"focusin":(Qn(g)||"true"===g.contentEditable)&&(yr=g,gr=r,vr=null);break;case"focusout":vr=gr=yr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,wr(s,n,i);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":wr(s,n,i)}var v;if(Ln)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else zn?Bn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Mn&&"ko"!==n.locale&&(zn||"onCompositionStart"!==b?"onCompositionEnd"===b&&zn&&(v=en()):(Jt="value"in(Xt=i)?Xt.value:Xt.textContent,zn=!0)),0<(g=$r(r,b)).length&&(b=new wn(b,e,null,n,i),s.push({event:b,listeners:g}),v?b.data=v:null!==(v=Vn(n))&&(b.data=v))),(v=Fn?function(e,t){switch(e){case"compositionend":return Vn(t);case"keypress":return 32!==t.which?null:(Un=!0,In);case"textInput":return(e=t.data)===In&&Un?null:e;default:return null}}(e,n):function(e,t){if(zn)return"compositionend"===e||!Ln&&Bn(e,t)?(e=en(),Zt=Jt=Xt=null,zn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Mn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=$r(r,"onBeforeInput")).length&&(i=new wn("onBeforeInput","beforeinput",null,n,i),s.push({event:i,listeners:r}),i.data=v))}Ir(s,t)}))}function Hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function $r(e,t){for(var n=t+"Capture",r=[];null!==e;){var i=e,o=i.stateNode;5===i.tag&&null!==o&&(i=o,null!=(o=je(e,n))&&r.unshift(Hr(e,o,i)),null!=(o=je(e,t))&&r.push(Hr(e,o,i))),e=e.return}return r}function Wr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,i){for(var o=t._reactName,s=[];null!==n&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(null!==l&&l===r)break;5===a.tag&&null!==u&&(a=u,i?null!=(l=je(n,o))&&s.unshift(Hr(n,l,a)):i||null!=(l=je(n,o))&&s.push(Hr(n,l,a))),n=n.return}0!==s.length&&e.push({event:t,listeners:s})}var Yr=/\r\n?/g,Gr=/\u0000|\uFFFD/g;function Xr(e){return("string"===typeof e?e:""+e).replace(Yr,"\n").replace(Gr,"")}function Jr(e,t,n){if(t=Xr(t),Xr(e)!==t&&n)throw Error(o(425))}function Zr(){}var ei=null,ti=null;function ni(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ri="function"===typeof setTimeout?setTimeout:void 0,ii="function"===typeof clearTimeout?clearTimeout:void 0,oi="function"===typeof Promise?Promise:void 0,si="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof oi?function(e){return oi.resolve(null).then(e).catch(ai)}:ri;function ai(e){setTimeout((function(){throw e}))}function li(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&8===i.nodeType)if("/$"===(n=i.data)){if(0===r)return e.removeChild(i),void zt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=i}while(n);zt(t)}function ui(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ci(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var di=Math.random().toString(36).slice(2),fi="__reactFiber$"+di,hi="__reactProps$"+di,pi="__reactContainer$"+di,mi="__reactEvents$"+di,yi="__reactListeners$"+di,gi="__reactHandles$"+di;function vi(e){var t=e[fi];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pi]||n[fi]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ci(e);null!==e;){if(n=e[fi])return n;e=ci(e)}return t}n=(e=n).parentNode}return null}function bi(e){return!(e=e[fi]||e[pi])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wi(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function xi(e){return e[hi]||null}var Si=[],Ei=-1;function ki(e){return{current:e}}function Ci(e){0>Ei||(e.current=Si[Ei],Si[Ei]=null,Ei--)}function Pi(e,t){Ei++,Si[Ei]=e.current,e.current=t}var Ai={},Ti=ki(Ai),Ri=ki(!1),_i=Ai;function Oi(e,t){var n=e.type.contextTypes;if(!n)return Ai;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,o={};for(i in n)o[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function ji(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ni(){Ci(Ri),Ci(Ti)}function Li(e,t,n){if(Ti.current!==Ai)throw Error(o(168));Pi(Ti,t),Pi(Ri,n)}function Di(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var i in r=r.getChildContext())if(!(i in t))throw Error(o(108,q(e)||"Unknown",i));return M({},n,r)}function Fi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ai,_i=Ti.current,Pi(Ti,e),Pi(Ri,Ri.current),!0}function Mi(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=Di(e,t,_i),r.__reactInternalMemoizedMergedChildContext=e,Ci(Ri),Ci(Ti),Pi(Ti,e)):Ci(Ri),Pi(Ri,n)}var Ii=null,Ui=!1,Bi=!1;function Vi(e){null===Ii?Ii=[e]:Ii.push(e)}function zi(){if(!Bi&&null!==Ii){Bi=!0;var e=0,t=bt;try{var n=Ii;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ii=null,Ui=!1}catch(i){throw null!==Ii&&(Ii=Ii.slice(e+1)),We(Ze,zi),i}finally{bt=t,Bi=!1}}return null}var qi=[],Qi=0,Hi=null,$i=0,Wi=[],Ki=0,Yi=null,Gi=1,Xi="";function Ji(e,t){qi[Qi++]=$i,qi[Qi++]=Hi,Hi=e,$i=t}function Zi(e,t,n){Wi[Ki++]=Gi,Wi[Ki++]=Xi,Wi[Ki++]=Yi,Yi=e;var r=Gi;e=Xi;var i=32-st(r)-1;r&=~(1<<i),n+=1;var o=32-st(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,Gi=1<<32-st(t)+i|n<<i|r,Xi=o+e}else Gi=1<<o|n<<i|r,Xi=e}function eo(e){null!==e.return&&(Ji(e,1),Zi(e,1,0))}function to(e){for(;e===Hi;)Hi=qi[--Qi],qi[Qi]=null,$i=qi[--Qi],qi[Qi]=null;for(;e===Yi;)Yi=Wi[--Ki],Wi[Ki]=null,Xi=Wi[--Ki],Wi[Ki]=null,Gi=Wi[--Ki],Wi[Ki]=null}var no=null,ro=null,io=!1,oo=null;function so(e,t){var n=Ou(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function ao(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,no=e,ro=ui(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,no=e,ro=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Yi?{id:Gi,overflow:Xi}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Ou(18,null,null,0)).stateNode=t,n.return=e,e.child=n,no=e,ro=null,!0);default:return!1}}function lo(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function uo(e){if(io){var t=ro;if(t){var n=t;if(!ao(e,t)){if(lo(e))throw Error(o(418));t=ui(n.nextSibling);var r=no;t&&ao(e,t)?so(r,n):(e.flags=-4097&e.flags|2,io=!1,no=e)}}else{if(lo(e))throw Error(o(418));e.flags=-4097&e.flags|2,io=!1,no=e}}}function co(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;no=e}function fo(e){if(e!==no)return!1;if(!io)return co(e),io=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ni(e.type,e.memoizedProps)),t&&(t=ro)){if(lo(e))throw ho(),Error(o(418));for(;t;)so(e,t),t=ui(t.nextSibling)}if(co(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ro=ui(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ro=null}}else ro=no?ui(e.stateNode.nextSibling):null;return!0}function ho(){for(var e=ro;e;)e=ui(e.nextSibling)}function po(){ro=no=null,io=!1}function mo(e){null===oo?oo=[e]:oo.push(e)}var yo=w.ReactCurrentBatchConfig;function go(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var i=r,s=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===s?t.ref:(t=function(e){var t=i.refs;null===e?delete t[s]:t[s]=e},t._stringRef=s,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function vo(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bo(e){return(0,e._init)(e._payload)}function wo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Nu(e,t)).index=0,e.sibling=null,e}function s(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function a(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Mu(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===E?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===j&&bo(o)===t.type)?((r=i(t,n.props)).ref=go(e,t,n),r.return=e,r):((r=Lu(n.type,n.key,n.props,null,e.mode,r)).ref=go(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Iu(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Du(n,e.mode,r,o)).return=e,t):((t=i(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Mu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case x:return(n=Lu(t.type,t.key,t.props,null,e.mode,n)).ref=go(e,null,t),n.return=e,n;case S:return(t=Iu(t,e.mode,n)).return=e,t;case j:return f(e,(0,t._init)(t._payload),n)}if(te(t)||D(t))return(t=Du(t,e.mode,n,null)).return=e,t;vo(e,t)}return null}function h(e,t,n,r){var i=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==i?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case x:return n.key===i?u(e,t,n,r):null;case S:return n.key===i?c(e,t,n,r):null;case j:return h(e,t,(i=n._init)(n._payload),r)}if(te(n)||D(n))return null!==i?null:d(e,t,n,r,null);vo(e,n)}return null}function p(e,t,n,r,i){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,i);if("object"===typeof r&&null!==r){switch(r.$$typeof){case x:return u(t,e=e.get(null===r.key?n:r.key)||null,r,i);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i);case j:return p(e,t,n,(0,r._init)(r._payload),i)}if(te(r)||D(r))return d(t,e=e.get(n)||null,r,i,null);vo(t,r)}return null}function m(i,o,a,l){for(var u=null,c=null,d=o,m=o=0,y=null;null!==d&&m<a.length;m++){d.index>m?(y=d,d=null):y=d.sibling;var g=h(i,d,a[m],l);if(null===g){null===d&&(d=y);break}e&&d&&null===g.alternate&&t(i,d),o=s(g,o,m),null===c?u=g:c.sibling=g,c=g,d=y}if(m===a.length)return n(i,d),io&&Ji(i,m),u;if(null===d){for(;m<a.length;m++)null!==(d=f(i,a[m],l))&&(o=s(d,o,m),null===c?u=d:c.sibling=d,c=d);return io&&Ji(i,m),u}for(d=r(i,d);m<a.length;m++)null!==(y=p(d,i,m,a[m],l))&&(e&&null!==y.alternate&&d.delete(null===y.key?m:y.key),o=s(y,o,m),null===c?u=y:c.sibling=y,c=y);return e&&d.forEach((function(e){return t(i,e)})),io&&Ji(i,m),u}function y(i,a,l,u){var c=D(l);if("function"!==typeof c)throw Error(o(150));if(null==(l=c.call(l)))throw Error(o(151));for(var d=c=null,m=a,y=a=0,g=null,v=l.next();null!==m&&!v.done;y++,v=l.next()){m.index>y?(g=m,m=null):g=m.sibling;var b=h(i,m,v.value,u);if(null===b){null===m&&(m=g);break}e&&m&&null===b.alternate&&t(i,m),a=s(b,a,y),null===d?c=b:d.sibling=b,d=b,m=g}if(v.done)return n(i,m),io&&Ji(i,y),c;if(null===m){for(;!v.done;y++,v=l.next())null!==(v=f(i,v.value,u))&&(a=s(v,a,y),null===d?c=v:d.sibling=v,d=v);return io&&Ji(i,y),c}for(m=r(i,m);!v.done;y++,v=l.next())null!==(v=p(m,i,y,v.value,u))&&(e&&null!==v.alternate&&m.delete(null===v.key?y:v.key),a=s(v,a,y),null===d?c=v:d.sibling=v,d=v);return e&&m.forEach((function(e){return t(i,e)})),io&&Ji(i,y),c}return function e(r,o,s,l){if("object"===typeof s&&null!==s&&s.type===E&&null===s.key&&(s=s.props.children),"object"===typeof s&&null!==s){switch(s.$$typeof){case x:e:{for(var u=s.key,c=o;null!==c;){if(c.key===u){if((u=s.type)===E){if(7===c.tag){n(r,c.sibling),(o=i(c,s.props.children)).return=r,r=o;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===j&&bo(u)===c.type){n(r,c.sibling),(o=i(c,s.props)).ref=go(r,c,s),o.return=r,r=o;break e}n(r,c);break}t(r,c),c=c.sibling}s.type===E?((o=Du(s.props.children,r.mode,l,s.key)).return=r,r=o):((l=Lu(s.type,s.key,s.props,null,r.mode,l)).ref=go(r,o,s),l.return=r,r=l)}return a(r);case S:e:{for(c=s.key;null!==o;){if(o.key===c){if(4===o.tag&&o.stateNode.containerInfo===s.containerInfo&&o.stateNode.implementation===s.implementation){n(r,o.sibling),(o=i(o,s.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=Iu(s,r.mode,l)).return=r,r=o}return a(r);case j:return e(r,o,(c=s._init)(s._payload),l)}if(te(s))return m(r,o,s,l);if(D(s))return y(r,o,s,l);vo(r,s)}return"string"===typeof s&&""!==s||"number"===typeof s?(s=""+s,null!==o&&6===o.tag?(n(r,o.sibling),(o=i(o,s)).return=r,r=o):(n(r,o),(o=Mu(s,r.mode,l)).return=r,r=o),a(r)):n(r,o)}}var xo=wo(!0),So=wo(!1),Eo=ki(null),ko=null,Co=null,Po=null;function Ao(){Po=Co=ko=null}function To(e){var t=Eo.current;Ci(Eo),e._currentValue=t}function Ro(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function _o(e,t){ko=e,Po=Co=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(ba=!0),e.firstContext=null)}function Oo(e){var t=e._currentValue;if(Po!==e)if(e={context:e,memoizedValue:t,next:null},null===Co){if(null===ko)throw Error(o(308));Co=e,ko.dependencies={lanes:0,firstContext:e}}else Co=Co.next=e;return t}var jo=null;function No(e){null===jo?jo=[e]:jo.push(e)}function Lo(e,t,n,r){var i=t.interleaved;return null===i?(n.next=n,No(t)):(n.next=i.next,i.next=n),t.interleaved=n,Do(e,r)}function Do(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Fo=!1;function Mo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Io(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Uo(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Bo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Tl)){var i=r.pending;return null===i?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Do(e,n)}return null===(i=r.interleaved)?(t.next=t,No(r)):(t.next=i.next,i.next=t),r.interleaved=t,Do(e,n)}function Vo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function zo(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var i=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?i=o=s:o=o.next=s,n=n.next}while(null!==n);null===o?i=o=t:o=o.next=t}else i=o=t;return n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function qo(e,t,n,r){var i=e.updateQueue;Fo=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,a=i.shared.pending;if(null!==a){i.shared.pending=null;var l=a,u=l.next;l.next=null,null===s?o=u:s.next=u,s=l;var c=e.alternate;null!==c&&((a=(c=c.updateQueue).lastBaseUpdate)!==s&&(null===a?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(null!==o){var d=i.baseState;for(s=0,c=u=l=null,a=o;;){var f=a.lane,h=a.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:h,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var p=e,m=a;switch(f=t,h=n,m.tag){case 1:if("function"===typeof(p=m.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(f="function"===typeof(p=m.payload)?p.call(h,d,f):p)||void 0===f)break e;d=M({},d,f);break e;case 2:Fo=!0}}null!==a.callback&&0!==a.lane&&(e.flags|=64,null===(f=i.effects)?i.effects=[a]:f.push(a))}else h={eventTime:h,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},null===c?(u=c=h,l=d):c=c.next=h,s|=f;if(null===(a=a.next)){if(null===(a=i.shared.pending))break;a=(f=a).next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}if(null===c&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,null!==(t=i.shared.interleaved)){i=t;do{s|=i.lane,i=i.next}while(i!==t)}else null===o&&(i.shared.lanes=0);Fl|=s,e.lanes=s,e.memoizedState=d}}function Qo(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(null!==i){if(r.callback=null,r=n,"function"!==typeof i)throw Error(o(191,i));i.call(r)}}}var Ho={},$o=ki(Ho),Wo=ki(Ho),Ko=ki(Ho);function Yo(e){if(e===Ho)throw Error(o(174));return e}function Go(e,t){switch(Pi(Ko,t),Pi(Wo,e),Pi($o,Ho),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ci($o),Pi($o,t)}function Xo(){Ci($o),Ci(Wo),Ci(Ko)}function Jo(e){Yo(Ko.current);var t=Yo($o.current),n=le(t,e.type);t!==n&&(Pi(Wo,e),Pi($o,n))}function Zo(e){Wo.current===e&&(Ci($o),Ci(Wo))}var es=ki(0);function ts(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ns=[];function rs(){for(var e=0;e<ns.length;e++)ns[e]._workInProgressVersionPrimary=null;ns.length=0}var is=w.ReactCurrentDispatcher,os=w.ReactCurrentBatchConfig,ss=0,as=null,ls=null,us=null,cs=!1,ds=!1,fs=0,hs=0;function ps(){throw Error(o(321))}function ms(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ar(e[n],t[n]))return!1;return!0}function ys(e,t,n,r,i,s){if(ss=s,as=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,is.current=null===e||null===e.memoizedState?Zs:ea,e=n(r,i),ds){s=0;do{if(ds=!1,fs=0,25<=s)throw Error(o(301));s+=1,us=ls=null,t.updateQueue=null,is.current=ta,e=n(r,i)}while(ds)}if(is.current=Js,t=null!==ls&&null!==ls.next,ss=0,us=ls=as=null,cs=!1,t)throw Error(o(300));return e}function gs(){var e=0!==fs;return fs=0,e}function vs(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===us?as.memoizedState=us=e:us=us.next=e,us}function bs(){if(null===ls){var e=as.alternate;e=null!==e?e.memoizedState:null}else e=ls.next;var t=null===us?as.memoizedState:us.next;if(null!==t)us=t,ls=e;else{if(null===e)throw Error(o(310));e={memoizedState:(ls=e).memoizedState,baseState:ls.baseState,baseQueue:ls.baseQueue,queue:ls.queue,next:null},null===us?as.memoizedState=us=e:us=us.next=e}return us}function ws(e,t){return"function"===typeof t?t(e):t}function xs(e){var t=bs(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=ls,i=r.baseQueue,s=n.pending;if(null!==s){if(null!==i){var a=i.next;i.next=s.next,s.next=a}r.baseQueue=i=s,n.pending=null}if(null!==i){s=i.next,r=r.baseState;var l=a=null,u=null,c=s;do{var d=c.lane;if((ss&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(l=u=f,a=r):u=u.next=f,as.lanes|=d,Fl|=d}c=c.next}while(null!==c&&c!==s);null===u?a=r:u.next=l,ar(r,t.memoizedState)||(ba=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){i=e;do{s=i.lane,as.lanes|=s,Fl|=s,i=i.next}while(i!==e)}else null===i&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ss(e){var t=bs(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(null!==i){n.pending=null;var a=i=i.next;do{s=e(s,a.action),a=a.next}while(a!==i);ar(s,t.memoizedState)||(ba=!0),t.memoizedState=s,null===t.baseQueue&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Es(){}function ks(e,t){var n=as,r=bs(),i=t(),s=!ar(r.memoizedState,i);if(s&&(r.memoizedState=i,ba=!0),r=r.queue,Fs(As.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||null!==us&&1&us.memoizedState.tag){if(n.flags|=2048,Os(9,Ps.bind(null,n,r,i,t),void 0,null),null===Rl)throw Error(o(349));0!==(30&ss)||Cs(n,t,i)}return i}function Cs(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=as.updateQueue)?(t={lastEffect:null,stores:null},as.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ps(e,t,n,r){t.value=n,t.getSnapshot=r,Ts(t)&&Rs(e)}function As(e,t,n){return n((function(){Ts(t)&&Rs(e)}))}function Ts(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ar(e,n)}catch(r){return!0}}function Rs(e){var t=Do(e,1);null!==t&&nu(t,e,1,-1)}function _s(e){var t=vs();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ws,lastRenderedState:e},t.queue=e,e=e.dispatch=Ks.bind(null,as,e),[t.memoizedState,e]}function Os(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=as.updateQueue)?(t={lastEffect:null,stores:null},as.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function js(){return bs().memoizedState}function Ns(e,t,n,r){var i=vs();as.flags|=e,i.memoizedState=Os(1|t,n,void 0,void 0===r?null:r)}function Ls(e,t,n,r){var i=bs();r=void 0===r?null:r;var o=void 0;if(null!==ls){var s=ls.memoizedState;if(o=s.destroy,null!==r&&ms(r,s.deps))return void(i.memoizedState=Os(t,n,o,r))}as.flags|=e,i.memoizedState=Os(1|t,n,o,r)}function Ds(e,t){return Ns(8390656,8,e,t)}function Fs(e,t){return Ls(2048,8,e,t)}function Ms(e,t){return Ls(4,2,e,t)}function Is(e,t){return Ls(4,4,e,t)}function Us(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Bs(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ls(4,4,Us.bind(null,t,e),n)}function Vs(){}function zs(e,t){var n=bs();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ms(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function qs(e,t){var n=bs();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ms(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Qs(e,t,n){return 0===(21&ss)?(e.baseState&&(e.baseState=!1,ba=!0),e.memoizedState=n):(ar(n,t)||(n=mt(),as.lanes|=n,Fl|=n,e.baseState=!0),t)}function Hs(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=os.transition;os.transition={};try{e(!1),t()}finally{bt=n,os.transition=r}}function $s(){return bs().memoizedState}function Ws(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ys(e))Gs(t,n);else if(null!==(n=Lo(e,t,n,r))){nu(n,e,r,eu()),Xs(n,t,r)}}function Ks(e,t,n){var r=tu(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ys(e))Gs(t,i);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var s=t.lastRenderedState,a=o(s,n);if(i.hasEagerState=!0,i.eagerState=a,ar(a,s)){var l=t.interleaved;return null===l?(i.next=i,No(t)):(i.next=l.next,l.next=i),void(t.interleaved=i)}}catch(u){}null!==(n=Lo(e,t,i,r))&&(nu(n,e,r,i=eu()),Xs(n,t,r))}}function Ys(e){var t=e.alternate;return e===as||null!==t&&t===as}function Gs(e,t){ds=cs=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xs(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Js={readContext:Oo,useCallback:ps,useContext:ps,useEffect:ps,useImperativeHandle:ps,useInsertionEffect:ps,useLayoutEffect:ps,useMemo:ps,useReducer:ps,useRef:ps,useState:ps,useDebugValue:ps,useDeferredValue:ps,useTransition:ps,useMutableSource:ps,useSyncExternalStore:ps,useId:ps,unstable_isNewReconciler:!1},Zs={readContext:Oo,useCallback:function(e,t){return vs().memoizedState=[e,void 0===t?null:t],e},useContext:Oo,useEffect:Ds,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ns(4194308,4,Us.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ns(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ns(4,2,e,t)},useMemo:function(e,t){var n=vs();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=vs();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ws.bind(null,as,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},vs().memoizedState=e},useState:_s,useDebugValue:Vs,useDeferredValue:function(e){return vs().memoizedState=e},useTransition:function(){var e=_s(!1),t=e[0];return e=Hs.bind(null,e[1]),vs().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=as,i=vs();if(io){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===Rl)throw Error(o(349));0!==(30&ss)||Cs(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,Ds(As.bind(null,r,s,e),[e]),r.flags|=2048,Os(9,Ps.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=vs(),t=Rl.identifierPrefix;if(io){var n=Xi;t=":"+t+"R"+(n=(Gi&~(1<<32-st(Gi)-1)).toString(32)+n),0<(n=fs++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=hs++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ea={readContext:Oo,useCallback:zs,useContext:Oo,useEffect:Fs,useImperativeHandle:Bs,useInsertionEffect:Ms,useLayoutEffect:Is,useMemo:qs,useReducer:xs,useRef:js,useState:function(){return xs(ws)},useDebugValue:Vs,useDeferredValue:function(e){return Qs(bs(),ls.memoizedState,e)},useTransition:function(){return[xs(ws)[0],bs().memoizedState]},useMutableSource:Es,useSyncExternalStore:ks,useId:$s,unstable_isNewReconciler:!1},ta={readContext:Oo,useCallback:zs,useContext:Oo,useEffect:Fs,useImperativeHandle:Bs,useInsertionEffect:Ms,useLayoutEffect:Is,useMemo:qs,useReducer:Ss,useRef:js,useState:function(){return Ss(ws)},useDebugValue:Vs,useDeferredValue:function(e){var t=bs();return null===ls?t.memoizedState=e:Qs(t,ls.memoizedState,e)},useTransition:function(){return[Ss(ws)[0],bs().memoizedState]},useMutableSource:Es,useSyncExternalStore:ks,useId:$s,unstable_isNewReconciler:!1};function na(e,t){if(e&&e.defaultProps){for(var n in t=M({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ra(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:M({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ia={isMounted:function(e){return!!(e=e._reactInternals)&&ze(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),i=tu(e),o=Uo(r,i);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Bo(e,o,i))&&(nu(t,e,i,r),Vo(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),i=tu(e),o=Uo(r,i);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Bo(e,o,i))&&(nu(t,e,i,r),Vo(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),i=Uo(n,r);i.tag=2,void 0!==t&&null!==t&&(i.callback=t),null!==(t=Bo(e,i,r))&&(nu(t,e,r,n),Vo(t,e,r))}};function oa(e,t,n,r,i,o,s){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,s):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(i,o))}function sa(e,t,n){var r=!1,i=Ai,o=t.contextType;return"object"===typeof o&&null!==o?o=Oo(o):(i=ji(t)?_i:Ti.current,o=(r=null!==(r=t.contextTypes)&&void 0!==r)?Oi(e,i):Ai),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ia,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function aa(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ia.enqueueReplaceState(t,t.state,null)}function la(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Mo(e);var o=t.contextType;"object"===typeof o&&null!==o?i.context=Oo(o):(o=ji(t)?_i:Ti.current,i.context=Oi(e,o)),i.state=e.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(ra(e,t,o,n),i.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof i.getSnapshotBeforeUpdate||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||(t=i.state,"function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&ia.enqueueReplaceState(i,i.state,null),qo(e,n,i,r),i.state=e.memoizedState),"function"===typeof i.componentDidMount&&(e.flags|=4194308)}function ua(e,t){try{var n="",r=t;do{n+=V(r),r=r.return}while(r);var i=n}catch(o){i="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:i,digest:null}}function ca(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function da(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var fa="function"===typeof WeakMap?WeakMap:Map;function ha(e,t,n){(n=Uo(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ql||(Ql=!0,Hl=r),da(0,t)},n}function pa(e,t,n){(n=Uo(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){da(0,t)}}var o=e.stateNode;return null!==o&&"function"===typeof o.componentDidCatch&&(n.callback=function(){da(0,t),"function"!==typeof r&&(null===$l?$l=new Set([this]):$l.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ma(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fa;var i=new Set;r.set(t,i)}else void 0===(i=r.get(t))&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Cu.bind(null,e,t,n),t.then(e,e))}function ya(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function ga(e,t,n,r,i){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Uo(-1,1)).tag=2,Bo(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=i,e)}var va=w.ReactCurrentOwner,ba=!1;function wa(e,t,n,r){t.child=null===e?So(t,null,n,r):xo(t,e.child,n,r)}function xa(e,t,n,r,i){n=n.render;var o=t.ref;return _o(t,i),r=ys(e,t,n,r,o,i),n=gs(),null===e||ba?(io&&n&&eo(t),t.flags|=1,wa(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Qa(e,t,i))}function Sa(e,t,n,r,i){if(null===e){var o=n.type;return"function"!==typeof o||ju(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Lu(n.type,null,r,t,t.mode,i)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Ea(e,t,o,r,i))}if(o=e.child,0===(e.lanes&i)){var s=o.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(s,r)&&e.ref===t.ref)return Qa(e,t,i)}return t.flags|=1,(e=Nu(o,r)).ref=t.ref,e.return=t,t.child=e}function Ea(e,t,n,r,i){if(null!==e){var o=e.memoizedProps;if(lr(o,r)&&e.ref===t.ref){if(ba=!1,t.pendingProps=r=o,0===(e.lanes&i))return t.lanes=e.lanes,Qa(e,t,i);0!==(131072&e.flags)&&(ba=!0)}}return Pa(e,t,n,r,i)}function ka(e,t,n){var r=t.pendingProps,i=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Pi(Nl,jl),jl|=n;else{if(0===(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Pi(Nl,jl),jl|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,Pi(Nl,jl),jl|=r}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,Pi(Nl,jl),jl|=r;return wa(e,t,i,n),t.child}function Ca(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Pa(e,t,n,r,i){var o=ji(n)?_i:Ti.current;return o=Oi(t,o),_o(t,i),n=ys(e,t,n,r,o,i),r=gs(),null===e||ba?(io&&r&&eo(t),t.flags|=1,wa(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Qa(e,t,i))}function Aa(e,t,n,r,i){if(ji(n)){var o=!0;Fi(t)}else o=!1;if(_o(t,i),null===t.stateNode)qa(e,t),sa(t,n,r),la(t,n,r,i),r=!0;else if(null===e){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,u=n.contextType;"object"===typeof u&&null!==u?u=Oo(u):u=Oi(t,u=ji(n)?_i:Ti.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof s.getSnapshotBeforeUpdate;d||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(a!==r||l!==u)&&aa(t,s,r,u),Fo=!1;var f=t.memoizedState;s.state=f,qo(t,r,s,i),l=t.memoizedState,a!==r||f!==l||Ri.current||Fo?("function"===typeof c&&(ra(t,n,c,r),l=t.memoizedState),(a=Fo||oa(t,n,a,r,f,l,u))?(d||"function"!==typeof s.UNSAFE_componentWillMount&&"function"!==typeof s.componentWillMount||("function"===typeof s.componentWillMount&&s.componentWillMount(),"function"===typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount()),"function"===typeof s.componentDidMount&&(t.flags|=4194308)):("function"===typeof s.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):("function"===typeof s.componentDidMount&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Io(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:na(t.type,a),s.props=u,d=t.pendingProps,f=s.context,"object"===typeof(l=n.contextType)&&null!==l?l=Oo(l):l=Oi(t,l=ji(n)?_i:Ti.current);var h=n.getDerivedStateFromProps;(c="function"===typeof h||"function"===typeof s.getSnapshotBeforeUpdate)||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(a!==d||f!==l)&&aa(t,s,r,l),Fo=!1,f=t.memoizedState,s.state=f,qo(t,r,s,i);var p=t.memoizedState;a!==d||f!==p||Ri.current||Fo?("function"===typeof h&&(ra(t,n,h,r),p=t.memoizedState),(u=Fo||oa(t,n,u,r,f,p,l)||!1)?(c||"function"!==typeof s.UNSAFE_componentWillUpdate&&"function"!==typeof s.componentWillUpdate||("function"===typeof s.componentWillUpdate&&s.componentWillUpdate(r,p,l),"function"===typeof s.UNSAFE_componentWillUpdate&&s.UNSAFE_componentWillUpdate(r,p,l)),"function"===typeof s.componentDidUpdate&&(t.flags|=4),"function"===typeof s.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof s.componentDidUpdate||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),s.props=r,s.state=p,s.context=l,r=u):("function"!==typeof s.componentDidUpdate||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof s.getSnapshotBeforeUpdate||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Ta(e,t,n,r,o,i)}function Ta(e,t,n,r,i,o){Ca(e,t);var s=0!==(128&t.flags);if(!r&&!s)return i&&Mi(t,n,!1),Qa(e,t,o);r=t.stateNode,va.current=t;var a=s&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&s?(t.child=xo(t,e.child,null,o),t.child=xo(t,null,a,o)):wa(e,t,a,o),t.memoizedState=r.state,i&&Mi(t,n,!0),t.child}function Ra(e){var t=e.stateNode;t.pendingContext?Li(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Li(0,t.context,!1),Go(e,t.containerInfo)}function _a(e,t,n,r,i){return po(),mo(i),t.flags|=256,wa(e,t,n,r),t.child}var Oa,ja,Na,La,Da={dehydrated:null,treeContext:null,retryLane:0};function Fa(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ma(e,t,n){var r,i=t.pendingProps,s=es.current,a=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&s)),r?(a=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(s|=1),Pi(es,1&s),null===e)return uo(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=i.children,e=i.fallback,a?(i=t.mode,a=t.child,l={mode:"hidden",children:l},0===(1&i)&&null!==a?(a.childLanes=0,a.pendingProps=l):a=Fu(l,i,0,null),e=Du(e,i,n,null),a.return=t,e.return=t,a.sibling=e,t.child=a,t.child.memoizedState=Fa(n),t.memoizedState=Da,e):Ia(t,l));if(null!==(s=e.memoizedState)&&null!==(r=s.dehydrated))return function(e,t,n,r,i,s,a){if(n)return 256&t.flags?(t.flags&=-257,Ua(e,t,a,r=ca(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=Fu({mode:"visible",children:r.children},i,0,null),(s=Du(s,i,a,null)).flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,0!==(1&t.mode)&&xo(t,e.child,null,a),t.child.memoizedState=Fa(a),t.memoizedState=Da,s);if(0===(1&t.mode))return Ua(e,t,a,null);if("$!"===i.data){if(r=i.nextSibling&&i.nextSibling.dataset)var l=r.dgst;return r=l,Ua(e,t,a,r=ca(s=Error(o(419)),r,void 0))}if(l=0!==(a&e.childLanes),ba||l){if(null!==(r=Rl)){switch(a&-a){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}0!==(i=0!==(i&(r.suspendedLanes|a))?0:i)&&i!==s.retryLane&&(s.retryLane=i,Do(e,i),nu(r,e,i,-1))}return mu(),Ua(e,t,a,r=ca(Error(o(421))))}return"$?"===i.data?(t.flags|=128,t.child=e.child,t=Au.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,ro=ui(i.nextSibling),no=t,io=!0,oo=null,null!==e&&(Wi[Ki++]=Gi,Wi[Ki++]=Xi,Wi[Ki++]=Yi,Gi=e.id,Xi=e.overflow,Yi=t),t=Ia(t,r.children),t.flags|=4096,t)}(e,t,l,i,r,s,n);if(a){a=i.fallback,l=t.mode,r=(s=e.child).sibling;var u={mode:"hidden",children:i.children};return 0===(1&l)&&t.child!==s?((i=t.child).childLanes=0,i.pendingProps=u,t.deletions=null):(i=Nu(s,u)).subtreeFlags=14680064&s.subtreeFlags,null!==r?a=Nu(r,a):(a=Du(a,l,n,null)).flags|=2,a.return=t,i.return=t,i.sibling=a,t.child=i,i=a,a=t.child,l=null===(l=e.child.memoizedState)?Fa(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},a.memoizedState=l,a.childLanes=e.childLanes&~n,t.memoizedState=Da,i}return e=(a=e.child).sibling,i=Nu(a,{mode:"visible",children:i.children}),0===(1&t.mode)&&(i.lanes=n),i.return=t,i.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=i,t.memoizedState=null,i}function Ia(e,t){return(t=Fu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ua(e,t,n,r){return null!==r&&mo(r),xo(t,e.child,null,n),(e=Ia(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Ba(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ro(e.return,t,n)}function Va(e,t,n,r,i){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function za(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(wa(e,t,r.children,n),0!==(2&(r=es.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ba(e,n,t);else if(19===e.tag)Ba(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Pi(es,r),0===(1&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===ts(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Va(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===ts(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Va(t,!0,n,null,o);break;case"together":Va(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function qa(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Qa(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Fl|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Nu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Nu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ha(e,t){if(!io)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function $a(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=14680064&i.subtreeFlags,r|=14680064&i.flags,i.return=e,i=i.sibling;else for(i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Wa(e,t,n){var r=t.pendingProps;switch(to(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return $a(t),null;case 1:case 17:return ji(t.type)&&Ni(),$a(t),null;case 3:return r=t.stateNode,Xo(),Ci(Ri),Ci(Ti),rs(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==oo&&(su(oo),oo=null))),ja(e,t),$a(t),null;case 5:Zo(t);var i=Yo(Ko.current);if(n=t.type,null!==e&&null!=t.stateNode)Na(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return $a(t),null}if(e=Yo($o.current),fo(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[fi]=t,r[hi]=s,e=0!==(1&t.mode),n){case"dialog":Ur("cancel",r),Ur("close",r);break;case"iframe":case"object":case"embed":Ur("load",r);break;case"video":case"audio":for(i=0;i<Dr.length;i++)Ur(Dr[i],r);break;case"source":Ur("error",r);break;case"img":case"image":case"link":Ur("error",r),Ur("load",r);break;case"details":Ur("toggle",r);break;case"input":G(r,s),Ur("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},Ur("invalid",r);break;case"textarea":ie(r,s),Ur("invalid",r)}for(var l in ve(n,s),i=null,s)if(s.hasOwnProperty(l)){var u=s[l];"children"===l?"string"===typeof u?r.textContent!==u&&(!0!==s.suppressHydrationWarning&&Jr(r.textContent,u,e),i=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==s.suppressHydrationWarning&&Jr(r.textContent,u,e),i=["children",""+u]):a.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&Ur("scroll",r)}switch(n){case"input":$(r),Z(r,s,!0);break;case"textarea":$(r),se(r);break;case"select":case"option":break;default:"function"===typeof s.onClick&&(r.onclick=Zr)}r=i,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===i.nodeType?i:i.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ae(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[fi]=t,e[hi]=r,Oa(e,t,!1,!1),t.stateNode=e;e:{switch(l=be(n,r),n){case"dialog":Ur("cancel",e),Ur("close",e),i=r;break;case"iframe":case"object":case"embed":Ur("load",e),i=r;break;case"video":case"audio":for(i=0;i<Dr.length;i++)Ur(Dr[i],e);i=r;break;case"source":Ur("error",e),i=r;break;case"img":case"image":case"link":Ur("error",e),Ur("load",e),i=r;break;case"details":Ur("toggle",e),i=r;break;case"input":G(e,r),i=Y(e,r),Ur("invalid",e);break;case"option":default:i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=M({},r,{value:void 0}),Ur("invalid",e);break;case"textarea":ie(e,r),i=re(e,r),Ur("invalid",e)}for(s in ve(n,i),u=i)if(u.hasOwnProperty(s)){var c=u[s];"style"===s?ye(e,c):"dangerouslySetInnerHTML"===s?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===s?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==s&&"suppressHydrationWarning"!==s&&"autoFocus"!==s&&(a.hasOwnProperty(s)?null!=c&&"onScroll"===s&&Ur("scroll",e):null!=c&&b(e,s,c,l))}switch(n){case"input":$(e),Z(e,r,!1);break;case"textarea":$(e),se(e);break;case"option":null!=r.value&&e.setAttribute("value",""+Q(r.value));break;case"select":e.multiple=!!r.multiple,null!=(s=r.value)?ne(e,!!r.multiple,s,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof i.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return $a(t),null;case 6:if(e&&null!=t.stateNode)La(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(n=Yo(Ko.current),Yo($o.current),fo(t)){if(r=t.stateNode,n=t.memoizedProps,r[fi]=t,(s=r.nodeValue!==n)&&null!==(e=no))switch(e.tag){case 3:Jr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Jr(r.nodeValue,n,0!==(1&e.mode))}s&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fi]=t,t.stateNode=r}return $a(t),null;case 13:if(Ci(es),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(io&&null!==ro&&0!==(1&t.mode)&&0===(128&t.flags))ho(),po(),t.flags|=98560,s=!1;else if(s=fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!s)throw Error(o(318));if(!(s=null!==(s=t.memoizedState)?s.dehydrated:null))throw Error(o(317));s[fi]=t}else po(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;$a(t),s=!1}else null!==oo&&(su(oo),oo=null),s=!0;if(!s)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&es.current)?0===Ll&&(Ll=3):mu())),null!==t.updateQueue&&(t.flags|=4),$a(t),null);case 4:return Xo(),ja(e,t),null===e&&zr(t.stateNode.containerInfo),$a(t),null;case 10:return To(t.type._context),$a(t),null;case 19:if(Ci(es),null===(s=t.memoizedState))return $a(t),null;if(r=0!==(128&t.flags),null===(l=s.rendering))if(r)Ha(s,!1);else{if(0!==Ll||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=ts(e))){for(t.flags|=128,Ha(s,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(s=n).flags&=14680066,null===(l=s.alternate)?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=l.childLanes,s.lanes=l.lanes,s.child=l.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=l.memoizedProps,s.memoizedState=l.memoizedState,s.updateQueue=l.updateQueue,s.type=l.type,e=l.dependencies,s.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Pi(es,1&es.current|2),t.child}e=e.sibling}null!==s.tail&&Xe()>zl&&(t.flags|=128,r=!0,Ha(s,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ts(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Ha(s,!0),null===s.tail&&"hidden"===s.tailMode&&!l.alternate&&!io)return $a(t),null}else 2*Xe()-s.renderingStartTime>zl&&1073741824!==n&&(t.flags|=128,r=!0,Ha(s,!1),t.lanes=4194304);s.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=s.last)?n.sibling=l:t.child=l,s.last=l)}return null!==s.tail?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Xe(),t.sibling=null,n=es.current,Pi(es,r?1&n|2:1&n),t):($a(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&jl)&&($a(t),6&t.subtreeFlags&&(t.flags|=8192)):$a(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function Ka(e,t){switch(to(t),t.tag){case 1:return ji(t.type)&&Ni(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xo(),Ci(Ri),Ci(Ti),rs(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zo(t),null;case 13:if(Ci(es),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));po()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ci(es),null;case 4:return Xo(),null;case 10:return To(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Oa=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},ja=function(){},Na=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Yo($o.current);var o,s=null;switch(n){case"input":i=Y(e,i),r=Y(e,r),s=[];break;case"select":i=M({},i,{value:void 0}),r=M({},r,{value:void 0}),s=[];break;case"textarea":i=re(e,i),r=re(e,r),s=[];break;default:"function"!==typeof i.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ve(n,r),n=null,i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&null!=i[c])if("style"===c){var l=i[c];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(a.hasOwnProperty(c)?s||(s=[]):(s=s||[]).push(c,null));for(c in r){var u=r[c];if(l=null!=i?i[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(null!=u||null!=l))if("style"===c)if(l){for(o in l)!l.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&l[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(s||(s=[]),s.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(s=s||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(s=s||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(a.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Ur("scroll",e),s||l===u||(s=[])):(s=s||[]).push(c,u))}n&&(s=s||[]).push("style",n);var c=s;(t.updateQueue=c)&&(t.flags|=4)}},La=function(e,t,n,r){n!==r&&(t.flags|=4)};var Ya=!1,Ga=!1,Xa="function"===typeof WeakSet?WeakSet:Set,Ja=null;function Za(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){ku(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){ku(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,void 0!==o&&el(t,n,o)}i=i.next}while(i!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function il(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ol(e){var t=e.alternate;null!==t&&(e.alternate=null,ol(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fi],delete t[hi],delete t[mi],delete t[yi],delete t[gi])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function sl(e){return 5===e.tag||3===e.tag||4===e.tag}function al(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||sl(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function ul(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ul(e,t,n),e=e.sibling;null!==e;)ul(e,t,n),e=e.sibling}var cl=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)hl(e,t,n),n=n.sibling}function hl(e,t,n){if(ot&&"function"===typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(it,n)}catch(a){}switch(n.tag){case 5:Ga||Za(n,t);case 6:var r=cl,i=dl;cl=null,fl(e,t,n),dl=i,null!==(cl=r)&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cl.removeChild(n.stateNode));break;case 18:null!==cl&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?li(e.parentNode,n):1===e.nodeType&&li(e,n),zt(e)):li(cl,n.stateNode));break;case 4:r=cl,i=dl,cl=n.stateNode.containerInfo,dl=!0,fl(e,t,n),cl=r,dl=i;break;case 0:case 11:case 14:case 15:if(!Ga&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,void 0!==s&&(0!==(2&o)||0!==(4&o))&&el(n,t,s),i=i.next}while(i!==r)}fl(e,t,n);break;case 1:if(!Ga&&(Za(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ku(n,t,a)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Ga=(r=Ga)||null!==n.memoizedState,fl(e,t,n),Ga=r):fl(e,t,n);break;default:fl(e,t,n)}}function pl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Xa),t.forEach((function(t){var r=Tu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function ml(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,a=t,l=a;e:for(;null!==l;){switch(l.tag){case 5:cl=l.stateNode,dl=!1;break e;case 3:case 4:cl=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===cl)throw Error(o(160));hl(s,a,i),cl=null,dl=!1;var u=i.alternate;null!==u&&(u.return=null),i.return=null}catch(c){ku(i,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)yl(t,e),t=t.sibling}function yl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),gl(e),4&r){try{nl(3,e,e.return),rl(3,e)}catch(y){ku(e,e.return,y)}try{nl(5,e,e.return)}catch(y){ku(e,e.return,y)}}break;case 1:ml(t,e),gl(e),512&r&&null!==n&&Za(n,n.return);break;case 5:if(ml(t,e),gl(e),512&r&&null!==n&&Za(n,n.return),32&e.flags){var i=e.stateNode;try{fe(i,"")}catch(y){ku(e,e.return,y)}}if(4&r&&null!=(i=e.stateNode)){var s=e.memoizedProps,a=null!==n?n.memoizedProps:s,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===s.type&&null!=s.name&&X(i,s),be(l,a);var c=be(l,s);for(a=0;a<u.length;a+=2){var d=u[a],f=u[a+1];"style"===d?ye(i,f):"dangerouslySetInnerHTML"===d?de(i,f):"children"===d?fe(i,f):b(i,d,f,c)}switch(l){case"input":J(i,s);break;case"textarea":oe(i,s);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var p=s.value;null!=p?ne(i,!!s.multiple,p,!1):h!==!!s.multiple&&(null!=s.defaultValue?ne(i,!!s.multiple,s.defaultValue,!0):ne(i,!!s.multiple,s.multiple?[]:"",!1))}i[hi]=s}catch(y){ku(e,e.return,y)}}break;case 6:if(ml(t,e),gl(e),4&r){if(null===e.stateNode)throw Error(o(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(y){ku(e,e.return,y)}}break;case 3:if(ml(t,e),gl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{zt(t.containerInfo)}catch(y){ku(e,e.return,y)}break;case 4:default:ml(t,e),gl(e);break;case 13:ml(t,e),gl(e),8192&(i=e.child).flags&&(s=null!==i.memoizedState,i.stateNode.isHidden=s,!s||null!==i.alternate&&null!==i.alternate.memoizedState||(Vl=Xe())),4&r&&pl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Ga=(c=Ga)||d,ml(t,e),Ga=c):ml(t,e),gl(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Ja=e,d=e.child;null!==d;){for(f=Ja=d;null!==Ja;){switch(p=(h=Ja).child,h.tag){case 0:case 11:case 14:case 15:nl(4,h,h.return);break;case 1:Za(h,h.return);var m=h.stateNode;if("function"===typeof m.componentWillUnmount){r=h,n=h.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(y){ku(r,n,y)}}break;case 5:Za(h,h.return);break;case 22:if(null!==h.memoizedState){xl(f);continue}}null!==p?(p.return=h,Ja=p):xl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{i=f.stateNode,c?"function"===typeof(s=i.style).setProperty?s.setProperty("display","none","important"):s.display="none":(l=f.stateNode,a=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,l.style.display=me("display",a))}catch(y){ku(e,e.return,y)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(y){ku(e,e.return,y)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),gl(e),4&r&&pl(e);case 21:}}function gl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(sl(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var i=r.stateNode;32&r.flags&&(fe(i,""),r.flags&=-33),ul(e,al(e),i);break;case 3:case 4:var s=r.stateNode.containerInfo;ll(e,al(e),s);break;default:throw Error(o(161))}}catch(a){ku(e,e.return,a)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vl(e,t,n){Ja=e,bl(e,t,n)}function bl(e,t,n){for(var r=0!==(1&e.mode);null!==Ja;){var i=Ja,o=i.child;if(22===i.tag&&r){var s=null!==i.memoizedState||Ya;if(!s){var a=i.alternate,l=null!==a&&null!==a.memoizedState||Ga;a=Ya;var u=Ga;if(Ya=s,(Ga=l)&&!u)for(Ja=i;null!==Ja;)l=(s=Ja).child,22===s.tag&&null!==s.memoizedState?Sl(i):null!==l?(l.return=s,Ja=l):Sl(i);for(;null!==o;)Ja=o,bl(o,t,n),o=o.sibling;Ja=i,Ya=a,Ga=u}wl(e)}else 0!==(8772&i.subtreeFlags)&&null!==o?(o.return=i,Ja=o):wl(e)}}function wl(e){for(;null!==Ja;){var t=Ja;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Ga||rl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Ga)if(null===n)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:na(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;null!==s&&Qo(t,s,r);break;case 3:var a=t.updateQueue;if(null!==a){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Qo(t,a,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&zt(f)}}}break;default:throw Error(o(163))}Ga||512&t.flags&&il(t)}catch(h){ku(t,t.return,h)}}if(t===e){Ja=null;break}if(null!==(n=t.sibling)){n.return=t.return,Ja=n;break}Ja=t.return}}function xl(e){for(;null!==Ja;){var t=Ja;if(t===e){Ja=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Ja=n;break}Ja=t.return}}function Sl(e){for(;null!==Ja;){var t=Ja;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){ku(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var i=t.return;try{r.componentDidMount()}catch(l){ku(t,i,l)}}var o=t.return;try{il(t)}catch(l){ku(t,o,l)}break;case 5:var s=t.return;try{il(t)}catch(l){ku(t,s,l)}}}catch(l){ku(t,t.return,l)}if(t===e){Ja=null;break}var a=t.sibling;if(null!==a){a.return=t.return,Ja=a;break}Ja=t.return}}var El,kl=Math.ceil,Cl=w.ReactCurrentDispatcher,Pl=w.ReactCurrentOwner,Al=w.ReactCurrentBatchConfig,Tl=0,Rl=null,_l=null,Ol=0,jl=0,Nl=ki(0),Ll=0,Dl=null,Fl=0,Ml=0,Il=0,Ul=null,Bl=null,Vl=0,zl=1/0,ql=null,Ql=!1,Hl=null,$l=null,Wl=!1,Kl=null,Yl=0,Gl=0,Xl=null,Jl=-1,Zl=0;function eu(){return 0!==(6&Tl)?Xe():-1!==Jl?Jl:Jl=Xe()}function tu(e){return 0===(1&e.mode)?1:0!==(2&Tl)&&0!==Ol?Ol&-Ol:null!==yo.transition?(0===Zl&&(Zl=mt()),Zl):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Gt(e.type)}function nu(e,t,n,r){if(50<Gl)throw Gl=0,Xl=null,Error(o(185));gt(e,n,r),0!==(2&Tl)&&e===Rl||(e===Rl&&(0===(2&Tl)&&(Ml|=n),4===Ll&&au(e,Ol)),ru(e,r),1===n&&0===Tl&&0===(1&t.mode)&&(zl=Xe()+500,Ui&&zi()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-st(o),a=1<<s,l=i[s];-1===l?0!==(a&n)&&0===(a&r)||(i[s]=ht(a,t)):l<=t&&(e.expiredLanes|=a),o&=~a}}(e,t);var r=ft(e,e===Rl?Ol:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Ui=!0,Vi(e)}(lu.bind(null,e)):Vi(lu.bind(null,e)),si((function(){0===(6&Tl)&&zi()})),n=null;else{switch(wt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Ru(n,iu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function iu(e,t){if(Jl=-1,Zl=0,0!==(6&Tl))throw Error(o(327));var n=e.callbackNode;if(Su()&&e.callbackNode!==n)return null;var r=ft(e,e===Rl?Ol:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=yu(e,r);else{t=r;var i=Tl;Tl|=2;var s=pu();for(Rl===e&&Ol===t||(ql=null,zl=Xe()+500,fu(e,t));;)try{vu();break}catch(l){hu(e,l)}Ao(),Cl.current=s,Tl=i,null!==_l?t=0:(Rl=null,Ol=0,t=Ll)}if(0!==t){if(2===t&&(0!==(i=pt(e))&&(r=i,t=ou(e,i))),1===t)throw n=Dl,fu(e,0),au(e,r),ru(e,Xe()),n;if(6===t)au(e,r);else{if(i=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!ar(o(),i))return!1}catch(a){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(i)&&(2===(t=yu(e,r))&&(0!==(s=pt(e))&&(r=s,t=ou(e,s))),1===t))throw n=Dl,fu(e,0),au(e,r),ru(e,Xe()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:xu(e,Bl,ql);break;case 3:if(au(e,r),(130023424&r)===r&&10<(t=Vl+500-Xe())){if(0!==ft(e,0))break;if(((i=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=ri(xu.bind(null,e,Bl,ql),t);break}xu(e,Bl,ql);break;case 4:if(au(e,r),(4194240&r)===r)break;for(t=e.eventTimes,i=-1;0<r;){var a=31-st(r);s=1<<a,(a=t[a])>i&&(i=a),r&=~s}if(r=i,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*kl(r/1960))-r)){e.timeoutHandle=ri(xu.bind(null,e,Bl,ql),r);break}xu(e,Bl,ql);break;default:throw Error(o(329))}}}return ru(e,Xe()),e.callbackNode===n?iu.bind(null,e):null}function ou(e,t){var n=Ul;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=yu(e,t))&&(t=Bl,Bl=n,null!==t&&su(t)),e}function su(e){null===Bl?Bl=e:Bl.push.apply(Bl,e)}function au(e,t){for(t&=~Il,t&=~Ml,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-st(t),r=1<<n;e[n]=-1,t&=~r}}function lu(e){if(0!==(6&Tl))throw Error(o(327));Su();var t=ft(e,0);if(0===(1&t))return ru(e,Xe()),null;var n=yu(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=ou(e,r))}if(1===n)throw n=Dl,fu(e,0),au(e,t),ru(e,Xe()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,xu(e,Bl,ql),ru(e,Xe()),null}function uu(e,t){var n=Tl;Tl|=1;try{return e(t)}finally{0===(Tl=n)&&(zl=Xe()+500,Ui&&zi())}}function cu(e){null!==Kl&&0===Kl.tag&&0===(6&Tl)&&Su();var t=Tl;Tl|=1;var n=Al.transition,r=bt;try{if(Al.transition=null,bt=1,e)return e()}finally{bt=r,Al.transition=n,0===(6&(Tl=t))&&zi()}}function du(){jl=Nl.current,Ci(Nl)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ii(n)),null!==_l)for(n=_l.return;null!==n;){var r=n;switch(to(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Ni();break;case 3:Xo(),Ci(Ri),Ci(Ti),rs();break;case 5:Zo(r);break;case 4:Xo();break;case 13:case 19:Ci(es);break;case 10:To(r.type._context);break;case 22:case 23:du()}n=n.return}if(Rl=e,_l=e=Nu(e.current,null),Ol=jl=t,Ll=0,Dl=null,Il=Ml=Fl=0,Bl=Ul=null,null!==jo){for(t=0;t<jo.length;t++)if(null!==(r=(n=jo[t]).interleaved)){n.interleaved=null;var i=r.next,o=n.pending;if(null!==o){var s=o.next;o.next=i,r.next=s}n.pending=r}jo=null}return e}function hu(e,t){for(;;){var n=_l;try{if(Ao(),is.current=Js,cs){for(var r=as.memoizedState;null!==r;){var i=r.queue;null!==i&&(i.pending=null),r=r.next}cs=!1}if(ss=0,us=ls=as=null,ds=!1,fs=0,Pl.current=null,null===n||null===n.return){Ll=1,Dl=t,_l=null;break}e:{var s=e,a=n.return,l=n,u=t;if(t=Ol,l.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=ya(a);if(null!==p){p.flags&=-257,ga(p,a,l,0,t),1&p.mode&&ma(s,c,t),u=c;var m=(t=p).updateQueue;if(null===m){var y=new Set;y.add(u),t.updateQueue=y}else m.add(u);break e}if(0===(1&t)){ma(s,c,t),mu();break e}u=Error(o(426))}else if(io&&1&l.mode){var g=ya(a);if(null!==g){0===(65536&g.flags)&&(g.flags|=256),ga(g,a,l,0,t),mo(ua(u,l));break e}}s=u=ua(u,l),4!==Ll&&(Ll=2),null===Ul?Ul=[s]:Ul.push(s),s=a;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t,zo(s,ha(0,u,t));break e;case 1:l=u;var v=s.type,b=s.stateNode;if(0===(128&s.flags)&&("function"===typeof v.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===$l||!$l.has(b)))){s.flags|=65536,t&=-t,s.lanes|=t,zo(s,pa(s,l,t));break e}}s=s.return}while(null!==s)}wu(n)}catch(w){t=w,_l===n&&null!==n&&(_l=n=n.return);continue}break}}function pu(){var e=Cl.current;return Cl.current=Js,null===e?Js:e}function mu(){0!==Ll&&3!==Ll&&2!==Ll||(Ll=4),null===Rl||0===(268435455&Fl)&&0===(268435455&Ml)||au(Rl,Ol)}function yu(e,t){var n=Tl;Tl|=2;var r=pu();for(Rl===e&&Ol===t||(ql=null,fu(e,t));;)try{gu();break}catch(i){hu(e,i)}if(Ao(),Tl=n,Cl.current=r,null!==_l)throw Error(o(261));return Rl=null,Ol=0,Ll}function gu(){for(;null!==_l;)bu(_l)}function vu(){for(;null!==_l&&!Ye();)bu(_l)}function bu(e){var t=El(e.alternate,e,jl);e.memoizedProps=e.pendingProps,null===t?wu(e):_l=t,Pl.current=null}function wu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Wa(n,t,jl)))return void(_l=n)}else{if(null!==(n=Ka(n,t)))return n.flags&=32767,void(_l=n);if(null===e)return Ll=6,void(_l=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(_l=t);_l=t=e}while(null!==t);0===Ll&&(Ll=5)}function xu(e,t,n){var r=bt,i=Al.transition;try{Al.transition=null,bt=1,function(e,t,n,r){do{Su()}while(null!==Kl);if(0!==(6&Tl))throw Error(o(327));n=e.finishedWork;var i=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-st(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}(e,s),e===Rl&&(_l=Rl=null,Ol=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Wl||(Wl=!0,Ru(tt,(function(){return Su(),null}))),s=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||s){s=Al.transition,Al.transition=null;var a=bt;bt=1;var l=Tl;Tl|=4,Pl.current=null,function(e,t){if(ei=Qt,hr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch(x){n=null;break e}var a=0,l=-1,u=-1,c=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==i&&3!==f.nodeType||(l=a+i),f!==s||0!==r&&3!==f.nodeType||(u=a+r),3===f.nodeType&&(a+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++c===i&&(l=a),h===s&&++d===r&&(u=a),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ti={focusedElem:e,selectionRange:n},Qt=!1,Ja=t;null!==Ja;)if(e=(t=Ja).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Ja=e;else for(;null!==Ja;){t=Ja;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var y=m.memoizedProps,g=m.memoizedState,v=t.stateNode,b=v.getSnapshotBeforeUpdate(t.elementType===t.type?y:na(t.type,y),g);v.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(o(163))}}catch(x){ku(t,t.return,x)}if(null!==(e=t.sibling)){e.return=t.return,Ja=e;break}Ja=t.return}m=tl,tl=!1}(e,n),yl(n,e),pr(ti),Qt=!!ei,ti=ei=null,e.current=n,vl(n,e,i),Ge(),Tl=l,bt=a,Al.transition=s}else e.current=n;if(Wl&&(Wl=!1,Kl=e,Yl=i),s=e.pendingLanes,0===s&&($l=null),function(e){if(ot&&"function"===typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(it,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Ql)throw Ql=!1,e=Hl,Hl=null,e;0!==(1&Yl)&&0!==e.tag&&Su(),s=e.pendingLanes,0!==(1&s)?e===Xl?Gl++:(Gl=0,Xl=e):Gl=0,zi()}(e,t,n,r)}finally{Al.transition=i,bt=r}return null}function Su(){if(null!==Kl){var e=wt(Yl),t=Al.transition,n=bt;try{if(Al.transition=null,bt=16>e?16:e,null===Kl)var r=!1;else{if(e=Kl,Kl=null,Yl=0,0!==(6&Tl))throw Error(o(331));var i=Tl;for(Tl|=4,Ja=e.current;null!==Ja;){var s=Ja,a=s.child;if(0!==(16&Ja.flags)){var l=s.deletions;if(null!==l){for(var u=0;u<l.length;u++){var c=l[u];for(Ja=c;null!==Ja;){var d=Ja;switch(d.tag){case 0:case 11:case 15:nl(8,d,s)}var f=d.child;if(null!==f)f.return=d,Ja=f;else for(;null!==Ja;){var h=(d=Ja).sibling,p=d.return;if(ol(d),d===c){Ja=null;break}if(null!==h){h.return=p,Ja=h;break}Ja=p}}}var m=s.alternate;if(null!==m){var y=m.child;if(null!==y){m.child=null;do{var g=y.sibling;y.sibling=null,y=g}while(null!==y)}}Ja=s}}if(0!==(2064&s.subtreeFlags)&&null!==a)a.return=s,Ja=a;else e:for(;null!==Ja;){if(0!==(2048&(s=Ja).flags))switch(s.tag){case 0:case 11:case 15:nl(9,s,s.return)}var v=s.sibling;if(null!==v){v.return=s.return,Ja=v;break e}Ja=s.return}}var b=e.current;for(Ja=b;null!==Ja;){var w=(a=Ja).child;if(0!==(2064&a.subtreeFlags)&&null!==w)w.return=a,Ja=w;else e:for(a=b;null!==Ja;){if(0!==(2048&(l=Ja).flags))try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(S){ku(l,l.return,S)}if(l===a){Ja=null;break e}var x=l.sibling;if(null!==x){x.return=l.return,Ja=x;break e}Ja=l.return}}if(Tl=i,zi(),ot&&"function"===typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(it,e)}catch(S){}r=!0}return r}finally{bt=n,Al.transition=t}}return!1}function Eu(e,t,n){e=Bo(e,t=ha(0,t=ua(n,t),1),1),t=eu(),null!==e&&(gt(e,1,t),ru(e,t))}function ku(e,t,n){if(3===e.tag)Eu(e,e,n);else for(;null!==t;){if(3===t.tag){Eu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===$l||!$l.has(r))){t=Bo(t,e=pa(t,e=ua(n,e),1),1),e=eu(),null!==t&&(gt(t,1,e),ru(t,e));break}}t=t.return}}function Cu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Rl===e&&(Ol&n)===n&&(4===Ll||3===Ll&&(130023424&Ol)===Ol&&500>Xe()-Vl?fu(e,0):Il|=n),ru(e,t)}function Pu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=Do(e,t))&&(gt(e,t,n),ru(e,n))}function Au(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Pu(e,n)}function Tu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;null!==i&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),Pu(e,n)}function Ru(e,t){return We(e,t)}function _u(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ou(e,t,n,r){return new _u(e,t,n,r)}function ju(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Nu(e,t){var n=e.alternate;return null===n?((n=Ou(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Lu(e,t,n,r,i,s){var a=2;if(r=e,"function"===typeof e)ju(e)&&(a=1);else if("string"===typeof e)a=5;else e:switch(e){case E:return Du(n.children,i,s,t);case k:a=8,i|=8;break;case C:return(e=Ou(12,n,t,2|i)).elementType=C,e.lanes=s,e;case R:return(e=Ou(13,n,t,i)).elementType=R,e.lanes=s,e;case _:return(e=Ou(19,n,t,i)).elementType=_,e.lanes=s,e;case N:return Fu(n,i,s,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case P:a=10;break e;case A:a=9;break e;case T:a=11;break e;case O:a=14;break e;case j:a=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Ou(a,n,t,i)).elementType=e,t.type=r,t.lanes=s,t}function Du(e,t,n,r){return(e=Ou(7,e,r,t)).lanes=n,e}function Fu(e,t,n,r){return(e=Ou(22,e,r,t)).elementType=N,e.lanes=n,e.stateNode={isHidden:!1},e}function Mu(e,t,n){return(e=Ou(6,e,null,t)).lanes=n,e}function Iu(e,t,n){return(t=Ou(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uu(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=yt(0),this.expirationTimes=yt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=yt(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Bu(e,t,n,r,i,o,s,a,l){return e=new Uu(e,t,n,a,l),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Ou(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Mo(o),e}function Vu(e){if(!e)return Ai;e:{if(ze(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ji(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(ji(n))return Di(e,n,t)}return t}function zu(e,t,n,r,i,o,s,a,l){return(e=Bu(n,r,!0,e,0,o,0,a,l)).context=Vu(null),n=e.current,(o=Uo(r=eu(),i=tu(n))).callback=void 0!==t&&null!==t?t:null,Bo(n,o,i),e.current.lanes=i,gt(e,i,r),ru(e,r),e}function qu(e,t,n,r){var i=t.current,o=eu(),s=tu(i);return n=Vu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Uo(o,s)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Bo(i,t,s))&&(nu(e,i,s,o),Vo(e,i,s)),s}function Qu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Hu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function $u(e,t){Hu(e,t),(e=e.alternate)&&Hu(e,t)}El=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ri.current)ba=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return ba=!1,function(e,t,n){switch(t.tag){case 3:Ra(t),po();break;case 5:Jo(t);break;case 1:ji(t.type)&&Fi(t);break;case 4:Go(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;Pi(Eo,r._currentValue),r._currentValue=i;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Pi(es,1&es.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ma(e,t,n):(Pi(es,1&es.current),null!==(e=Qa(e,t,n))?e.sibling:null);Pi(es,1&es.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return za(e,t,n);t.flags|=128}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),Pi(es,es.current),r)break;return null;case 22:case 23:return t.lanes=0,ka(e,t,n)}return Qa(e,t,n)}(e,t,n);ba=0!==(131072&e.flags)}else ba=!1,io&&0!==(1048576&t.flags)&&Zi(t,$i,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;qa(e,t),e=t.pendingProps;var i=Oi(t,Ti.current);_o(t,n),i=ys(null,t,r,e,i,n);var s=gs();return t.flags|=1,"object"===typeof i&&null!==i&&"function"===typeof i.render&&void 0===i.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ji(r)?(s=!0,Fi(t)):s=!1,t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,Mo(t),i.updater=ia,t.stateNode=i,i._reactInternals=t,la(t,r,e,n),t=Ta(null,t,r,!0,s,n)):(t.tag=0,io&&s&&eo(t),wa(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(qa(e,t),e=t.pendingProps,r=(i=r._init)(r._payload),t.type=r,i=t.tag=function(e){if("function"===typeof e)return ju(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===T)return 11;if(e===O)return 14}return 2}(r),e=na(r,e),i){case 0:t=Pa(null,t,r,e,n);break e;case 1:t=Aa(null,t,r,e,n);break e;case 11:t=xa(null,t,r,e,n);break e;case 14:t=Sa(null,t,r,na(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,Pa(e,t,r,i=t.elementType===r?i:na(r,i),n);case 1:return r=t.type,i=t.pendingProps,Aa(e,t,r,i=t.elementType===r?i:na(r,i),n);case 3:e:{if(Ra(t),null===e)throw Error(o(387));r=t.pendingProps,i=(s=t.memoizedState).element,Io(e,t),qo(t,r,null,n);var a=t.memoizedState;if(r=a.element,s.isDehydrated){if(s={element:r,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=s,t.memoizedState=s,256&t.flags){t=_a(e,t,r,n,i=ua(Error(o(423)),t));break e}if(r!==i){t=_a(e,t,r,n,i=ua(Error(o(424)),t));break e}for(ro=ui(t.stateNode.containerInfo.firstChild),no=t,io=!0,oo=null,n=So(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(po(),r===i){t=Qa(e,t,n);break e}wa(e,t,r,n)}t=t.child}return t;case 5:return Jo(t),null===e&&uo(t),r=t.type,i=t.pendingProps,s=null!==e?e.memoizedProps:null,a=i.children,ni(r,i)?a=null:null!==s&&ni(r,s)&&(t.flags|=32),Ca(e,t),wa(e,t,a,n),t.child;case 6:return null===e&&uo(t),null;case 13:return Ma(e,t,n);case 4:return Go(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xo(t,null,r,n):wa(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,xa(e,t,r,i=t.elementType===r?i:na(r,i),n);case 7:return wa(e,t,t.pendingProps,n),t.child;case 8:case 12:return wa(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,a=i.value,Pi(Eo,r._currentValue),r._currentValue=a,null!==s)if(ar(s.value,a)){if(s.children===i.children&&!Ri.current){t=Qa(e,t,n);break e}}else for(null!==(s=t.child)&&(s.return=t);null!==s;){var l=s.dependencies;if(null!==l){a=s.child;for(var u=l.firstContext;null!==u;){if(u.context===r){if(1===s.tag){(u=Uo(-1,n&-n)).tag=2;var c=s.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}s.lanes|=n,null!==(u=s.alternate)&&(u.lanes|=n),Ro(s.return,n,t),l.lanes|=n;break}u=u.next}}else if(10===s.tag)a=s.type===t.type?null:s.child;else if(18===s.tag){if(null===(a=s.return))throw Error(o(341));a.lanes|=n,null!==(l=a.alternate)&&(l.lanes|=n),Ro(a,n,t),a=s.sibling}else a=s.child;if(null!==a)a.return=s;else for(a=s;null!==a;){if(a===t){a=null;break}if(null!==(s=a.sibling)){s.return=a.return,a=s;break}a=a.return}s=a}wa(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,_o(t,n),r=r(i=Oo(i)),t.flags|=1,wa(e,t,r,n),t.child;case 14:return i=na(r=t.type,t.pendingProps),Sa(e,t,r,i=na(r.type,i),n);case 15:return Ea(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:na(r,i),qa(e,t),t.tag=1,ji(r)?(e=!0,Fi(t)):e=!1,_o(t,n),sa(t,r,i),la(t,r,i,n),Ta(null,t,r,!0,e,n);case 19:return za(e,t,n);case 22:return ka(e,t,n)}throw Error(o(156,t.tag))};var Wu="function"===typeof reportError?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}function Yu(e){this._internalRoot=e}function Gu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Ju(){}function Zu(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if("function"===typeof i){var a=i;i=function(){var e=Qu(s);a.call(e)}}qu(t,s,e,i)}else s=function(e,t,n,r,i){if(i){if("function"===typeof r){var o=r;r=function(){var e=Qu(s);o.call(e)}}var s=zu(t,r,e,0,null,!1,0,"",Ju);return e._reactRootContainer=s,e[pi]=s.current,zr(8===e.nodeType?e.parentNode:e),cu(),s}for(;i=e.lastChild;)e.removeChild(i);if("function"===typeof r){var a=r;r=function(){var e=Qu(l);a.call(e)}}var l=Bu(e,0,!1,null,0,!1,0,"",Ju);return e._reactRootContainer=l,e[pi]=l.current,zr(8===e.nodeType?e.parentNode:e),cu((function(){qu(t,l,n,r)})),l}(n,t,e,i,r);return Qu(s)}Yu.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));qu(e,t,null,null)},Yu.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu((function(){qu(null,e,null,null)})),t[pi]=null}},Yu.prototype.unstable_scheduleHydration=function(e){if(e){var t=kt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Nt.length&&0!==t&&t<Nt[n].priority;n++);Nt.splice(n,0,e),0===n&&Mt(e)}},xt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(vt(t,1|n),ru(t,Xe()),0===(6&Tl)&&(zl=Xe()+500,zi()))}break;case 13:cu((function(){var t=Do(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}})),$u(e,1)}},St=function(e){if(13===e.tag){var t=Do(e,134217728);if(null!==t)nu(t,e,134217728,eu());$u(e,134217728)}},Et=function(e){if(13===e.tag){var t=tu(e),n=Do(e,t);if(null!==n)nu(n,e,t,eu());$u(e,t)}},kt=function(){return bt},Ct=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(J(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=xi(r);if(!i)throw Error(o(90));W(r),J(r,i)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Te=uu,Re=cu;var ec={usingClientEntryPoint:!1,Events:[bi,wi,xi,Pe,Ae,uu]},tc={findFiberByHostInstance:vi,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=He(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{it=rc.inject(nc),ot=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Gu(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Gu(e))throw Error(o(299));var n=!1,r="",i=Wu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(i=t.onRecoverableError)),t=Bu(e,1,!1,null,0,n,0,r,i),e[pi]=t.current,zr(8===e.nodeType?e.parentNode:e),new Ku(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=He(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Xu(t))throw Error(o(200));return Zu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Gu(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,i=!1,s="",a=Wu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(i=!0),void 0!==n.identifierPrefix&&(s=n.identifierPrefix),void 0!==n.onRecoverableError&&(a=n.onRecoverableError)),t=zu(t,null,e,1,null!=n?n:null,i,0,s,a),e[pi]=t.current,zr(e),r)for(e=0;e<r.length;e++)i=(i=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Yu(t)},t.render=function(e,t,n){if(!Xu(t))throw Error(o(200));return Zu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Xu(e))throw Error(o(40));return!!e._reactRootContainer&&(cu((function(){Zu(null,null,e,!1,(function(){e._reactRootContainer=null,e[pi]=null}))})),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xu(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return Zu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},362:()=>{},367:(e,t,n)=>{"use strict";var r=n(766);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},371:(e,t,n)=>{"use strict";n.d(t,{B:()=>o,t:()=>i});var r=console;function i(){return r}function o(e){r=e}},419:()=>{},427:(e,t,n)=>{"use strict";n.d(t,{QueryClient:()=>r.E});var r=n(466),i=n(922);n.o(i,"QueryClientProvider")&&n.d(t,{QueryClientProvider:function(){return i.QueryClientProvider}}),n.o(i,"useQuery")&&n.d(t,{useQuery:function(){return i.useQuery}})},466:(e,t,n)=>{"use strict";n.d(t,{E:()=>v});var r=n(102),i=n(206),o=n(994),s=n(175),a=n(371),l=n(582),u=function(){function e(e){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.cache=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.initialState=e.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=e.meta,this.scheduleGc()}var t=e.prototype;return t.setOptions=function(e){var t;this.options=(0,r.A)({},this.defaultOptions,e),this.meta=null==e?void 0:e.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(t=this.options.cacheTime)?t:3e5)},t.setDefaultOptions=function(e){this.defaultOptions=e},t.scheduleGc=function(){var e=this;this.clearGcTimeout(),(0,i.gn)(this.cacheTime)&&(this.gcTimeout=setTimeout((function(){e.optionalRemove()}),this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(e,t){var n,r,o=this.state.data,s=(0,i.Zw)(e,o);return(null==(n=(r=this.options).isDataEqual)?void 0:n.call(r,o,s))?s=o:!1!==this.options.structuralSharing&&(s=(0,i.BH)(o,s)),this.dispatch({data:s,type:"success",dataUpdatedAt:null==t?void 0:t.updatedAt}),s},t.setState=function(e,t){this.dispatch({type:"setState",state:e,setStateOptions:t})},t.cancel=function(e){var t,n=this.promise;return null==(t=this.retryer)||t.cancel(e),n?n.then(i.lQ).catch(i.lQ):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some((function(e){return!1!==e.options.enabled}))},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some((function(e){return e.getCurrentResult().isStale}))},t.isStaleByTime=function(e){return void 0===e&&(e=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,i.j3)(this.state.dataUpdatedAt,e)},t.onFocus=function(){var e,t=this.observers.find((function(e){return e.shouldFetchOnWindowFocus()}));t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.onOnline=function(){var e,t=this.observers.find((function(e){return e.shouldFetchOnReconnect()}));t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.addObserver=function(e){-1===this.observers.indexOf(e)&&(this.observers.push(e),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:e}))},t.removeObserver=function(e){-1!==this.observers.indexOf(e)&&(this.observers=this.observers.filter((function(t){return t!==e})),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:e}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(e,t){var n,r,o,s=this;if(this.state.isFetching)if(this.state.dataUpdatedAt&&(null==t?void 0:t.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var u;return null==(u=this.retryer)||u.continueRetry(),this.promise}if(e&&this.setOptions(e),!this.options.queryFn){var c=this.observers.find((function(e){return e.options.queryFn}));c&&this.setOptions(c.options)}var d=(0,i.HN)(this.queryKey),f=(0,i.jY)(),h={queryKey:d,pageParam:void 0,meta:this.meta};Object.defineProperty(h,"signal",{enumerable:!0,get:function(){if(f)return s.abortSignalConsumed=!0,f.signal}});var p,m,y={fetchOptions:t,options:this.options,queryKey:d,state:this.state,fetchFn:function(){return s.options.queryFn?(s.abortSignalConsumed=!1,s.options.queryFn(h)):Promise.reject("Missing queryFn")},meta:this.meta};(null==(n=this.options.behavior)?void 0:n.onFetch)&&(null==(p=this.options.behavior)||p.onFetch(y));(this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(r=y.fetchOptions)?void 0:r.meta))||this.dispatch({type:"fetch",meta:null==(m=y.fetchOptions)?void 0:m.meta});return this.retryer=new l.eJ({fn:y.fetchFn,abort:null==f||null==(o=f.abort)?void 0:o.bind(f),onSuccess:function(e){s.setData(e),null==s.cache.config.onSuccess||s.cache.config.onSuccess(e,s),0===s.cacheTime&&s.optionalRemove()},onError:function(e){(0,l.wm)(e)&&e.silent||s.dispatch({type:"error",error:e}),(0,l.wm)(e)||(null==s.cache.config.onError||s.cache.config.onError(e,s),(0,a.t)().error(e)),0===s.cacheTime&&s.optionalRemove()},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:y.options.retry,retryDelay:y.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(e){var t=this;this.state=this.reducer(this.state,e),s.j.batch((function(){t.observers.forEach((function(t){t.onQueryUpdate(e)})),t.cache.notify({query:t,type:"queryUpdated",action:e})}))},t.getDefaultState=function(e){var t="function"===typeof e.initialData?e.initialData():e.initialData,n="undefined"!==typeof e.initialData?"function"===typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0,r="undefined"!==typeof t;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:r?"success":"idle"}},t.reducer=function(e,t){var n,i;switch(t.type){case"failed":return(0,r.A)({},e,{fetchFailureCount:e.fetchFailureCount+1});case"pause":return(0,r.A)({},e,{isPaused:!0});case"continue":return(0,r.A)({},e,{isPaused:!1});case"fetch":return(0,r.A)({},e,{fetchFailureCount:0,fetchMeta:null!=(n=t.meta)?n:null,isFetching:!0,isPaused:!1},!e.dataUpdatedAt&&{error:null,status:"loading"});case"success":return(0,r.A)({},e,{data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:null!=(i=t.dataUpdatedAt)?i:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var o=t.error;return(0,l.wm)(o)&&o.revert&&this.revertState?(0,r.A)({},this.revertState):(0,r.A)({},e,{error:o,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return(0,r.A)({},e,{isInvalidated:!0});case"setState":return(0,r.A)({},e,t.state);default:return e}},e}(),c=n(678),d=function(e){function t(t){var n;return(n=e.call(this)||this).config=t||{},n.queries=[],n.queriesMap={},n}(0,o.A)(t,e);var n=t.prototype;return n.build=function(e,t,n){var r,o=t.queryKey,s=null!=(r=t.queryHash)?r:(0,i.F$)(o,t),a=this.get(s);return a||(a=new u({cache:this,queryKey:o,queryHash:s,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(o),meta:t.meta}),this.add(a)),a},n.add=function(e){this.queriesMap[e.queryHash]||(this.queriesMap[e.queryHash]=e,this.queries.push(e),this.notify({type:"queryAdded",query:e}))},n.remove=function(e){var t=this.queriesMap[e.queryHash];t&&(e.destroy(),this.queries=this.queries.filter((function(t){return t!==e})),t===e&&delete this.queriesMap[e.queryHash],this.notify({type:"queryRemoved",query:e}))},n.clear=function(){var e=this;s.j.batch((function(){e.queries.forEach((function(t){e.remove(t)}))}))},n.get=function(e){return this.queriesMap[e]},n.getAll=function(){return this.queries},n.find=function(e,t){var n=(0,i.b_)(e,t)[0];return"undefined"===typeof n.exact&&(n.exact=!0),this.queries.find((function(e){return(0,i.MK)(n,e)}))},n.findAll=function(e,t){var n=(0,i.b_)(e,t)[0];return Object.keys(n).length>0?this.queries.filter((function(e){return(0,i.MK)(n,e)})):this.queries},n.notify=function(e){var t=this;s.j.batch((function(){t.listeners.forEach((function(t){t(e)}))}))},n.onFocus=function(){var e=this;s.j.batch((function(){e.queries.forEach((function(e){e.onFocus()}))}))},n.onOnline=function(){var e=this;s.j.batch((function(){e.queries.forEach((function(e){e.onOnline()}))}))},t}(c.Q),f=function(){function e(e){this.options=(0,r.A)({},e.defaultOptions,e.options),this.mutationId=e.mutationId,this.mutationCache=e.mutationCache,this.observers=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0},this.meta=e.meta}var t=e.prototype;return t.setState=function(e){this.dispatch({type:"setState",state:e})},t.addObserver=function(e){-1===this.observers.indexOf(e)&&this.observers.push(e)},t.removeObserver=function(e){this.observers=this.observers.filter((function(t){return t!==e}))},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(i.lQ).catch(i.lQ)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var e,t=this,n="loading"===this.state.status,r=Promise.resolve();return n||(this.dispatch({type:"loading",variables:this.options.variables}),r=r.then((function(){null==t.mutationCache.config.onMutate||t.mutationCache.config.onMutate(t.state.variables,t)})).then((function(){return null==t.options.onMutate?void 0:t.options.onMutate(t.state.variables)})).then((function(e){e!==t.state.context&&t.dispatch({type:"loading",context:e,variables:t.state.variables})}))),r.then((function(){return t.executeMutation()})).then((function(n){e=n,null==t.mutationCache.config.onSuccess||t.mutationCache.config.onSuccess(e,t.state.variables,t.state.context,t)})).then((function(){return null==t.options.onSuccess?void 0:t.options.onSuccess(e,t.state.variables,t.state.context)})).then((function(){return null==t.options.onSettled?void 0:t.options.onSettled(e,null,t.state.variables,t.state.context)})).then((function(){return t.dispatch({type:"success",data:e}),e})).catch((function(e){return null==t.mutationCache.config.onError||t.mutationCache.config.onError(e,t.state.variables,t.state.context,t),(0,a.t)().error(e),Promise.resolve().then((function(){return null==t.options.onError?void 0:t.options.onError(e,t.state.variables,t.state.context)})).then((function(){return null==t.options.onSettled?void 0:t.options.onSettled(void 0,e,t.state.variables,t.state.context)})).then((function(){throw t.dispatch({type:"error",error:e}),e}))}))},t.executeMutation=function(){var e,t=this;return this.retryer=new l.eJ({fn:function(){return t.options.mutationFn?t.options.mutationFn(t.state.variables):Promise.reject("No mutationFn found")},onFail:function(){t.dispatch({type:"failed"})},onPause:function(){t.dispatch({type:"pause"})},onContinue:function(){t.dispatch({type:"continue"})},retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(e){var t=this;this.state=function(e,t){switch(t.type){case"failed":return(0,r.A)({},e,{failureCount:e.failureCount+1});case"pause":return(0,r.A)({},e,{isPaused:!0});case"continue":return(0,r.A)({},e,{isPaused:!1});case"loading":return(0,r.A)({},e,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return(0,r.A)({},e,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return(0,r.A)({},e,{data:void 0,error:t.error,failureCount:e.failureCount+1,isPaused:!1,status:"error"});case"setState":return(0,r.A)({},e,t.state);default:return e}}(this.state,e),s.j.batch((function(){t.observers.forEach((function(t){t.onMutationUpdate(e)})),t.mutationCache.notify(t)}))},e}();var h=function(e){function t(t){var n;return(n=e.call(this)||this).config=t||{},n.mutations=[],n.mutationId=0,n}(0,o.A)(t,e);var n=t.prototype;return n.build=function(e,t,n){var r=new f({mutationCache:this,mutationId:++this.mutationId,options:e.defaultMutationOptions(t),state:n,defaultOptions:t.mutationKey?e.getMutationDefaults(t.mutationKey):void 0,meta:t.meta});return this.add(r),r},n.add=function(e){this.mutations.push(e),this.notify(e)},n.remove=function(e){this.mutations=this.mutations.filter((function(t){return t!==e})),e.cancel(),this.notify(e)},n.clear=function(){var e=this;s.j.batch((function(){e.mutations.forEach((function(t){e.remove(t)}))}))},n.getAll=function(){return this.mutations},n.find=function(e){return"undefined"===typeof e.exact&&(e.exact=!0),this.mutations.find((function(t){return(0,i.nJ)(e,t)}))},n.findAll=function(e){return this.mutations.filter((function(t){return(0,i.nJ)(e,t)}))},n.notify=function(e){var t=this;s.j.batch((function(){t.listeners.forEach((function(t){t(e)}))}))},n.onFocus=function(){this.resumePausedMutations()},n.onOnline=function(){this.resumePausedMutations()},n.resumePausedMutations=function(){var e=this.mutations.filter((function(e){return e.state.isPaused}));return s.j.batch((function(){return e.reduce((function(e,t){return e.then((function(){return t.continue().catch(i.lQ)}))}),Promise.resolve())}))},t}(c.Q),p=n(908),m=n(61);function y(e,t){return null==e.getNextPageParam?void 0:e.getNextPageParam(t[t.length-1],t)}function g(e,t){return null==e.getPreviousPageParam?void 0:e.getPreviousPageParam(t[0],t)}var v=function(){function e(e){void 0===e&&(e={}),this.queryCache=e.queryCache||new d,this.mutationCache=e.mutationCache||new h,this.defaultOptions=e.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=e.prototype;return t.mount=function(){var e=this;this.unsubscribeFocus=p.m.subscribe((function(){p.m.isFocused()&&m.t.isOnline()&&(e.mutationCache.onFocus(),e.queryCache.onFocus())})),this.unsubscribeOnline=m.t.subscribe((function(){p.m.isFocused()&&m.t.isOnline()&&(e.mutationCache.onOnline(),e.queryCache.onOnline())}))},t.unmount=function(){var e,t;null==(e=this.unsubscribeFocus)||e.call(this),null==(t=this.unsubscribeOnline)||t.call(this)},t.isFetching=function(e,t){var n=(0,i.b_)(e,t)[0];return n.fetching=!0,this.queryCache.findAll(n).length},t.isMutating=function(e){return this.mutationCache.findAll((0,r.A)({},e,{fetching:!0})).length},t.getQueryData=function(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state.data},t.getQueriesData=function(e){return this.getQueryCache().findAll(e).map((function(e){return[e.queryKey,e.state.data]}))},t.setQueryData=function(e,t,n){var r=(0,i.vh)(e),o=this.defaultQueryOptions(r);return this.queryCache.build(this,o).setData(t,n)},t.setQueriesData=function(e,t,n){var r=this;return s.j.batch((function(){return r.getQueryCache().findAll(e).map((function(e){var i=e.queryKey;return[i,r.setQueryData(i,t,n)]}))}))},t.getQueryState=function(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state},t.removeQueries=function(e,t){var n=(0,i.b_)(e,t)[0],r=this.queryCache;s.j.batch((function(){r.findAll(n).forEach((function(e){r.remove(e)}))}))},t.resetQueries=function(e,t,n){var o=this,a=(0,i.b_)(e,t,n),l=a[0],u=a[1],c=this.queryCache,d=(0,r.A)({},l,{active:!0});return s.j.batch((function(){return c.findAll(l).forEach((function(e){e.reset()})),o.refetchQueries(d,u)}))},t.cancelQueries=function(e,t,n){var r=this,o=(0,i.b_)(e,t,n),a=o[0],l=o[1],u=void 0===l?{}:l;"undefined"===typeof u.revert&&(u.revert=!0);var c=s.j.batch((function(){return r.queryCache.findAll(a).map((function(e){return e.cancel(u)}))}));return Promise.all(c).then(i.lQ).catch(i.lQ)},t.invalidateQueries=function(e,t,n){var o,a,l,u=this,c=(0,i.b_)(e,t,n),d=c[0],f=c[1],h=(0,r.A)({},d,{active:null==(o=null!=(a=d.refetchActive)?a:d.active)||o,inactive:null!=(l=d.refetchInactive)&&l});return s.j.batch((function(){return u.queryCache.findAll(d).forEach((function(e){e.invalidate()})),u.refetchQueries(h,f)}))},t.refetchQueries=function(e,t,n){var o=this,a=(0,i.b_)(e,t,n),l=a[0],u=a[1],c=s.j.batch((function(){return o.queryCache.findAll(l).map((function(e){return e.fetch(void 0,(0,r.A)({},u,{meta:{refetchPage:null==l?void 0:l.refetchPage}}))}))})),d=Promise.all(c).then(i.lQ);return(null==u?void 0:u.throwOnError)||(d=d.catch(i.lQ)),d},t.fetchQuery=function(e,t,n){var r=(0,i.vh)(e,t,n),o=this.defaultQueryOptions(r);"undefined"===typeof o.retry&&(o.retry=!1);var s=this.queryCache.build(this,o);return s.isStaleByTime(o.staleTime)?s.fetch(o):Promise.resolve(s.state.data)},t.prefetchQuery=function(e,t,n){return this.fetchQuery(e,t,n).then(i.lQ).catch(i.lQ)},t.fetchInfiniteQuery=function(e,t,n){var r=(0,i.vh)(e,t,n);return r.behavior={onFetch:function(e){e.fetchFn=function(){var t,n,r,o,s,a,u,c=null==(t=e.fetchOptions)||null==(n=t.meta)?void 0:n.refetchPage,d=null==(r=e.fetchOptions)||null==(o=r.meta)?void 0:o.fetchMore,f=null==d?void 0:d.pageParam,h="forward"===(null==d?void 0:d.direction),p="backward"===(null==d?void 0:d.direction),m=(null==(s=e.state.data)?void 0:s.pages)||[],v=(null==(a=e.state.data)?void 0:a.pageParams)||[],b=(0,i.jY)(),w=null==b?void 0:b.signal,x=v,S=!1,E=e.options.queryFn||function(){return Promise.reject("Missing queryFn")},k=function(e,t,n,r){return x=r?[t].concat(x):[].concat(x,[t]),r?[n].concat(e):[].concat(e,[n])},C=function(t,n,r,i){if(S)return Promise.reject("Cancelled");if("undefined"===typeof r&&!n&&t.length)return Promise.resolve(t);var o={queryKey:e.queryKey,signal:w,pageParam:r,meta:e.meta},s=E(o),a=Promise.resolve(s).then((function(e){return k(t,r,e,i)}));return(0,l.dd)(s)&&(a.cancel=s.cancel),a};if(m.length)if(h){var P="undefined"!==typeof f,A=P?f:y(e.options,m);u=C(m,P,A)}else if(p){var T="undefined"!==typeof f,R=T?f:g(e.options,m);u=C(m,T,R,!0)}else!function(){x=[];var t="undefined"===typeof e.options.getNextPageParam,n=!c||!m[0]||c(m[0],0,m);u=n?C([],t,v[0]):Promise.resolve(k([],v[0],m[0]));for(var r=function(n){u=u.then((function(r){if(!c||!m[n]||c(m[n],n,m)){var i=t?v[n]:y(e.options,r);return C(r,t,i)}return Promise.resolve(k(r,v[n],m[n]))}))},i=1;i<m.length;i++)r(i)}();else u=C([]);var _=u.then((function(e){return{pages:e,pageParams:x}}));return _.cancel=function(){S=!0,null==b||b.abort(),(0,l.dd)(u)&&u.cancel()},_}}},this.fetchQuery(r)},t.prefetchInfiniteQuery=function(e,t,n){return this.fetchInfiniteQuery(e,t,n).then(i.lQ).catch(i.lQ)},t.cancelMutations=function(){var e=this,t=s.j.batch((function(){return e.mutationCache.getAll().map((function(e){return e.cancel()}))}));return Promise.all(t).then(i.lQ).catch(i.lQ)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(e){return this.mutationCache.build(this,e).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(e){this.defaultOptions=e},t.setQueryDefaults=function(e,t){var n=this.queryDefaults.find((function(t){return(0,i.Od)(e)===(0,i.Od)(t.queryKey)}));n?n.defaultOptions=t:this.queryDefaults.push({queryKey:e,defaultOptions:t})},t.getQueryDefaults=function(e){var t;return e?null==(t=this.queryDefaults.find((function(t){return(0,i.Cp)(e,t.queryKey)})))?void 0:t.defaultOptions:void 0},t.setMutationDefaults=function(e,t){var n=this.mutationDefaults.find((function(t){return(0,i.Od)(e)===(0,i.Od)(t.mutationKey)}));n?n.defaultOptions=t:this.mutationDefaults.push({mutationKey:e,defaultOptions:t})},t.getMutationDefaults=function(e){var t;return e?null==(t=this.mutationDefaults.find((function(t){return(0,i.Cp)(e,t.mutationKey)})))?void 0:t.defaultOptions:void 0},t.defaultQueryOptions=function(e){if(null==e?void 0:e._defaulted)return e;var t=(0,r.A)({},this.defaultOptions.queries,this.getQueryDefaults(null==e?void 0:e.queryKey),e,{_defaulted:!0});return!t.queryHash&&t.queryKey&&(t.queryHash=(0,i.F$)(t.queryKey,t)),t},t.defaultQueryObserverOptions=function(e){return this.defaultQueryOptions(e)},t.defaultMutationOptions=function(e){return(null==e?void 0:e._defaulted)?e:(0,r.A)({},this.defaultOptions.mutations,this.getMutationDefaults(null==e?void 0:e.mutationKey),e,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},e}()},498:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,i=e[r];if(!(0<o(i,t)))break e;e[r]=t,e[n]=i,n=r}}function r(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length,s=i>>>1;r<s;){var a=2*(r+1)-1,l=e[a],u=a+1,c=e[u];if(0>o(l,n))u<i&&0>o(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[a]=n,r=a);else{if(!(u<i&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var s=performance;t.unstable_now=function(){return s.now()}}else{var a=Date,l=a.now();t.unstable_now=function(){return a.now()-l}}var u=[],c=[],d=1,f=null,h=3,p=!1,m=!1,y=!1,g="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)i(c);else{if(!(t.startTime<=e))break;i(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function x(e){if(y=!1,w(e),!m)if(null!==r(u))m=!0,N(S);else{var t=r(c);null!==t&&L(x,t.startTime-e)}}function S(e,n){m=!1,y&&(y=!1,v(P),P=-1),p=!0;var o=h;try{for(w(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!R());){var s=f.callback;if("function"===typeof s){f.callback=null,h=f.priorityLevel;var a=s(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof a?f.callback=a:f===r(u)&&i(u),w(n)}else i(u);f=r(u)}if(null!==f)var l=!0;else{var d=r(c);null!==d&&L(x,d.startTime-n),l=!1}return l}finally{f=null,h=o,p=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var E,k=!1,C=null,P=-1,A=5,T=-1;function R(){return!(t.unstable_now()-T<A)}function _(){if(null!==C){var e=t.unstable_now();T=e;var n=!0;try{n=C(!0,e)}finally{n?E():(k=!1,C=null)}}else k=!1}if("function"===typeof b)E=function(){b(_)};else if("undefined"!==typeof MessageChannel){var O=new MessageChannel,j=O.port2;O.port1.onmessage=_,E=function(){j.postMessage(null)}}else E=function(){g(_,0)};function N(e){C=e,k||(k=!0,E())}function L(e,n){P=g((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,N(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):A=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,i,o){var s=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?s+o:s:o=s,e){case 1:var a=-1;break;case 2:a=250;break;case 5:a=1073741823;break;case 4:a=1e4;break;default:a=5e3}return e={id:d++,callback:i,priorityLevel:e,startTime:o,expirationTime:a=o+a,sortIndex:-1},o>s?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(y?(v(P),P=-1):y=!0,L(x,o-s))):(e.sortIndex=a,n(u,e),m||p||(m=!0,N(S))),e},t.unstable_shouldYield=R,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},507:(e,t,n)=>{"use strict";e.exports=n(241)},547:(e,t,n)=>{"use strict";n.d(t,{QueryClient:()=>r.QueryClient,QueryClientProvider:()=>i.QueryClientProvider,useQuery:()=>i.useQuery});var r=n(427);n.o(r,"QueryClientProvider")&&n.d(t,{QueryClientProvider:function(){return r.QueryClientProvider}}),n.o(r,"useQuery")&&n.d(t,{useQuery:function(){return r.useQuery}});var i=n(695)},567:()=>{},582:(e,t,n)=>{"use strict";n.d(t,{dd:()=>a,eJ:()=>c,wm:()=>u});var r=n(908),i=n(61),o=n(206);function s(e){return Math.min(1e3*Math.pow(2,e),3e4)}function a(e){return"function"===typeof(null==e?void 0:e.cancel)}var l=function(e){this.revert=null==e?void 0:e.revert,this.silent=null==e?void 0:e.silent};function u(e){return e instanceof l}var c=function(e){var t,n,u,c,d=this,f=!1;this.abort=e.abort,this.cancel=function(e){return null==t?void 0:t(e)},this.cancelRetry=function(){f=!0},this.continueRetry=function(){f=!1},this.continue=function(){return null==n?void 0:n()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise((function(e,t){u=e,c=t}));var h=function(t){d.isResolved||(d.isResolved=!0,null==e.onSuccess||e.onSuccess(t),null==n||n(),u(t))},p=function(t){d.isResolved||(d.isResolved=!0,null==e.onError||e.onError(t),null==n||n(),c(t))};!function u(){if(!d.isResolved){var c;try{c=e.fn()}catch(m){c=Promise.reject(m)}t=function(e){if(!d.isResolved&&(p(new l(e)),null==d.abort||d.abort(),a(c)))try{c.cancel()}catch(t){}},d.isTransportCancelable=a(c),Promise.resolve(c).then(h).catch((function(t){var a,l;if(!d.isResolved){var c=null!=(a=e.retry)?a:3,h=null!=(l=e.retryDelay)?l:s,m="function"===typeof h?h(d.failureCount,t):h,y=!0===c||"number"===typeof c&&d.failureCount<c||"function"===typeof c&&c(d.failureCount,t);!f&&y?(d.failureCount++,null==e.onFail||e.onFail(d.failureCount,t),(0,o.yy)(m).then((function(){if(!r.m.isFocused()||!i.t.isOnline())return new Promise((function(t){n=t,d.isPaused=!0,null==e.onPause||e.onPause()})).then((function(){n=void 0,d.isPaused=!1,null==e.onContinue||e.onContinue()}))})).then((function(){f?p(t):u()}))):p(t)}}))}}()}},615:(e,t)=>{"use strict";var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,i=n?Symbol.for("react.portal"):60106,o=n?Symbol.for("react.fragment"):60107,s=n?Symbol.for("react.strict_mode"):60108,a=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,u=n?Symbol.for("react.context"):60110,c=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,h=n?Symbol.for("react.suspense"):60113,p=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,y=n?Symbol.for("react.lazy"):60116,g=n?Symbol.for("react.block"):60121,v=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function x(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case c:case d:case o:case a:case s:case h:return e;default:switch(e=e&&e.$$typeof){case u:case f:case y:case m:case l:return e;default:return t}}case i:return t}}}function S(e){return x(e)===d}t.AsyncMode=c,t.ConcurrentMode=d,t.ContextConsumer=u,t.ContextProvider=l,t.Element=r,t.ForwardRef=f,t.Fragment=o,t.Lazy=y,t.Memo=m,t.Portal=i,t.Profiler=a,t.StrictMode=s,t.Suspense=h,t.isAsyncMode=function(e){return S(e)||x(e)===c},t.isConcurrentMode=S,t.isContextConsumer=function(e){return x(e)===u},t.isContextProvider=function(e){return x(e)===l},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return x(e)===f},t.isFragment=function(e){return x(e)===o},t.isLazy=function(e){return x(e)===y},t.isMemo=function(e){return x(e)===m},t.isPortal=function(e){return x(e)===i},t.isProfiler=function(e){return x(e)===a},t.isStrictMode=function(e){return x(e)===s},t.isSuspense=function(e){return x(e)===h},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===o||e===d||e===a||e===s||e===h||e===p||"object"===typeof e&&null!==e&&(e.$$typeof===y||e.$$typeof===m||e.$$typeof===l||e.$$typeof===u||e.$$typeof===f||e.$$typeof===v||e.$$typeof===b||e.$$typeof===w||e.$$typeof===g)},t.typeOf=x},643:(e,t,n)=>{"use strict";e.exports=n(226)},678:(e,t,n)=>{"use strict";n.d(t,{Q:()=>r});var r=function(){function e(){this.listeners=[]}var t=e.prototype;return t.subscribe=function(e){var t=this,n=e||function(){};return this.listeners.push(n),this.onSubscribe(),function(){t.listeners=t.listeners.filter((function(e){return e!==n})),t.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},e}()},695:(e,t,n)=>{"use strict";n.d(t,{QueryClientProvider:()=>f,useQuery:()=>A});var r=n(175),i=n(766).unstable_batchedUpdates;r.j.setBatchNotifyFunction(i);var o=n(371),s=console;(0,o.B)(s);var a=n(643),l=a.createContext(void 0),u=a.createContext(!1);function c(e){return e&&"undefined"!==typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=l),window.ReactQueryClientContext):l}var d=function(){var e=a.useContext(c(a.useContext(u)));if(!e)throw new Error("No QueryClient set, use QueryClientProvider to set one");return e},f=function(e){var t=e.client,n=e.contextSharing,r=void 0!==n&&n,i=e.children;a.useEffect((function(){return t.mount(),function(){t.unmount()}}),[t]);var o=c(r);return a.createElement(u.Provider,{value:r},a.createElement(o.Provider,{value:t},i))},h=n(102),p=n(994),m=n(206),y=n(908),g=n(678),v=n(582),b=function(e){function t(t,n){var r;return(r=e.call(this)||this).client=t,r.options=n,r.trackedProps=[],r.selectError=null,r.bindMethods(),r.setOptions(n),r}(0,p.A)(t,e);var n=t.prototype;return n.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},n.onSubscribe=function(){1===this.listeners.length&&(this.currentQuery.addObserver(this),w(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},n.onUnsubscribe=function(){this.listeners.length||this.destroy()},n.shouldFetchOnReconnect=function(){return x(this.currentQuery,this.options,this.options.refetchOnReconnect)},n.shouldFetchOnWindowFocus=function(){return x(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},n.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},n.setOptions=function(e,t){var n=this.options,r=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(e),"undefined"!==typeof this.options.enabled&&"boolean"!==typeof this.options.enabled)throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=n.queryKey),this.updateQuery();var i=this.hasListeners();i&&S(this.currentQuery,r,this.options,n)&&this.executeFetch(),this.updateResult(t),!i||this.currentQuery===r&&this.options.enabled===n.enabled&&this.options.staleTime===n.staleTime||this.updateStaleTimeout();var o=this.computeRefetchInterval();!i||this.currentQuery===r&&this.options.enabled===n.enabled&&o===this.currentRefetchInterval||this.updateRefetchInterval(o)},n.getOptimisticResult=function(e){var t=this.client.defaultQueryObserverOptions(e),n=this.client.getQueryCache().build(this.client,t);return this.createResult(n,t)},n.getCurrentResult=function(){return this.currentResult},n.trackResult=function(e,t){var n=this,r={},i=function(e){n.trackedProps.includes(e)||n.trackedProps.push(e)};return Object.keys(e).forEach((function(t){Object.defineProperty(r,t,{configurable:!1,enumerable:!0,get:function(){return i(t),e[t]}})})),(t.useErrorBoundary||t.suspense)&&i("error"),r},n.getNextResult=function(e){var t=this;return new Promise((function(n,r){var i=t.subscribe((function(t){t.isFetching||(i(),t.isError&&(null==e?void 0:e.throwOnError)?r(t.error):n(t))}))}))},n.getCurrentQuery=function(){return this.currentQuery},n.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},n.refetch=function(e){return this.fetch((0,h.A)({},e,{meta:{refetchPage:null==e?void 0:e.refetchPage}}))},n.fetchOptimistic=function(e){var t=this,n=this.client.defaultQueryObserverOptions(e),r=this.client.getQueryCache().build(this.client,n);return r.fetch().then((function(){return t.createResult(r,n)}))},n.fetch=function(e){var t=this;return this.executeFetch(e).then((function(){return t.updateResult(),t.currentResult}))},n.executeFetch=function(e){this.updateQuery();var t=this.currentQuery.fetch(this.options,e);return(null==e?void 0:e.throwOnError)||(t=t.catch(m.lQ)),t},n.updateStaleTimeout=function(){var e=this;if(this.clearStaleTimeout(),!m.S$&&!this.currentResult.isStale&&(0,m.gn)(this.options.staleTime)){var t=(0,m.j3)(this.currentResult.dataUpdatedAt,this.options.staleTime)+1;this.staleTimeoutId=setTimeout((function(){e.currentResult.isStale||e.updateResult()}),t)}},n.computeRefetchInterval=function(){var e;return"function"===typeof this.options.refetchInterval?this.options.refetchInterval(this.currentResult.data,this.currentQuery):null!=(e=this.options.refetchInterval)&&e},n.updateRefetchInterval=function(e){var t=this;this.clearRefetchInterval(),this.currentRefetchInterval=e,!m.S$&&!1!==this.options.enabled&&(0,m.gn)(this.currentRefetchInterval)&&0!==this.currentRefetchInterval&&(this.refetchIntervalId=setInterval((function(){(t.options.refetchIntervalInBackground||y.m.isFocused())&&t.executeFetch()}),this.currentRefetchInterval))},n.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},n.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},n.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},n.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},n.createResult=function(e,t){var n,r=this.currentQuery,i=this.options,s=this.currentResult,a=this.currentResultState,l=this.currentResultOptions,u=e!==r,c=u?e.state:this.currentQueryInitialState,d=u?this.currentResult:this.previousQueryResult,f=e.state,h=f.dataUpdatedAt,p=f.error,y=f.errorUpdatedAt,g=f.isFetching,v=f.status,b=!1,x=!1;if(t.optimisticResults){var k=this.hasListeners(),C=!k&&w(e,t),P=k&&S(e,r,t,i);(C||P)&&(g=!0,h||(v="loading"))}if(t.keepPreviousData&&!f.dataUpdateCount&&(null==d?void 0:d.isSuccess)&&"error"!==v)n=d.data,h=d.dataUpdatedAt,v=d.status,b=!0;else if(t.select&&"undefined"!==typeof f.data)if(s&&f.data===(null==a?void 0:a.data)&&t.select===this.selectFn)n=this.selectResult;else try{this.selectFn=t.select,n=t.select(f.data),!1!==t.structuralSharing&&(n=(0,m.BH)(null==s?void 0:s.data,n)),this.selectResult=n,this.selectError=null}catch(T){(0,o.t)().error(T),this.selectError=T}else n=f.data;if("undefined"!==typeof t.placeholderData&&"undefined"===typeof n&&("loading"===v||"idle"===v)){var A;if((null==s?void 0:s.isPlaceholderData)&&t.placeholderData===(null==l?void 0:l.placeholderData))A=s.data;else if(A="function"===typeof t.placeholderData?t.placeholderData():t.placeholderData,t.select&&"undefined"!==typeof A)try{A=t.select(A),!1!==t.structuralSharing&&(A=(0,m.BH)(null==s?void 0:s.data,A)),this.selectError=null}catch(T){(0,o.t)().error(T),this.selectError=T}"undefined"!==typeof A&&(v="success",n=A,x=!0)}return this.selectError&&(p=this.selectError,n=this.selectResult,y=Date.now(),v="error"),{status:v,isLoading:"loading"===v,isSuccess:"success"===v,isError:"error"===v,isIdle:"idle"===v,data:n,dataUpdatedAt:h,error:p,errorUpdatedAt:y,failureCount:f.fetchFailureCount,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>c.dataUpdateCount||f.errorUpdateCount>c.errorUpdateCount,isFetching:g,isRefetching:g&&"loading"!==v,isLoadingError:"error"===v&&0===f.dataUpdatedAt,isPlaceholderData:x,isPreviousData:b,isRefetchError:"error"===v&&0!==f.dataUpdatedAt,isStale:E(e,t),refetch:this.refetch,remove:this.remove}},n.shouldNotifyListeners=function(e,t){if(!t)return!0;var n=this.options,r=n.notifyOnChangeProps,i=n.notifyOnChangePropsExclusions;if(!r&&!i)return!0;if("tracked"===r&&!this.trackedProps.length)return!0;var o="tracked"===r?this.trackedProps:r;return Object.keys(e).some((function(n){var r=n,s=e[r]!==t[r],a=null==o?void 0:o.some((function(e){return e===n})),l=null==i?void 0:i.some((function(e){return e===n}));return s&&!l&&(!o||a)}))},n.updateResult=function(e){var t=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!(0,m.f8)(this.currentResult,t)){var n={cache:!0};!1!==(null==e?void 0:e.listeners)&&this.shouldNotifyListeners(this.currentResult,t)&&(n.listeners=!0),this.notify((0,h.A)({},n,e))}},n.updateQuery=function(){var e=this.client.getQueryCache().build(this.client,this.options);if(e!==this.currentQuery){var t=this.currentQuery;this.currentQuery=e,this.currentQueryInitialState=e.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(null==t||t.removeObserver(this),e.addObserver(this))}},n.onQueryUpdate=function(e){var t={};"success"===e.type?t.onSuccess=!0:"error"!==e.type||(0,v.wm)(e.error)||(t.onError=!0),this.updateResult(t),this.hasListeners()&&this.updateTimers()},n.notify=function(e){var t=this;r.j.batch((function(){e.onSuccess?(null==t.options.onSuccess||t.options.onSuccess(t.currentResult.data),null==t.options.onSettled||t.options.onSettled(t.currentResult.data,null)):e.onError&&(null==t.options.onError||t.options.onError(t.currentResult.error),null==t.options.onSettled||t.options.onSettled(void 0,t.currentResult.error)),e.listeners&&t.listeners.forEach((function(e){e(t.currentResult)})),e.cache&&t.client.getQueryCache().notify({query:t.currentQuery,type:"observerResultsUpdated"})}))},t}(g.Q);function w(e,t){return function(e,t){return!1!==t.enabled&&!e.state.dataUpdatedAt&&!("error"===e.state.status&&!1===t.retryOnMount)}(e,t)||e.state.dataUpdatedAt>0&&x(e,t,t.refetchOnMount)}function x(e,t,n){if(!1!==t.enabled){var r="function"===typeof n?n(e):n;return"always"===r||!1!==r&&E(e,t)}return!1}function S(e,t,n,r){return!1!==n.enabled&&(e!==t||!1===r.enabled)&&(!n.suspense||"error"!==e.state.status)&&E(e,n)}function E(e,t){return e.isStaleByTime(t.staleTime)}function k(){var e=!1;return{clearReset:function(){e=!1},reset:function(){e=!0},isReset:function(){return e}}}var C=a.createContext(k()),P=function(){return a.useContext(C)};function A(e,t,n){return function(e,t){var n=a.useRef(!1),i=a.useState(0)[1],o=d(),s=P(),l=o.defaultQueryObserverOptions(e);l.optimisticResults=!0,l.onError&&(l.onError=r.j.batchCalls(l.onError)),l.onSuccess&&(l.onSuccess=r.j.batchCalls(l.onSuccess)),l.onSettled&&(l.onSettled=r.j.batchCalls(l.onSettled)),l.suspense&&("number"!==typeof l.staleTime&&(l.staleTime=1e3),0===l.cacheTime&&(l.cacheTime=1)),(l.suspense||l.useErrorBoundary)&&(s.isReset()||(l.retryOnMount=!1));var u,c,f,h=a.useState((function(){return new t(o,l)}))[0],p=h.getOptimisticResult(l);if(a.useEffect((function(){n.current=!0,s.clearReset();var e=h.subscribe(r.j.batchCalls((function(){n.current&&i((function(e){return e+1}))})));return h.updateResult(),function(){n.current=!1,e()}}),[s,h]),a.useEffect((function(){h.setOptions(l,{listeners:!1})}),[l,h]),l.suspense&&p.isLoading)throw h.fetchOptimistic(l).then((function(e){var t=e.data;null==l.onSuccess||l.onSuccess(t),null==l.onSettled||l.onSettled(t,null)})).catch((function(e){s.clearReset(),null==l.onError||l.onError(e),null==l.onSettled||l.onSettled(void 0,e)}));if(p.isError&&!s.isReset()&&!p.isFetching&&(u=l.suspense,c=l.useErrorBoundary,f=[p.error,h.getCurrentQuery()],"function"===typeof c?c.apply(void 0,f):"boolean"===typeof c?c:u))throw p.error;return"tracked"===l.notifyOnChangeProps&&(p=h.trackResult(p,l)),p}((0,m.vh)(e,t,n),b)}},735:()=>{},754:()=>{},766:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(306)},845:()=>{},908:(e,t,n)=>{"use strict";n.d(t,{m:()=>s});var r=n(994),i=n(678),o=n(206),s=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!o.S$&&(null==(t=window)?void 0:t.addEventListener)){var n=function(){return e()};return window.addEventListener("visibilitychange",n,!1),window.addEventListener("focus",n,!1),function(){window.removeEventListener("visibilitychange",n),window.removeEventListener("focus",n)}}},t}(0,r.A)(t,e);var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},n.setEventListener=function(e){var t,n=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e((function(e){"boolean"===typeof e?n.setFocused(e):n.onFocus()}))},n.setFocused=function(e){this.focused=e,e&&this.onFocus()},n.onFocus=function(){this.listeners.forEach((function(e){e()}))},n.isFocused=function(){return"boolean"===typeof this.focused?this.focused:"undefined"===typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},t}(i.Q))},922:()=>{},955:(e,t,n)=>{"use strict";e.exports=n(615)},962:()=>{},994:(e,t,n)=>{"use strict";function r(e,t){return r=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},r(e,t)}function i(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,r(e,t)}n.d(t,{A:()=>i})}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,i){if(1&i&&(r=this(r)),8&i)return r;if("object"===typeof r&&r){if(4&i&&r.__esModule)return r;if(16&i&&"function"===typeof r.then)return r}var o=Object.create(null);n.r(o);var s={};e=e||[null,t({}),t([]),t(t)];for(var a=2&i&&r;"object"==typeof a&&!~e.indexOf(a);a=t(a))Object.getOwnPropertyNames(a).forEach((e=>s[e]=()=>r[e]));return s.default=()=>r,n.d(o,s),o}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};n.r(e),n.d(e,{hasBrowserEnv:()=>Jn,hasStandardBrowserEnv:()=>er,hasStandardBrowserWebWorkerEnv:()=>tr,navigator:()=>Zn,origin:()=>nr});var t={};n.r(t),n.d(t,{Decoder:()=>ms,Encoder:()=>hs,PacketType:()=>fs,protocol:()=>ds});var r,i=n(643),o=n.t(i,2),s=n(367),a=n(766),l=n.t(a,2);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(r||(r={}));const c="popstate";function d(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function f(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(je){}}}function h(e,t){return{usr:e.state,key:e.key,idx:t}}function p(e,t,n,r){return void 0===n&&(n=null),u({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?y(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function m(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function y(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function g(e,t,n,i){void 0===i&&(i={});let{window:o=document.defaultView,v5Compat:s=!1}=i,a=o.history,l=r.Pop,f=null,y=g();function g(){return(a.state||{idx:null}).idx}function v(){l=r.Pop;let e=g(),t=null==e?null:e-y;y=e,f&&f({action:l,location:w.location,delta:t})}function b(e){let t="null"!==o.location.origin?o.location.origin:o.location.href,n="string"===typeof e?e:m(e);return n=n.replace(/ $/,"%20"),d(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==y&&(y=0,a.replaceState(u({},a.state,{idx:y}),""));let w={get action(){return l},get location(){return e(o,a)},listen(e){if(f)throw new Error("A history only accepts one active listener");return o.addEventListener(c,v),f=e,()=>{o.removeEventListener(c,v),f=null}},createHref:e=>t(o,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){l=r.Push;let i=p(w.location,e,t);n&&n(i,e),y=g()+1;let u=h(i,y),c=w.createHref(i);try{a.pushState(u,"",c)}catch(d){if(d instanceof DOMException&&"DataCloneError"===d.name)throw d;o.location.assign(c)}s&&f&&f({action:l,location:w.location,delta:1})},replace:function(e,t){l=r.Replace;let i=p(w.location,e,t);n&&n(i,e),y=g();let o=h(i,y),u=w.createHref(i);a.replaceState(o,"",u),s&&f&&f({action:l,location:w.location,delta:0})},go:e=>a.go(e)};return w}var v;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(v||(v={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function b(e,t,n){return void 0===n&&(n="/"),w(e,t,n,!1)}function w(e,t,n,r){let i=L(("string"===typeof t?y(t):t).pathname||"/",n);if(null==i)return null;let o=x(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let s=null;for(let a=0;null==s&&a<o.length;++a){let e=N(i);s=O(o[a],e,r)}return s}function x(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let i=(e,i,o)=>{let s={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:i,route:e};s.relativePath.startsWith("/")&&(d(s.relativePath.startsWith(r),'Absolute route path "'+s.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),s.relativePath=s.relativePath.slice(r.length));let a=U([r,s.relativePath]),l=n.concat(s);e.children&&e.children.length>0&&(d(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+a+'".'),x(e.children,t,l,a)),(null!=e.path||e.index)&&t.push({path:a,score:_(a,e.index),routesMeta:l})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of S(e.path))i(e,t,r);else i(e,t)})),t}function S(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,i=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return i?[o,""]:[o];let s=S(r.join("/")),a=[];return a.push(...s.map((e=>""===e?o:[o,e].join("/")))),i&&a.push(...s),a.map((t=>e.startsWith("/")&&""===t?"/":t))}const E=/^:[\w-]+$/,k=3,C=2,P=1,A=10,T=-2,R=e=>"*"===e;function _(e,t){let n=e.split("/"),r=n.length;return n.some(R)&&(r+=T),t&&(r+=C),n.filter((e=>!R(e))).reduce(((e,t)=>e+(E.test(t)?k:""===t?P:A)),r)}function O(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,i={},o="/",s=[];for(let a=0;a<r.length;++a){let e=r[a],l=a===r.length-1,u="/"===o?t:t.slice(o.length)||"/",c=j({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},u),d=e.route;if(!c&&l&&n&&!r[r.length-1].route.index&&(c=j({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(i,c.params),s.push({params:i,pathname:U([o,c.pathname]),pathnameBase:B(U([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=U([o,c.pathnameBase]))}return s}function j(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);f("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),i+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":""!==e&&"/"!==e&&(i+="(?:(?=\\/|$))");let o=new RegExp(i,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let o=i[0],s=o.replace(/(.)\/+$/,"$1"),a=i.slice(1),l=r.reduce(((e,t,n)=>{let{paramName:r,isOptional:i}=t;if("*"===r){let e=a[n]||"";s=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const l=a[n];return e[r]=i&&!l?void 0:(l||"").replace(/%2F/g,"/"),e}),{});return{params:l,pathname:o,pathnameBase:s,pattern:e}}function N(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return f(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function L(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function D(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function F(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function M(e,t){let n=F(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function I(e,t,n,r){let i;void 0===r&&(r=!1),"string"===typeof e?i=y(e):(i=u({},e),d(!i.pathname||!i.pathname.includes("?"),D("?","pathname","search",i)),d(!i.pathname||!i.pathname.includes("#"),D("#","pathname","hash",i)),d(!i.search||!i.search.includes("#"),D("#","search","hash",i)));let o,s=""===e||""===i.pathname,a=s?"/":i.pathname;if(null==a)o=n;else{let e=t.length-1;if(!r&&a.startsWith("..")){let t=a.split("/");for(;".."===t[0];)t.shift(),e-=1;i.pathname=t.join("/")}o=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:i=""}="string"===typeof e?y(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:V(r),hash:z(i)}}(i,o),c=a&&"/"!==a&&a.endsWith("/"),f=(s||"."===a)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!f||(l.pathname+="/"),l}const U=e=>e.join("/").replace(/\/\/+/g,"/"),B=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),V=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",z=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function q(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const Q=["post","put","patch","delete"],H=(new Set(Q),["get",...Q]);new Set(H),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function $(){return $=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$.apply(this,arguments)}const W=i.createContext(null);const K=i.createContext(null);const Y=i.createContext(null);const G=i.createContext(null);const X=i.createContext({outlet:null,matches:[],isDataRoute:!1});const J=i.createContext(null);function Z(){return null!=i.useContext(G)}function ee(){return Z()||d(!1),i.useContext(G).location}function te(e){i.useContext(Y).static||i.useLayoutEffect(e)}function ne(){let{isDataRoute:e}=i.useContext(X);return e?function(){let{router:e}=fe(ce.UseNavigateStable),t=pe(de.UseNavigateStable),n=i.useRef(!1);te((()=>{n.current=!0}));let r=i.useCallback((function(r,i){void 0===i&&(i={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,$({fromRouteId:t},i)))}),[e,t]);return r}():function(){Z()||d(!1);let e=i.useContext(W),{basename:t,future:n,navigator:r}=i.useContext(Y),{matches:o}=i.useContext(X),{pathname:s}=ee(),a=JSON.stringify(M(o,n.v7_relativeSplatPath)),l=i.useRef(!1);return te((()=>{l.current=!0})),i.useCallback((function(n,i){if(void 0===i&&(i={}),!l.current)return;if("number"===typeof n)return void r.go(n);let o=I(n,JSON.parse(a),s,"path"===i.relative);null==e&&"/"!==t&&(o.pathname="/"===o.pathname?t:U([t,o.pathname])),(i.replace?r.replace:r.push)(o,i.state,i)}),[t,r,a,s,e])}()}function re(){let{matches:e}=i.useContext(X),t=e[e.length-1];return t?t.params:{}}function ie(e,t,n,o){Z()||d(!1);let{navigator:s}=i.useContext(Y),{matches:a}=i.useContext(X),l=a[a.length-1],u=l?l.params:{},c=(l&&l.pathname,l?l.pathnameBase:"/");l&&l.route;let f,h=ee();if(t){var p;let e="string"===typeof t?y(t):t;"/"===c||(null==(p=e.pathname)?void 0:p.startsWith(c))||d(!1),f=e}else f=h;let m=f.pathname||"/",g=m;if("/"!==c){let e=c.replace(/^\//,"").split("/");g="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let v=b(e,{pathname:g});let w=ue(v&&v.map((e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:U([c,s.encodeLocation?s.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?c:U([c,s.encodeLocation?s.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),a,n,o);return t&&w?i.createElement(G.Provider,{value:{location:$({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:r.Pop}},w):w}function oe(){let e=function(){var e;let t=i.useContext(J),n=he(de.UseRouteError),r=pe(de.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=q(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r};return i.createElement(i.Fragment,null,i.createElement("h2",null,"Unexpected Application Error!"),i.createElement("h3",{style:{fontStyle:"italic"}},t),n?i.createElement("pre",{style:o},n):null,null)}const se=i.createElement(oe,null);class ae extends i.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?i.createElement(X.Provider,{value:this.props.routeContext},i.createElement(J.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function le(e){let{routeContext:t,match:n,children:r}=e,o=i.useContext(W);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),i.createElement(X.Provider,{value:t},r)}function ue(e,t,n,r){var o;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var s;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(s=r)&&s.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let a=e,l=null==(o=n)?void 0:o.errors;if(null!=l){let e=a.findIndex((e=>e.route.id&&void 0!==(null==l?void 0:l[e.route.id])));e>=0||d(!1),a=a.slice(0,Math.min(a.length,e+1))}let u=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let i=0;i<a.length;i++){let e=a[i];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=i),e.route.id){let{loaderData:t,errors:r}=n,i=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||i){u=!0,a=c>=0?a.slice(0,c+1):[a[0]];break}}}return a.reduceRight(((e,r,o)=>{let s,d=!1,f=null,h=null;var p;n&&(s=l&&r.route.id?l[r.route.id]:void 0,f=r.route.errorElement||se,u&&(c<0&&0===o?(p="route-fallback",!1||me[p]||(me[p]=!0),d=!0,h=null):c===o&&(d=!0,h=r.route.hydrateFallbackElement||null)));let m=t.concat(a.slice(0,o+1)),y=()=>{let t;return t=s?f:d?h:r.route.Component?i.createElement(r.route.Component,null):r.route.element?r.route.element:e,i.createElement(le,{match:r,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===o)?i.createElement(ae,{location:n.location,revalidation:n.revalidation,component:f,error:s,children:y(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):y()}),null)}var ce=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ce||{}),de=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(de||{});function fe(e){let t=i.useContext(W);return t||d(!1),t}function he(e){let t=i.useContext(K);return t||d(!1),t}function pe(e){let t=function(){let e=i.useContext(X);return e||d(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||d(!1),n.route.id}const me={};function ye(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}o.startTransition;function ge(e){d(!1)}function ve(e){let{basename:t="/",children:n=null,location:o,navigationType:s=r.Pop,navigator:a,static:l=!1,future:u}=e;Z()&&d(!1);let c=t.replace(/^\/*/,"/"),f=i.useMemo((()=>({basename:c,navigator:a,static:l,future:$({v7_relativeSplatPath:!1},u)})),[c,u,a,l]);"string"===typeof o&&(o=y(o));let{pathname:h="/",search:p="",hash:m="",state:g=null,key:v="default"}=o,b=i.useMemo((()=>{let e=L(h,c);return null==e?null:{location:{pathname:e,search:p,hash:m,state:g,key:v},navigationType:s}}),[c,h,p,m,g,v,s]);return null==b?null:i.createElement(Y.Provider,{value:f},i.createElement(G.Provider,{children:n,value:b}))}function be(e){let{children:t,location:n}=e;return ie(we(t),n)}new Promise((()=>{}));i.Component;function we(e,t){void 0===t&&(t=[]);let n=[];return i.Children.forEach(e,((e,r)=>{if(!i.isValidElement(e))return;let o=[...t,r];if(e.type===i.Fragment)return void n.push.apply(n,we(e.props.children,o));e.type!==ge&&d(!1),e.props.index&&e.props.children&&d(!1);let s={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(s.children=we(e.props.children,o)),n.push(s)})),n}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(je){}new Map;const xe=o.startTransition;l.flushSync,o.useId;function Se(e){let{basename:t,children:n,future:r,window:o}=e,s=i.useRef();var a;null==s.current&&(s.current=(void 0===(a={window:o,v5Compat:!0})&&(a={}),g((function(e,t){let{pathname:n,search:r,hash:i}=e.location;return p("",{pathname:n,search:r,hash:i},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:m(t)}),null,a)));let l=s.current,[u,c]=i.useState({action:l.action,location:l.location}),{v7_startTransition:d}=r||{},f=i.useCallback((e=>{d&&xe?xe((()=>c(e))):c(e)}),[c,d]);return i.useLayoutEffect((()=>l.listen(f)),[l,f]),i.useEffect((()=>ye(r)),[r]),i.createElement(ve,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:l,future:r})}"undefined"!==typeof window&&"undefined"!==typeof window.document&&window.document.createElement;var Ee,ke;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Ee||(Ee={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(ke||(ke={}));var Ce=n(547);function Pe(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function Ae(e){return Ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ae(e)}function Te(e){var t=function(e,t){if("object"!=Ae(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Ae(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ae(t)?t:t+""}function Re(e,t,n){return(t=Te(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Oe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_e(Object(n),!0).forEach((function(t){Re(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_e(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}let je={data:""},Ne=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||je,Le=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,De=/\/\*[^]*?\*\/|  +/g,Fe=/\n+/g,Me=(e,t)=>{let n="",r="",i="";for(let o in e){let s=e[o];"@"==o[0]?"i"==o[1]?n=o+" "+s+";":r+="f"==o[1]?Me(s,o):o+"{"+Me(s,"k"==o[1]?"":t)+"}":"object"==typeof s?r+=Me(s,t?t.replace(/([^,])+/g,(e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)))):o):null!=s&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=Me.p?Me.p(o,s):o+":"+s+";")}return n+(t&&i?t+"{"+i+"}":i)+r},Ie={},Ue=e=>{if("object"==typeof e){let t="";for(let n in e)t+=n+Ue(e[n]);return t}return e},Be=(e,t,n,r,i)=>{let o=Ue(e),s=Ie[o]||(Ie[o]=(e=>{let t=0,n=11;for(;t<e.length;)n=101*n+e.charCodeAt(t++)>>>0;return"go"+n})(o));if(!Ie[s]){let t=o!==e?e:(e=>{let t,n,r=[{}];for(;t=Le.exec(e.replace(De,""));)t[4]?r.shift():t[3]?(n=t[3].replace(Fe," ").trim(),r.unshift(r[0][n]=r[0][n]||{})):r[0][t[1]]=t[2].replace(Fe," ").trim();return r[0]})(e);Ie[s]=Me(i?{["@keyframes "+s]:t}:t,n?"":"."+s)}let a=n&&Ie.g?Ie.g:null;return n&&(Ie.g=Ie[s]),((e,t,n,r)=>{r?t.data=t.data.replace(r,e):-1===t.data.indexOf(e)&&(t.data=n?e+t.data:t.data+e)})(Ie[s],t,r,a),s};function Ve(e){let t=this||{},n=e.call?e(t.p):e;return Be(n.unshift?n.raw?((e,t,n)=>e.reduce(((e,r,i)=>{let o=t[i];if(o&&o.call){let e=o(n),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=t?"."+t:e&&"object"==typeof e?e.props?"":Me(e,""):!1===e?"":e}return e+r+(null==o?"":o)}),""))(n,[].slice.call(arguments,1),t.p):n.reduce(((e,n)=>Object.assign(e,n&&n.call?n(t.p):n)),{}):n,Ne(t.target),t.g,t.o,t.k)}Ve.bind({g:1});let ze,qe,Qe,He=Ve.bind({k:1});function $e(e,t){let n=this||{};return function(){let r=arguments;function i(o,s){let a=Object.assign({},o),l=a.className||i.className;n.p=Object.assign({theme:qe&&qe()},a),n.o=/ *go\d+/.test(l),a.className=Ve.apply(n,r)+(l?" "+l:""),t&&(a.ref=s);let u=e;return e[0]&&(u=a.as||e,delete a.as),Qe&&u[0]&&Qe(a),ze(u,a)}return t?t(i):i}}var We,Ke,Ye,Ge,Xe,Je,Ze,et,tt,nt,rt,it,ot,st,at,lt,ut=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,ct=(()=>{let e=0;return()=>(++e).toString()})(),dt=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),ft=(e,t)=>{switch(t.type){case 0:return Oe(Oe({},e),{},{toasts:[t.toast,...e.toasts].slice(0,20)});case 1:return Oe(Oe({},e),{},{toasts:e.toasts.map((e=>e.id===t.toast.id?Oe(Oe({},e),t.toast):e))});case 2:let{toast:n}=t;return ft(e,{type:e.toasts.find((e=>e.id===n.id))?1:0,toast:n});case 3:let{toastId:r}=t;return Oe(Oe({},e),{},{toasts:e.toasts.map((e=>e.id===r||void 0===r?Oe(Oe({},e),{},{dismissed:!0,visible:!1}):e))});case 4:return void 0===t.toastId?Oe(Oe({},e),{},{toasts:[]}):Oe(Oe({},e),{},{toasts:e.toasts.filter((e=>e.id!==t.toastId))});case 5:return Oe(Oe({},e),{},{pausedAt:t.time});case 6:let i=t.time-(e.pausedAt||0);return Oe(Oe({},e),{},{pausedAt:void 0,toasts:e.toasts.map((e=>Oe(Oe({},e),{},{pauseDuration:e.pauseDuration+i})))})}},ht=[],pt={toasts:[],pausedAt:void 0},mt=e=>{pt=ft(pt,e),ht.forEach((e=>{e(pt)}))},yt={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},gt=e=>(t,n)=>{let r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"blank",n=arguments.length>2?arguments[2]:void 0;return Oe(Oe({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0},n),{},{id:(null==n?void 0:n.id)||ct()})}(t,e,n);return mt({type:2,toast:r}),r.id},vt=(e,t)=>gt("blank")(e,t);vt.error=gt("error"),vt.success=gt("success"),vt.loading=gt("loading"),vt.custom=gt("custom"),vt.dismiss=e=>{mt({type:3,toastId:e})},vt.remove=e=>mt({type:4,toastId:e}),vt.promise=(e,t,n)=>{let r=vt.loading(t.loading,Oe(Oe({},n),null==n?void 0:n.loading));return"function"==typeof e&&(e=e()),e.then((e=>{let i=t.success?ut(t.success,e):void 0;return i?vt.success(i,Oe(Oe({id:r},n),null==n?void 0:n.success)):vt.dismiss(r),e})).catch((e=>{let i=t.error?ut(t.error,e):void 0;i?vt.error(i,Oe(Oe({id:r},n),null==n?void 0:n.error)):vt.dismiss(r)})),e};var bt=(e,t)=>{mt({type:1,toast:{id:e,height:t}})},wt=()=>{mt({type:5,time:Date.now()})},xt=new Map,St=e=>{let{toasts:t,pausedAt:n}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[t,n]=(0,i.useState)(pt),r=(0,i.useRef)(pt);(0,i.useEffect)((()=>(r.current!==pt&&n(pt),ht.push(n),()=>{let e=ht.indexOf(n);e>-1&&ht.splice(e,1)})),[]);let o=t.toasts.map((t=>{var n,r,i;return Oe(Oe(Oe(Oe({},e),e[t.type]),t),{},{removeDelay:t.removeDelay||(null==(n=e[t.type])?void 0:n.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(r=e[t.type])?void 0:r.duration)||(null==e?void 0:e.duration)||yt[t.type],style:Oe(Oe(Oe({},e.style),null==(i=e[t.type])?void 0:i.style),t.style)})}));return Oe(Oe({},t),{},{toasts:o})}(e);(0,i.useEffect)((()=>{if(n)return;let e=Date.now(),r=t.map((t=>{if(t.duration===1/0)return;let n=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(n<0))return setTimeout((()=>vt.dismiss(t.id)),n);t.visible&&vt.dismiss(t.id)}));return()=>{r.forEach((e=>e&&clearTimeout(e)))}}),[t,n]);let r=(0,i.useCallback)((()=>{n&&mt({type:6,time:Date.now()})}),[n]),o=(0,i.useCallback)(((e,n)=>{let{reverseOrder:r=!1,gutter:i=8,defaultPosition:o}=n||{},s=t.filter((t=>(t.position||o)===(e.position||o)&&t.height)),a=s.findIndex((t=>t.id===e.id)),l=s.filter(((e,t)=>t<a&&e.visible)).length;return s.filter((e=>e.visible)).slice(...r?[l+1]:[0,l]).reduce(((e,t)=>e+(t.height||0)+i),0)}),[t]);return(0,i.useEffect)((()=>{t.forEach((e=>{if(e.dismissed)!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;if(xt.has(e))return;let n=setTimeout((()=>{xt.delete(e),mt({type:4,toastId:e})}),t);xt.set(e,n)}(e.id,e.removeDelay);else{let t=xt.get(e.id);t&&(clearTimeout(t),xt.delete(e.id))}}))}),[t]),{toasts:t,handlers:{updateHeight:bt,startPause:wt,endPause:r,calculateOffset:o}}},Et=He(We||(We=Pe(["\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}"]))),kt=He(Ke||(Ke=Pe(["\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}"]))),Ct=He(Ye||(Ye=Pe(["\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}"]))),Pt=$e("div")(Ge||(Ge=Pe(["\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ",";\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: "," 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: "," 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ",";\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: "," 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n"])),(e=>e.primary||"#ff4b4b"),Et,kt,(e=>e.secondary||"#fff"),Ct),At=He(Xe||(Xe=Pe(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"]))),Tt=$e("div")(Je||(Je=Pe(["\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ",";\n  border-right-color: ",";\n  animation: "," 1s linear infinite;\n"])),(e=>e.secondary||"#e0e0e0"),(e=>e.primary||"#616161"),At),Rt=He(Ze||(Ze=Pe(["\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}"]))),_t=He(et||(et=Pe(["\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}"]))),Ot=$e("div")(tt||(tt=Pe(["\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ",";\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: "," 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: "," 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ",";\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n"])),(e=>e.primary||"#61d345"),Rt,_t,(e=>e.secondary||"#fff")),jt=$e("div")(nt||(nt=Pe(["\n  position: absolute;\n"]))),Nt=$e("div")(rt||(rt=Pe(["\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n"]))),Lt=He(it||(it=Pe(["\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}"]))),Dt=$e("div")(ot||(ot=Pe(["\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: "," 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n"])),Lt),Ft=e=>{let{toast:t}=e,{icon:n,type:r,iconTheme:o}=t;return void 0!==n?"string"==typeof n?i.createElement(Dt,null,n):n:"blank"===r?null:i.createElement(Nt,null,i.createElement(Tt,Oe({},o)),"loading"!==r&&i.createElement(jt,null,"error"===r?i.createElement(Pt,Oe({},o)):i.createElement(Ot,Oe({},o))))},Mt=e=>"\n0% {transform: translate3d(0,".concat(-200*e,"%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n"),It=e=>"\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,".concat(-150*e,"%,-1px) scale(.6); opacity:0;}\n"),Ut=$e("div")(st||(st=Pe(["\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n"]))),Bt=$e("div")(at||(at=Pe(["\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n"]))),Vt=i.memo((e=>{let{toast:t,position:n,style:r,children:o}=e,s=t.height?((e,t)=>{let n=e.includes("top")?1:-1,[r,i]=dt()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[Mt(n),It(n)];return{animation:t?"".concat(He(r)," 0.35s cubic-bezier(.21,1.02,.73,1) forwards"):"".concat(He(i)," 0.4s forwards cubic-bezier(.06,.71,.55,1)")}})(t.position||n||"top-center",t.visible):{opacity:0},a=i.createElement(Ft,{toast:t}),l=i.createElement(Bt,Oe({},t.ariaProps),ut(t.message,t));return i.createElement(Ut,{className:t.className,style:Oe(Oe(Oe({},s),r),t.style)},"function"==typeof o?o({icon:a,message:l}):i.createElement(i.Fragment,null,a,l))}));!function(e,t,n,r){Me.p=t,ze=e,qe=n,Qe=r}(i.createElement);var zt=e=>{let{id:t,className:n,style:r,onHeightUpdate:o,children:s}=e,a=i.useCallback((e=>{if(e){let n=()=>{let n=e.getBoundingClientRect().height;o(t,n)};n(),new MutationObserver(n).observe(e,{subtree:!0,childList:!0,characterData:!0})}}),[t,o]);return i.createElement("div",{ref:a,className:n,style:r},s)},qt=Ve(lt||(lt=Pe(["\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n"]))),Qt=e=>{let{reverseOrder:t,position:n="top-center",toastOptions:r,gutter:o,children:s,containerStyle:a,containerClassName:l}=e,{toasts:u,handlers:c}=St(r);return i.createElement("div",{id:"_rht_toaster",style:Oe({position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none"},a),className:l,onMouseEnter:c.startPause,onMouseLeave:c.endPause},u.map((e=>{let r=e.position||n,a=((e,t)=>{let n=e.includes("top"),r=n?{top:0}:{bottom:0},i=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return Oe(Oe({left:0,right:0,display:"flex",position:"absolute",transition:dt()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:"translateY(".concat(t*(n?1:-1),"px)")},r),i)})(r,c.calculateOffset(e,{reverseOrder:t,gutter:o,defaultPosition:n}));return i.createElement(zt,{id:e.id,key:e.id,onHeightUpdate:c.updateHeight,className:e.visible?qt:"",style:a},"custom"===e.type?ut(e.message,e):s?s(e):i.createElement(Vt,{toast:e,position:r}))})))},Ht=vt;function $t(e,t){return function(){return e.apply(t,arguments)}}const{toString:Wt}=Object.prototype,{getPrototypeOf:Kt}=Object,{iterator:Yt,toStringTag:Gt}=Symbol,Xt=(e=>t=>{const n=Wt.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Jt=e=>(e=e.toLowerCase(),t=>Xt(t)===e),Zt=e=>t=>typeof t===e,{isArray:en}=Array,tn=Zt("undefined");const nn=Jt("ArrayBuffer");const rn=Zt("string"),on=Zt("function"),sn=Zt("number"),an=e=>null!==e&&"object"===typeof e,ln=e=>{if("object"!==Xt(e))return!1;const t=Kt(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Gt in e)&&!(Yt in e)},un=Jt("Date"),cn=Jt("File"),dn=Jt("Blob"),fn=Jt("FileList"),hn=Jt("URLSearchParams"),[pn,mn,yn,gn]=["ReadableStream","Request","Response","Headers"].map(Jt);function vn(e,t){let n,r,{allOwnKeys:i=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),en(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=i?Object.getOwnPropertyNames(e):Object.keys(e),o=r.length;let s;for(n=0;n<o;n++)s=r[n],t.call(null,e[s],s,e)}}function bn(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,i=n.length;for(;i-- >0;)if(r=n[i],t===r.toLowerCase())return r;return null}const wn="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,xn=e=>!tn(e)&&e!==wn;const Sn=(En="undefined"!==typeof Uint8Array&&Kt(Uint8Array),e=>En&&e instanceof En);var En;const kn=Jt("HTMLFormElement"),Cn=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),Pn=Jt("RegExp"),An=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};vn(n,((n,i)=>{let o;!1!==(o=t(n,i,e))&&(r[i]=o||n)})),Object.defineProperties(e,r)};const Tn=Jt("AsyncFunction"),Rn=(_n="function"===typeof setImmediate,On=on(wn.postMessage),_n?setImmediate:On?((e,t)=>(wn.addEventListener("message",(n=>{let{source:r,data:i}=n;r===wn&&i===e&&t.length&&t.shift()()}),!1),n=>{t.push(n),wn.postMessage(e,"*")}))("axios@".concat(Math.random()),[]):e=>setTimeout(e));var _n,On;const jn="undefined"!==typeof queueMicrotask?queueMicrotask.bind(wn):"undefined"!==typeof process&&process.nextTick||Rn,Nn={isArray:en,isArrayBuffer:nn,isBuffer:function(e){return null!==e&&!tn(e)&&null!==e.constructor&&!tn(e.constructor)&&on(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||on(e.append)&&("formdata"===(t=Xt(e))||"object"===t&&on(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&nn(e.buffer),t},isString:rn,isNumber:sn,isBoolean:e=>!0===e||!1===e,isObject:an,isPlainObject:ln,isReadableStream:pn,isRequest:mn,isResponse:yn,isHeaders:gn,isUndefined:tn,isDate:un,isFile:cn,isBlob:dn,isRegExp:Pn,isFunction:on,isStream:e=>an(e)&&on(e.pipe),isURLSearchParams:hn,isTypedArray:Sn,isFileList:fn,forEach:vn,merge:function e(){const{caseless:t}=xn(this)&&this||{},n={},r=(r,i)=>{const o=t&&bn(n,i)||i;ln(n[o])&&ln(r)?n[o]=e(n[o],r):ln(r)?n[o]=e({},r):en(r)?n[o]=r.slice():n[o]=r};for(let i=0,o=arguments.length;i<o;i++)arguments[i]&&vn(arguments[i],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return vn(t,((t,r)=>{n&&on(t)?e[r]=$t(t,n):e[r]=t}),{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let i,o,s;const a={};if(t=t||{},null==e)return t;do{for(i=Object.getOwnPropertyNames(e),o=i.length;o-- >0;)s=i[o],r&&!r(s,e,t)||a[s]||(t[s]=e[s],a[s]=!0);e=!1!==n&&Kt(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Xt,kindOfTest:Jt,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(en(e))return e;let t=e.length;if(!sn(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Yt]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:kn,hasOwnProperty:Cn,hasOwnProp:Cn,reduceDescriptors:An,freezeMethods:e=>{An(e,((t,n)=>{if(on(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];on(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return en(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:bn,global:wn,isContextDefined:xn,isSpecCompliantForm:function(e){return!!(e&&on(e.append)&&"FormData"===e[Gt]&&e[Yt])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(an(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const i=en(e)?[]:{};return vn(e,((e,t)=>{const o=n(e,r+1);!tn(o)&&(i[t]=o)})),t[r]=void 0,i}}return e};return n(e,0)},isAsyncFn:Tn,isThenable:e=>e&&(an(e)||on(e))&&on(e.then)&&on(e.catch),setImmediate:Rn,asap:jn,isIterable:e=>null!=e&&on(e[Yt])};function Ln(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}Nn.inherits(Ln,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Nn.toJSONObject(this.config),code:this.code,status:this.status}}});const Dn=Ln.prototype,Fn={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{Fn[e]={value:e}})),Object.defineProperties(Ln,Fn),Object.defineProperty(Dn,"isAxiosError",{value:!0}),Ln.from=(e,t,n,r,i,o)=>{const s=Object.create(Dn);return Nn.toFlatObject(e,s,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),Ln.call(s,e.message,t,n,r,i),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};const Mn=Ln;function In(e){return Nn.isPlainObject(e)||Nn.isArray(e)}function Un(e){return Nn.endsWith(e,"[]")?e.slice(0,-2):e}function Bn(e,t,n){return e?e.concat(t).map((function(e,t){return e=Un(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const Vn=Nn.toFlatObject(Nn,{},null,(function(e){return/^is[A-Z]/.test(e)}));const zn=function(e,t,n){if(!Nn.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Nn.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!Nn.isUndefined(t[e])}))).metaTokens,i=n.visitor||u,o=n.dots,s=n.indexes,a=(n.Blob||"undefined"!==typeof Blob&&Blob)&&Nn.isSpecCompliantForm(t);if(!Nn.isFunction(i))throw new TypeError("visitor must be a function");function l(e){if(null===e)return"";if(Nn.isDate(e))return e.toISOString();if(!a&&Nn.isBlob(e))throw new Mn("Blob is not supported. Use a Buffer instead.");return Nn.isArrayBuffer(e)||Nn.isTypedArray(e)?a&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,i){let a=e;if(e&&!i&&"object"===typeof e)if(Nn.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Nn.isArray(e)&&function(e){return Nn.isArray(e)&&!e.some(In)}(e)||(Nn.isFileList(e)||Nn.endsWith(n,"[]"))&&(a=Nn.toArray(e)))return n=Un(n),a.forEach((function(e,r){!Nn.isUndefined(e)&&null!==e&&t.append(!0===s?Bn([n],r,o):null===s?n:n+"[]",l(e))})),!1;return!!In(e)||(t.append(Bn(i,n,o),l(e)),!1)}const c=[],d=Object.assign(Vn,{defaultVisitor:u,convertValue:l,isVisitable:In});if(!Nn.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Nn.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),Nn.forEach(n,(function(n,o){!0===(!(Nn.isUndefined(n)||null===n)&&i.call(t,n,Nn.isString(o)?o.trim():o,r,d))&&e(n,r?r.concat(o):[o])})),c.pop()}}(e),t};function qn(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function Qn(e,t){this._pairs=[],e&&zn(e,this,t)}const Hn=Qn.prototype;Hn.append=function(e,t){this._pairs.push([e,t])},Hn.toString=function(e){const t=e?function(t){return e.call(this,t,qn)}:qn;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const $n=Qn;function Wn(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Kn(e,t,n){if(!t)return e;const r=n&&n.encode||Wn;Nn.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let o;if(o=i?i(t,n):Nn.isURLSearchParams(t)?t.toString():new $n(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const Yn=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Nn.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},Gn={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Xn={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:$n,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Jn="undefined"!==typeof window&&"undefined"!==typeof document,Zn="object"===typeof navigator&&navigator||void 0,er=Jn&&(!Zn||["ReactNative","NativeScript","NS"].indexOf(Zn.product)<0),tr="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,nr=Jn&&window.location.href||"http://localhost",rr=Oe(Oe({},e),Xn);const ir=function(e){function t(e,n,r,i){let o=e[i++];if("__proto__"===o)return!0;const s=Number.isFinite(+o),a=i>=e.length;if(o=!o&&Nn.isArray(r)?r.length:o,a)return Nn.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!s;r[o]&&Nn.isObject(r[o])||(r[o]=[]);return t(e,n,r[o],i)&&Nn.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const i=n.length;let o;for(r=0;r<i;r++)o=n[r],t[o]=e[o];return t}(r[o])),!s}if(Nn.isFormData(e)&&Nn.isFunction(e.entries)){const n={};return Nn.forEachEntry(e,((e,r)=>{t(function(e){return Nn.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null};const or={transitional:Gn,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,i=Nn.isObject(e);i&&Nn.isHTMLForm(e)&&(e=new FormData(e));if(Nn.isFormData(e))return r?JSON.stringify(ir(e)):e;if(Nn.isArrayBuffer(e)||Nn.isBuffer(e)||Nn.isStream(e)||Nn.isFile(e)||Nn.isBlob(e)||Nn.isReadableStream(e))return e;if(Nn.isArrayBufferView(e))return e.buffer;if(Nn.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return zn(e,new rr.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return rr.isNode&&Nn.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=Nn.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return zn(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return i||r?(t.setContentType("application/json",!1),function(e,t,n){if(Nn.isString(e))try{return(t||JSON.parse)(e),Nn.trim(e)}catch(je){if("SyntaxError"!==je.name)throw je}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||or.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Nn.isResponse(e)||Nn.isReadableStream(e))return e;if(e&&Nn.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(je){if(n){if("SyntaxError"===je.name)throw Mn.from(je,Mn.ERR_BAD_RESPONSE,this,null,this.response);throw je}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:rr.classes.FormData,Blob:rr.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Nn.forEach(["delete","get","head","post","put","patch"],(e=>{or.headers[e]={}}));const sr=or,ar=Nn.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),lr=Symbol("internals");function ur(e){return e&&String(e).trim().toLowerCase()}function cr(e){return!1===e||null==e?e:Nn.isArray(e)?e.map(cr):String(e)}function dr(e,t,n,r,i){return Nn.isFunction(r)?r.call(this,t,n):(i&&(t=n),Nn.isString(t)?Nn.isString(r)?-1!==t.indexOf(r):Nn.isRegExp(r)?r.test(t):void 0:void 0)}class fr{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function i(e,t,n){const i=ur(t);if(!i)throw new Error("header name must be a non-empty string");const o=Nn.findKey(r,i);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=cr(e))}const o=(e,t)=>Nn.forEach(e,((e,n)=>i(e,n,t)));if(Nn.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(Nn.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let n,r,i;return e&&e.split("\n").forEach((function(e){i=e.indexOf(":"),n=e.substring(0,i).trim().toLowerCase(),r=e.substring(i+1).trim(),!n||t[n]&&ar[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(Nn.isObject(e)&&Nn.isIterable(e)){let n,r,i={};for(const t of e){if(!Nn.isArray(t))throw TypeError("Object iterator must return a key-value pair");i[r=t[0]]=(n=i[r])?Nn.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(i,t)}else null!=e&&i(t,e,n);return this}get(e,t){if(e=ur(e)){const n=Nn.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Nn.isFunction(t))return t.call(this,e,n);if(Nn.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=ur(e)){const n=Nn.findKey(this,e);return!(!n||void 0===this[n]||t&&!dr(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function i(e){if(e=ur(e)){const i=Nn.findKey(n,e);!i||t&&!dr(0,n[i],i,t)||(delete n[i],r=!0)}}return Nn.isArray(e)?e.forEach(i):i(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const i=t[n];e&&!dr(0,this[i],i,e,!0)||(delete this[i],r=!0)}return r}normalize(e){const t=this,n={};return Nn.forEach(this,((r,i)=>{const o=Nn.findKey(n,i);if(o)return t[o]=cr(r),void delete t[i];const s=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(i):String(i).trim();s!==i&&delete t[i],t[s]=cr(r),n[s]=!0})),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return Nn.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Nn.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((e=>{let[t,n]=e;return t+": "+n})).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return r.forEach((e=>t.set(e))),t}static accessor(e){const t=(this[lr]=this[lr]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=ur(e);t[r]||(!function(e,t){const n=Nn.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,i){return this[r].call(this,t,e,n,i)},configurable:!0})}))}(n,e),t[r]=!0)}return Nn.isArray(e)?e.forEach(r):r(e),this}}fr.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Nn.reduceDescriptors(fr.prototype,((e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}})),Nn.freezeMethods(fr);const hr=fr;function pr(e,t){const n=this||sr,r=t||n,i=hr.from(r.headers);let o=r.data;return Nn.forEach(e,(function(e){o=e.call(n,o,i.normalize(),t?t.status:void 0)})),i.normalize(),o}function mr(e){return!(!e||!e.__CANCEL__)}function yr(e,t,n){Mn.call(this,null==e?"canceled":e,Mn.ERR_CANCELED,t,n),this.name="CanceledError"}Nn.inherits(yr,Mn,{__CANCEL__:!0});const gr=yr;function vr(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Mn("Request failed with status code "+n.status,[Mn.ERR_BAD_REQUEST,Mn.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const br=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i,o=0,s=0;return t=void 0!==t?t:1e3,function(a){const l=Date.now(),u=r[s];i||(i=l),n[o]=a,r[o]=l;let c=s,d=0;for(;c!==o;)d+=n[c++],c%=e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),l-i<t)return;const f=u&&l-u;return f?Math.round(1e3*d/f):void 0}};const wr=function(e,t){let n,r,i=0,o=1e3/t;const s=function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();i=o,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-i;for(var a=arguments.length,l=new Array(a),u=0;u<a;u++)l[u]=arguments[u];t>=o?s(l,e):(n=l,r||(r=setTimeout((()=>{r=null,s(n)}),o-t)))},()=>n&&s(n)]},xr=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const i=br(50,250);return wr((n=>{const o=n.loaded,s=n.lengthComputable?n.total:void 0,a=o-r,l=i(a);r=o;e({loaded:o,total:s,progress:s?o/s:void 0,bytes:a,rate:l||void 0,estimated:l&&s&&o<=s?(s-o)/l:void 0,event:n,lengthComputable:null!=s,[t?"download":"upload"]:!0})}),n)},Sr=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Er=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Nn.asap((()=>e(...n)))},kr=rr.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,rr.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(rr.origin),rr.navigator&&/(msie|trident)/i.test(rr.navigator.userAgent)):()=>!0,Cr=rr.hasStandardBrowserEnv?{write(e,t,n,r,i,o){const s=[e+"="+encodeURIComponent(t)];Nn.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),Nn.isString(r)&&s.push("path="+r),Nn.isString(i)&&s.push("domain="+i),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Pr(e,t,n){let r=!function(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Ar=e=>e instanceof hr?Oe({},e):e;function Tr(e,t){t=t||{};const n={};function r(e,t,n,r){return Nn.isPlainObject(e)&&Nn.isPlainObject(t)?Nn.merge.call({caseless:r},e,t):Nn.isPlainObject(t)?Nn.merge({},t):Nn.isArray(t)?t.slice():t}function i(e,t,n,i){return Nn.isUndefined(t)?Nn.isUndefined(e)?void 0:r(void 0,e,0,i):r(e,t,0,i)}function o(e,t){if(!Nn.isUndefined(t))return r(void 0,t)}function s(e,t){return Nn.isUndefined(t)?Nn.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function a(n,i,o){return o in t?r(n,i):o in e?r(void 0,n):void 0}const l={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(e,t,n)=>i(Ar(e),Ar(t),0,!0)};return Nn.forEach(Object.keys(Object.assign({},e,t)),(function(r){const o=l[r]||i,s=o(e[r],t[r],r);Nn.isUndefined(s)&&o!==a||(n[r]=s)})),n}const Rr=e=>{const t=Tr({},e);let n,{data:r,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:s,headers:a,auth:l}=t;if(t.headers=a=hr.from(a),t.url=Kn(Pr(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&a.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),Nn.isFormData(r))if(rr.hasStandardBrowserEnv||rr.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...t].join("; "))}if(rr.hasStandardBrowserEnv&&(i&&Nn.isFunction(i)&&(i=i(t)),i||!1!==i&&kr(t.url))){const e=o&&s&&Cr.read(s);e&&a.set(o,e)}return t},_r="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=Rr(e);let i=r.data;const o=hr.from(r.headers).normalize();let s,a,l,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:h}=r;function p(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(s),r.signal&&r.signal.removeEventListener("abort",s)}let m=new XMLHttpRequest;function y(){if(!m)return;const r=hr.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());vr((function(e){t(e),p()}),(function(e){n(e),p()}),{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=y:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(y)},m.onabort=function(){m&&(n(new Mn("Request aborted",Mn.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new Mn("Network Error",Mn.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const i=r.transitional||Gn;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Mn(t,i.clarifyTimeoutError?Mn.ETIMEDOUT:Mn.ECONNABORTED,e,m)),m=null},void 0===i&&o.setContentType(null),"setRequestHeader"in m&&Nn.forEach(o.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),Nn.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),h&&([l,c]=xr(h,!0),m.addEventListener("progress",l)),f&&m.upload&&([a,u]=xr(f),m.upload.addEventListener("progress",a),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(s=t=>{m&&(n(!t||t.type?new gr(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(s),r.signal&&(r.signal.aborted?s():r.signal.addEventListener("abort",s)));const g=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);g&&-1===rr.protocols.indexOf(g)?n(new Mn("Unsupported protocol "+g+":",Mn.ERR_BAD_REQUEST,e)):m.send(i||null)}))},Or=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const i=function(e){if(!n){n=!0,s();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Mn?t:new gr(t instanceof Error?t.message:t))}};let o=t&&setTimeout((()=>{o=null,i(new Mn("timeout ".concat(t," of ms exceeded"),Mn.ETIMEDOUT))}),t);const s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)})),e=null)};e.forEach((e=>e.addEventListener("abort",i)));const{signal:a}=r;return a.unsubscribe=()=>Nn.asap(s),a}};function jr(e,t){this.v=e,this.k=t}function Nr(e){return function(){return new Lr(e.apply(this,arguments))}}function Lr(e){var t,n;function r(t,n){try{var o=e[t](n),s=o.value,a=s instanceof jr;Promise.resolve(a?s.v:s).then((function(n){if(a){var l="return"===t?"return":"next";if(!s.k||n.done)return r(l,n);n=e[l](n).value}i(o.done?"return":"normal",n)}),(function(e){r("throw",e)}))}catch(e){i("throw",e)}}function i(e,i){switch(e){case"return":t.resolve({value:i,done:!0});break;case"throw":t.reject(i);break;default:t.resolve({value:i,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,i){return new Promise((function(o,s){var a={key:e,arg:i,resolve:o,reject:s,next:null};n?n=n.next=a:(t=n=a,r(e,i))}))},"function"!=typeof e.return&&(this.return=void 0)}function Dr(e){return new jr(e,0)}function Fr(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise((function(n){n(e[t](r))})),{done:!1,value:new jr(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function Mr(e){var t,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new Ir(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Ir(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then((function(e){return{value:e,done:t}}))}return Ir=function(e){this.s=e,this.n=e.next},Ir.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new Ir(e)}Lr.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},Lr.prototype.next=function(e){return this._invoke("next",e)},Lr.prototype.throw=function(e){return this._invoke("throw",e)},Lr.prototype.return=function(e){return this._invoke("return",e)};const Ur=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,i=0;for(;i<n;)r=i+t,yield e.slice(i,r),i=r},Br=function(){var e=Nr((function*(e,t){var n,r=!1,i=!1;try{for(var o,s=Mr(Vr(e));r=!(o=yield Dr(s.next())).done;r=!1){const e=o.value;yield*Fr(Mr(Ur(e,t)))}}catch(a){i=!0,n=a}finally{try{r&&null!=s.return&&(yield Dr(s.return()))}finally{if(i)throw n}}}));return function(t,n){return e.apply(this,arguments)}}(),Vr=function(){var e=Nr((function*(e){if(e[Symbol.asyncIterator])return void(yield*Fr(Mr(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield Dr(t.read());if(e)break;yield n}}finally{yield Dr(t.cancel())}}));return function(t){return e.apply(this,arguments)}}(),zr=(e,t,n,r)=>{const i=Br(e,t);let o,s=0,a=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await i.next();if(t)return a(),void e.close();let o=r.byteLength;if(n){let e=s+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw a(t),t}},cancel:e=>(a(e),i.return())},{highWaterMark:2})},qr="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,Qr=qr&&"function"===typeof ReadableStream,Hr=qr&&("function"===typeof TextEncoder?($r=new TextEncoder,e=>$r.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var $r;const Wr=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(je){return!1}},Kr=Qr&&Wr((()=>{let e=!1;const t=new Request(rr.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),Yr=Qr&&Wr((()=>Nn.isReadableStream(new Response("").body))),Gr={stream:Yr&&(e=>e.body)};var Xr;qr&&(Xr=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!Gr[e]&&(Gr[e]=Nn.isFunction(Xr[e])?t=>t[e]():(t,n)=>{throw new Mn("Response type '".concat(e,"' is not supported"),Mn.ERR_NOT_SUPPORT,n)})})));const Jr=async(e,t)=>{const n=Nn.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(Nn.isBlob(e))return e.size;if(Nn.isSpecCompliantForm(e)){const t=new Request(rr.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Nn.isArrayBufferView(e)||Nn.isArrayBuffer(e)?e.byteLength:(Nn.isURLSearchParams(e)&&(e+=""),Nn.isString(e)?(await Hr(e)).byteLength:void 0)})(t):n},Zr=qr&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:o,timeout:s,onDownloadProgress:a,onUploadProgress:l,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=Rr(e);u=u?(u+"").toLowerCase():"text";let h,p=Or([i,o&&o.toAbortSignal()],s);const m=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let y;try{if(l&&Kr&&"get"!==n&&"head"!==n&&0!==(y=await Jr(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(Nn.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=Sr(y,xr(Er(l)));r=zr(n.body,65536,e,t)}}Nn.isString(d)||(d=d?"include":"omit");const i="credentials"in Request.prototype;h=new Request(t,Oe(Oe({},f),{},{signal:p,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:i?d:void 0}));let o=await fetch(h);const s=Yr&&("stream"===u||"response"===u);if(Yr&&(a||s&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=o[t]}));const t=Nn.toFiniteNumber(o.headers.get("content-length")),[n,r]=a&&Sr(t,xr(Er(a),!0))||[];o=new Response(zr(o.body,65536,n,(()=>{r&&r(),m&&m()})),e)}u=u||"text";let g=await Gr[Nn.findKey(Gr,u)||"text"](o,e);return!s&&m&&m(),await new Promise(((t,n)=>{vr(t,n,{data:g,headers:hr.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:h})}))}catch(g){if(m&&m(),g&&"TypeError"===g.name&&/Load failed|fetch/i.test(g.message))throw Object.assign(new Mn("Network Error",Mn.ERR_NETWORK,e,h),{cause:g.cause||g});throw Mn.from(g,g&&g.code,e,h)}}),ei={http:null,xhr:_r,fetch:Zr};Nn.forEach(ei,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(je){}Object.defineProperty(e,"adapterName",{value:t})}}));const ti=e=>"- ".concat(e),ni=e=>Nn.isFunction(e)||null===e||!1===e,ri=e=>{e=Nn.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!ni(n)&&(r=ei[(t=String(n)).toLowerCase()],void 0===r))throw new Mn("Unknown adapter '".concat(t,"'"));if(r)break;i[t||"#"+o]=r}if(!r){const e=Object.entries(i).map((e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")}));let n=t?e.length>1?"since :\n"+e.map(ti).join("\n"):" "+ti(e[0]):"as no adapter specified";throw new Mn("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function ii(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new gr(null,e)}function oi(e){ii(e),e.headers=hr.from(e.headers),e.data=pr.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return ri(e.adapter||sr.adapter)(e).then((function(t){return ii(e),t.data=pr.call(e,e.transformResponse,t),t.headers=hr.from(t.headers),t}),(function(t){return mr(t)||(ii(e),t&&t.response&&(t.response.data=pr.call(e,e.transformResponse,t.response),t.response.headers=hr.from(t.response.headers))),Promise.reject(t)}))}const si="1.9.0",ai={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{ai[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const li={};ai.transitional=function(e,t,n){function r(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,i,o)=>{if(!1===e)throw new Mn(r(i," has been removed"+(t?" in "+t:"")),Mn.ERR_DEPRECATED);return t&&!li[i]&&(li[i]=!0,console.warn(r(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,i,o)}},ai.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const ui={assertOptions:function(e,t,n){if("object"!==typeof e)throw new Mn("options must be an object",Mn.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const o=r[i],s=t[o];if(s){const t=e[o],n=void 0===t||s(t,o,e);if(!0!==n)throw new Mn("option "+o+" must be "+n,Mn.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new Mn("Unknown option "+o,Mn.ERR_BAD_OPTION)}},validators:ai},ci=ui.validators;class di{constructor(e){this.defaults=e||{},this.interceptors={request:new Yn,response:new Yn}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(je){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=Tr(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:i}=t;void 0!==n&&ui.assertOptions(n,{silentJSONParsing:ci.transitional(ci.boolean),forcedJSONParsing:ci.transitional(ci.boolean),clarifyTimeoutError:ci.transitional(ci.boolean)},!1),null!=r&&(Nn.isFunction(r)?t.paramsSerializer={serialize:r}:ui.assertOptions(r,{encode:ci.function,serialize:ci.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),ui.assertOptions(t,{baseUrl:ci.spelling("baseURL"),withXsrfToken:ci.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=i&&Nn.merge(i.common,i[t.method]);i&&Nn.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete i[e]})),t.headers=hr.concat(o,i);const s=[];let a=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,s.unshift(e.fulfilled,e.rejected))}));const l=[];let u;this.interceptors.response.forEach((function(e){l.push(e.fulfilled,e.rejected)}));let c,d=0;if(!a){const e=[oi.bind(this),void 0];for(e.unshift.apply(e,s),e.push.apply(e,l),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=s.length;let f=t;for(d=0;d<c;){const e=s[d++],t=s[d++];try{f=e(f)}catch(h){t.call(this,h);break}}try{u=oi.call(this,f)}catch(h){return Promise.reject(h)}for(d=0,c=l.length;d<c;)u=u.then(l[d++],l[d++]);return u}getUri(e){return Kn(Pr((e=Tr(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Nn.forEach(["delete","get","head","options"],(function(e){di.prototype[e]=function(t,n){return this.request(Tr(n||{},{method:e,url:t,data:(n||{}).data}))}})),Nn.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,i){return this.request(Tr(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}di.prototype[e]=t(),di.prototype[e+"Form"]=t(!0)}));const fi=di;class hi{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,i){n.reason||(n.reason=new gr(e,r,i),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;const t=new hi((function(t){e=t}));return{token:t,cancel:e}}}const pi=hi;const mi={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(mi).forEach((e=>{let[t,n]=e;mi[n]=t}));const yi=mi;const gi=function e(t){const n=new fi(t),r=$t(fi.prototype.request,n);return Nn.extend(r,fi.prototype,n,{allOwnKeys:!0}),Nn.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Tr(t,n))},r}(sr);gi.Axios=fi,gi.CanceledError=gr,gi.CancelToken=pi,gi.isCancel=mr,gi.VERSION=si,gi.toFormData=zn,gi.AxiosError=Mn,gi.Cancel=gi.CanceledError,gi.all=function(e){return Promise.all(e)},gi.spread=function(e){return function(t){return e.apply(null,t)}},gi.isAxiosError=function(e){return Nn.isObject(e)&&!0===e.isAxiosError},gi.mergeConfig=Tr,gi.AxiosHeaders=hr,gi.formToJSON=e=>ir(Nn.isHTMLForm(e)?new FormData(e):e),gi.getAdapter=ri,gi.HttpStatusCode=yi,gi.default=gi;const vi=gi,bi=vi.create({baseURL:{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:5000/api",timeout:3e4,headers:{"Content-Type":"application/json"}});bi.interceptors.request.use((e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e.metadata={startTime:new Date},e}),(e=>Promise.reject(e))),bi.interceptors.response.use((e=>{const t=new Date-e.config.metadata.startTime;return e.duration=t,e}),(async e=>{var t,n;const r=e.config;return 401!==(null===(t=e.response)||void 0===t?void 0:t.status)||r._retry?(403===(null===(n=e.response)||void 0===n?void 0:n.status)&&window.dispatchEvent(new CustomEvent("auth:forbidden")),e.response||(e.message="Network error. Please check your connection."),"ECONNABORTED"===e.code&&(e.message="Request timeout. Please try again."),Promise.reject(e)):(r._retry=!0,localStorage.removeItem("token"),delete bi.defaults.headers.common.Authorization,window.dispatchEvent(new CustomEvent("auth:logout")),Promise.reject(e))}));const wi={setAuthToken:e=>{e?(bi.defaults.headers.common.Authorization="Bearer ".concat(e),localStorage.setItem("token",e)):(delete bi.defaults.headers.common.Authorization,localStorage.removeItem("token"))},clearAuth:()=>{delete bi.defaults.headers.common.Authorization,localStorage.removeItem("token")},get:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return bi.get(e,t)},post:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return bi.post(e,t,n)},put:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return bi.put(e,t,n)},patch:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return bi.patch(e,t,n)},delete:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return bi.delete(e,t)},auth:{login:e=>bi.post("/auth/login",e),register:e=>bi.post("/auth/register",e),logout:()=>bi.post("/auth/logout"),refreshToken:()=>bi.post("/auth/refresh"),me:()=>bi.get("/auth/me"),forgotPassword:e=>bi.post("/auth/forgot-password",{email:e}),resetPassword:(e,t)=>bi.post("/auth/reset-password",{token:e,password:t}),changePassword:(e,t)=>bi.post("/auth/change-password",{currentPassword:e,newPassword:t})},upload:{single:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=new FormData;return n.append("file",e),Object.entries(t).forEach((e=>{let[t,r]=e;void 0!==r&&null!==r&&n.append(t,"object"===typeof r?JSON.stringify(r):r)})),bi.post("/upload",n,Oe({headers:{"Content-Type":"multipart/form-data"}},t.config))},batch:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=new FormData;return e.forEach((e=>n.append("files",e))),Object.entries(t).forEach((e=>{let[t,r]=e;"config"!==t&&void 0!==r&&null!==r&&n.append(t,"object"===typeof r?JSON.stringify(r):r)})),bi.post("/upload/batch",n,Oe({headers:{"Content-Type":"multipart/form-data"}},t.config))},list:function(){const e=new URLSearchParams(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{});return bi.get("/upload?".concat(e.toString()))},info:e=>bi.get("/upload/".concat(e,"/info")),delete:e=>bi.delete("/upload/".concat(e)),validate:e=>bi.post("/upload/".concat(e,"/validate")),cleanup:()=>bi.post("/upload/cleanup")},analysis:{start:e=>bi.post("/analysis/start",e),list:function(){const e=new URLSearchParams(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{});return bi.get("/analysis?".concat(e.toString()))},status:e=>bi.get("/analysis/".concat(e,"/status")),result:e=>bi.get("/analysis/".concat(e,"/result")),insights:e=>bi.get("/analysis/".concat(e,"/insights")),kpis:e=>bi.get("/analysis/".concat(e,"/kpis")),recommendations:e=>bi.get("/analysis/".concat(e,"/recommendations")),report:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"pdf",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return bi.post("/analysis/".concat(e,"/report"),{format:t,options:n})},rerun:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return bi.post("/analysis/".concat(e,"/rerun"),t)},delete:e=>bi.delete("/analysis/".concat(e))},agents:{execute:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return bi.post("/agents/execute",Oe({type:e,data:t},n))},executeAsync:function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return bi.post("/agents/execute/async",Oe({type:e,data:t},n))},status:e=>bi.get("/agents/".concat(e,"/status")),result:e=>bi.get("/agents/".concat(e,"/result")),list:function(){const e=new URLSearchParams(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{});return bi.get("/agents?".concat(e.toString()))},cancel:e=>bi.post("/agents/".concat(e,"/cancel")),delete:e=>bi.delete("/agents/".concat(e)),types:()=>bi.get("/agents/types")},utils:{health:()=>bi.get("/health"),ping:()=>bi.get("/ping"),version:()=>bi.get("/version"),download:async(e,t)=>{try{const n=await bi.get(e,{responseType:"blob"}),r=new Blob([n.data]),i=window.URL.createObjectURL(r),o=document.createElement("a");return o.href=i,o.download=t,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(i),!0}catch(n){throw console.error("Download failed:",n),n}},uploadWithProgress:(e,t,n)=>bi.post(e,t,{onUploadProgress:e=>{const t=Math.round(100*e.loaded/e.total);n&&n(t)}}),retryRequest:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3;for(let s=0;s<=t;s++)try{return await e()}catch(o){var r,i;if(s===t)throw o;if((null===(r=o.response)||void 0===r?void 0:r.status)>=400&&(null===(i=o.response)||void 0===i?void 0:i.status)<500)throw o;const e=n*Math.pow(2,s);await new Promise((t=>setTimeout(t,e)))}},batchRequests:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;const n=[],r=[];for(const i of e){const e=i().then((t=>(r.splice(r.indexOf(e),1),t)));n.push(e),r.push(e),r.length>=t&&await Promise.race(r)}return Promise.allSettled(n)}}},xi=localStorage.getItem("token");xi&&wi.setAuthToken(xi),window.addEventListener("auth:logout",(()=>{wi.clearAuth()}));const Si=wi;var Ei=n(507);const ki=(0,i.createContext)(),Ci="FETCH_ANALYSES_START",Pi="FETCH_ANALYSES_SUCCESS",Ai="FETCH_ANALYSES_ERROR",Ti="START_ANALYSIS_START",Ri="START_ANALYSIS_SUCCESS",_i="START_ANALYSIS_ERROR",Oi="UPDATE_ANALYSIS_STATUS",ji="ADD_ANALYSIS",Ni="REMOVE_ANALYSIS",Li="SET_CURRENT_ANALYSIS",Di="UPDATE_ANALYSIS_RESULT",Fi="CLEAR_ANALYSES",Mi={analyses:[],currentAnalysis:null,loading:!1,error:null,starting:!1,lastUpdated:null,pagination:{page:1,limit:20,total:0,totalPages:0}},Ii=(e,t)=>{var n,r,i;switch(t.type){case Ci:return Oe(Oe({},e),{},{loading:!0,error:null});case Pi:return Oe(Oe({},e),{},{loading:!1,analyses:t.payload.analyses||[],pagination:t.payload.pagination||e.pagination,error:null,lastUpdated:new Date});case Ai:return Oe(Oe({},e),{},{loading:!1,error:t.payload});case Ti:return Oe(Oe({},e),{},{starting:!0,error:null});case Ri:return Oe(Oe({},e),{},{starting:!1,analyses:[t.payload,...e.analyses],currentAnalysis:t.payload,error:null});case _i:return Oe(Oe({},e),{},{starting:!1,error:t.payload});case Oi:return Oe(Oe({},e),{},{analyses:e.analyses.map((e=>e.analysisId===t.payload.analysisId?Oe(Oe({},e),t.payload.updates):e)),currentAnalysis:(null===(n=e.currentAnalysis)||void 0===n?void 0:n.analysisId)===t.payload.analysisId?Oe(Oe({},e.currentAnalysis),t.payload.updates):e.currentAnalysis});case ji:return Oe(Oe({},e),{},{analyses:[t.payload,...e.analyses]});case Ni:return Oe(Oe({},e),{},{analyses:e.analyses.filter((e=>e.analysisId!==t.payload.analysisId)),currentAnalysis:(null===(r=e.currentAnalysis)||void 0===r?void 0:r.analysisId)===t.payload.analysisId?null:e.currentAnalysis});case Li:return Oe(Oe({},e),{},{currentAnalysis:t.payload});case Di:return Oe(Oe({},e),{},{analyses:e.analyses.map((e=>e.analysisId===t.payload.analysisId?Oe(Oe({},e),{},{result:t.payload.result}):e)),currentAnalysis:(null===(i=e.currentAnalysis)||void 0===i?void 0:i.analysisId)===t.payload.analysisId?Oe(Oe({},e.currentAnalysis),{},{result:t.payload.result}):e.currentAnalysis});case Fi:return Oe({},Mi);default:return e}},Ui=e=>{let{children:t}=e;const[n,r]=(0,i.useReducer)(Ii,Mi),o=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{r({type:Ci});const t=new URLSearchParams;Object.entries(e).forEach((e=>{let[n,r]=e;void 0!==r&&null!==r&&""!==r&&t.append(n,r)}));const n=await Si.get("/analysis?".concat(t.toString()));return r({type:Pi,payload:n.data.data}),n.data.data}catch(i){var t,n;const e=(null===(t=i.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.message)||"Failed to fetch analyses";throw r({type:Ai,payload:e}),i}},s=async e=>{try{const t=await Si.get("/analysis/".concat(e,"/status"));return r({type:Oi,payload:{analysisId:e,updates:t.data.data}}),t.data.data}catch(t){throw console.error("Failed to get analysis status:",t),t}};(0,i.useEffect)((()=>{o().catch(console.error)}),[]);const a=Oe(Oe({},n),{},{fetchAnalyses:o,startAnalysis:async e=>{try{r({type:Ti});const t=await Si.post("/analysis/start",e);return r({type:Ri,payload:t.data.data}),t.data.data}catch(i){var t,n;const e=(null===(t=i.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.message)||"Failed to start analysis";throw r({type:_i,payload:e}),i}},getAnalysisStatus:s,getAnalysisResult:async e=>{try{const t=await Si.get("/analysis/".concat(e,"/result"));return r({type:Di,payload:{analysisId:e,result:t.data.data.result}}),t.data.data}catch(t){throw console.error("Failed to get analysis result:",t),t}},getAnalysisInsights:async e=>{try{return(await Si.get("/analysis/".concat(e,"/insights"))).data.data}catch(t){throw console.error("Failed to get analysis insights:",t),t}},getAnalysisKPIs:async e=>{try{return(await Si.get("/analysis/".concat(e,"/kpis"))).data.data}catch(t){throw console.error("Failed to get analysis KPIs:",t),t}},getAnalysisRecommendations:async e=>{try{return(await Si.get("/analysis/".concat(e,"/recommendations"))).data.data}catch(t){throw console.error("Failed to get analysis recommendations:",t),t}},generateAnalysisReport:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"pdf",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{const r=await Si.post("/analysis/".concat(e,"/report"),{format:t,options:n},{responseType:"pdf"===t?"blob":"json"});if("pdf"===t){const t=new Blob([r.data],{type:"application/pdf"}),n=window.URL.createObjectURL(t),i=document.createElement("a");return i.href=n,i.download="analysis-report-".concat(e,".pdf"),document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(n),!0}return r.data.data}catch(r){throw console.error("Failed to generate analysis report:",r),r}},rerunAnalysis:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{const n=await Si.post("/analysis/".concat(e,"/rerun"),t);return r({type:Oi,payload:{analysisId:e,updates:n.data.data}}),n.data.data}catch(n){throw console.error("Failed to rerun analysis:",n),n}},deleteAnalysis:async e=>{try{return await Si.delete("/analysis/".concat(e)),r({type:Ni,payload:{analysisId:e}}),!0}catch(t){throw console.error("Failed to delete analysis:",t),t}},setCurrentAnalysis:e=>{r({type:Li,payload:e})},pollAnalysisStatus:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3e3;const n=async()=>{try{const r=await s(e);return"running"!==r.status&&"pending"!==r.status||setTimeout(n,t),r}catch(r){console.error("Analysis polling error:",r)}};return n()},clearAnalyses:()=>{r({type:Fi})},getAnalysesByStatus:e=>n.analyses.filter((t=>t.status===e)),getRunningAnalyses:()=>n.analyses.filter((e=>"running"===e.status||"pending"===e.status)),getCompletedAnalyses:()=>n.analyses.filter((e=>"completed"===e.status)),getFailedAnalyses:()=>n.analyses.filter((e=>"failed"===e.status)),hasRunningAnalyses:()=>n.analyses.some((e=>"running"===e.status||"pending"===e.status))});return(0,Ei.jsx)(ki.Provider,{value:a,children:t})},Bi=(0,i.createContext)(),Vi="FETCH_AGENTS_START",zi="FETCH_AGENTS_SUCCESS",qi="FETCH_AGENTS_ERROR",Qi="EXECUTE_AGENT_START",Hi="EXECUTE_AGENT_SUCCESS",$i="EXECUTE_AGENT_ERROR",Wi="UPDATE_AGENT_STATUS",Ki="ADD_AGENT_EXECUTION",Yi="REMOVE_AGENT_EXECUTION",Gi="SET_AGENT_TYPES",Xi="CLEAR_AGENTS",Ji={agents:[],agentTypes:[],executions:[],loading:!1,error:null,executing:{},lastUpdated:null},Zi=(e,t)=>{switch(t.type){case Vi:return Oe(Oe({},e),{},{loading:!0,error:null});case zi:return Oe(Oe({},e),{},{loading:!1,executions:t.payload.executions||[],error:null,lastUpdated:new Date});case qi:return Oe(Oe({},e),{},{loading:!1,error:t.payload});case Qi:return Oe(Oe({},e),{},{executing:Oe(Oe({},e.executing),{},{[t.payload.type]:!0}),error:null});case Hi:return Oe(Oe({},e),{},{executing:Oe(Oe({},e.executing),{},{[t.payload.type]:!1}),executions:[t.payload.execution,...e.executions],error:null});case $i:return Oe(Oe({},e),{},{executing:Oe(Oe({},e.executing),{},{[t.payload.type]:!1}),error:t.payload.error});case Wi:return Oe(Oe({},e),{},{executions:e.executions.map((e=>e.agentId===t.payload.agentId?Oe(Oe({},e),t.payload.updates):e))});case Ki:return Oe(Oe({},e),{},{executions:[t.payload,...e.executions]});case Yi:return Oe(Oe({},e),{},{executions:e.executions.filter((e=>e.agentId!==t.payload.agentId))});case Gi:return Oe(Oe({},e),{},{agentTypes:t.payload});case Xi:return Oe({},Ji);default:return e}},eo=e=>{let{children:t}=e;const[n,r]=(0,i.useReducer)(Zi,Ji),o=async()=>{try{const e=await Si.get("/agents/types");return r({type:Gi,payload:e.data.data.agentTypes}),e.data.data.agentTypes}catch(e){throw console.error("Failed to fetch agent types:",e),e}},s=async e=>{try{const t=await Si.get("/agents/".concat(e,"/status"));return r({type:Wi,payload:{agentId:e,updates:t.data.data}}),t.data.data}catch(t){throw console.error("Failed to get agent status:",t),t}};(0,i.useEffect)((()=>{o().catch(console.error)}),[]);const a=Oe(Oe({},n),{},{fetchAgents:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{r({type:Vi});const t=new URLSearchParams;Object.entries(e).forEach((e=>{let[n,r]=e;void 0!==r&&null!==r&&""!==r&&t.append(n,r)}));const n=await Si.get("/agents?".concat(t.toString()));return r({type:zi,payload:n.data.data}),n.data.data}catch(i){var t,n;const e=(null===(t=i.response)||void 0===t||null===(n=t.data)||void 0===n?void 0:n.message)||"Failed to fetch agents";throw r({type:qi,payload:e}),i}},fetchAgentTypes:o,executeAgent:async function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{r({type:Qi,payload:{type:e}});const i={type:e,data:t,context:n.context||{},options:n.options||{}},o=n.async?"/agents/execute/async":"/agents/execute",s=await Si.post(o,i);return r({type:Hi,payload:{type:e,execution:s.data.data}}),s.data.data}catch(s){var i,o;const t=(null===(i=s.response)||void 0===i||null===(o=i.data)||void 0===o?void 0:o.message)||"Failed to execute agent";throw r({type:$i,payload:{type:e,error:t}}),s}},getAgentStatus:s,getAgentResult:async e=>{try{return(await Si.get("/agents/".concat(e,"/result"))).data.data}catch(t){throw console.error("Failed to get agent result:",t),t}},cancelAgentExecution:async e=>{try{return await Si.post("/agents/".concat(e,"/cancel")),r({type:Wi,payload:{agentId:e,updates:{status:"cancelled"}}}),!0}catch(t){throw console.error("Failed to cancel agent execution:",t),t}},deleteAgentExecution:async e=>{try{return await Si.delete("/agents/".concat(e)),r({type:Yi,payload:{agentId:e}}),!0}catch(t){throw console.error("Failed to delete agent execution:",t),t}},pollAgentStatus:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2e3;const n=async()=>{try{const r=await s(e);return"running"!==r.status&&"pending"!==r.status||setTimeout(n,t),r}catch(r){console.error("Polling error:",r)}};return n()},clearAgents:()=>{r({type:Xi})},isExecuting:e=>!!n.executing[e],getExecutionsByType:e=>n.executions.filter((t=>t.type===e)),getRunningExecutions:()=>n.executions.filter((e=>"running"===e.status||"pending"===e.status)),getCompletedExecutions:()=>n.executions.filter((e=>"completed"===e.status)),getFailedExecutions:()=>n.executions.filter((e=>"failed"===e.status))});return(0,Ei.jsx)(Bi.Provider,{value:a,children:t})},to=Object.create(null);to.open="0",to.close="1",to.ping="2",to.pong="3",to.message="4",to.upgrade="5",to.noop="6";const no=Object.create(null);Object.keys(to).forEach((e=>{no[to[e]]=e}));const ro={type:"error",data:"parser error"},io="function"===typeof Blob||"undefined"!==typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),oo="function"===typeof ArrayBuffer,so=e=>"function"===typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,ao=(e,t,n)=>{let{type:r,data:i}=e;return io&&i instanceof Blob?t?n(i):lo(i,n):oo&&(i instanceof ArrayBuffer||so(i))?t?n(i):lo(new Blob([i]),n):n(to[r]+(i||""))},lo=(e,t)=>{const n=new FileReader;return n.onload=function(){const e=n.result.split(",")[1];t("b"+(e||""))},n.readAsDataURL(e)};function uo(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let co;const fo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ho="undefined"===typeof Uint8Array?[]:new Uint8Array(256);for(let n=0;n<64;n++)ho[fo.charCodeAt(n)]=n;const po="function"===typeof ArrayBuffer,mo=(e,t)=>{if("string"!==typeof e)return{type:"message",data:go(e,t)};const n=e.charAt(0);if("b"===n)return{type:"message",data:yo(e.substring(1),t)};return no[n]?e.length>1?{type:no[n],data:e.substring(1)}:{type:no[n]}:ro},yo=(e,t)=>{if(po){const n=(e=>{let t,n,r,i,o,s=.75*e.length,a=e.length,l=0;"="===e[e.length-1]&&(s--,"="===e[e.length-2]&&s--);const u=new ArrayBuffer(s),c=new Uint8Array(u);for(t=0;t<a;t+=4)n=ho[e.charCodeAt(t)],r=ho[e.charCodeAt(t+1)],i=ho[e.charCodeAt(t+2)],o=ho[e.charCodeAt(t+3)],c[l++]=n<<2|r>>4,c[l++]=(15&r)<<4|i>>2,c[l++]=(3&i)<<6|63&o;return u})(e);return go(n,t)}return{base64:!0,data:e}},go=(e,t)=>"blob"===t?e instanceof Blob?e:new Blob([e]):e instanceof ArrayBuffer?e:e.buffer,vo=String.fromCharCode(30);function bo(){return new TransformStream({transform(e,t){!function(e,t){io&&e.data instanceof Blob?e.data.arrayBuffer().then(uo).then(t):oo&&(e.data instanceof ArrayBuffer||so(e.data))?t(uo(e.data)):ao(e,!1,(e=>{co||(co=new TextEncoder),t(co.encode(e))}))}(e,(n=>{const r=n.length;let i;if(r<126)i=new Uint8Array(1),new DataView(i.buffer).setUint8(0,r);else if(r<65536){i=new Uint8Array(3);const e=new DataView(i.buffer);e.setUint8(0,126),e.setUint16(1,r)}else{i=new Uint8Array(9);const e=new DataView(i.buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(r))}e.data&&"string"!==typeof e.data&&(i[0]|=128),t.enqueue(i),t.enqueue(n)}))}})}let wo;function xo(e){return e.reduce(((e,t)=>e+t.length),0)}function So(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let r=0;for(let i=0;i<t;i++)n[i]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),n}function Eo(e){if(e)return function(e){for(var t in Eo.prototype)e[t]=Eo.prototype[t];return e}(e)}Eo.prototype.on=Eo.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},Eo.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},Eo.prototype.off=Eo.prototype.removeListener=Eo.prototype.removeAllListeners=Eo.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<r.length;i++)if((n=r[i])===t||n.fn===t){r.splice(i,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},Eo.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var i=(n=n.slice(0)).length;r<i;++r)n[r].apply(this,t)}return this},Eo.prototype.emitReserved=Eo.prototype.emit,Eo.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},Eo.prototype.hasListeners=function(e){return!!this.listeners(e).length};const ko="function"===typeof Promise&&"function"===typeof Promise.resolve?e=>Promise.resolve().then(e):(e,t)=>t(e,0),Co="undefined"!==typeof self?self:"undefined"!==typeof window?window:Function("return this")();function Po(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return n.reduce(((t,n)=>(e.hasOwnProperty(n)&&(t[n]=e[n]),t)),{})}const Ao=Co.setTimeout,To=Co.clearTimeout;function Ro(e,t){t.useNativeTimers?(e.setTimeoutFn=Ao.bind(Co),e.clearTimeoutFn=To.bind(Co)):(e.setTimeoutFn=Co.setTimeout.bind(Co),e.clearTimeoutFn=Co.clearTimeout.bind(Co))}function _o(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class Oo extends Error{constructor(e,t,n){super(e),this.description=t,this.context=n,this.type="TransportError"}}class jo extends Eo{constructor(e){super(),this.writable=!1,Ro(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,n){return super.emitReserved("error",new Oo(e,t,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){const t=mo(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){const e=this.opts.hostname;return-1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){const t=function(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}(e);return t.length?"?"+t:""}}class No extends jo{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";const t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(e++,this.once("pollComplete",(function(){--e||t()}))),this.writable||(e++,this.once("drain",(function(){--e||t()})))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){((e,t)=>{const n=e.split(vo),r=[];for(let i=0;i<n.length;i++){const e=mo(n[i],t);if(r.push(e),"error"===e.type)break}return r})(e,this.socket.binaryType).forEach((e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)})),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){const e=()=>{this.write([{type:"close"}])};"open"===this.readyState?e():this.once("open",e)}write(e){this.writable=!1,((e,t)=>{const n=e.length,r=new Array(n);let i=0;e.forEach(((e,o)=>{ao(e,!1,(e=>{r[o]=e,++i===n&&t(r.join(vo))}))}))})(e,(e=>{this.doWrite(e,(()=>{this.writable=!0,this.emitReserved("drain")}))}))}uri(){const e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=_o()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let Lo=!1;try{Lo="undefined"!==typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(Qm){}const Do=Lo;function Fo(){}class Mo extends No{constructor(e){if(super(e),"undefined"!==typeof location){const t="https:"===location.protocol;let n=location.port;n||(n=t?"443":"80"),this.xd="undefined"!==typeof location&&e.hostname!==location.hostname||n!==e.port}}doWrite(e,t){const n=this.request({method:"POST",data:e});n.on("success",t),n.on("error",((e,t)=>{this.onError("xhr post error",e,t)}))}doPoll(){const e=this.request();e.on("data",this.onData.bind(this)),e.on("error",((e,t)=>{this.onError("xhr poll error",e,t)})),this.pollXhr=e}}class Io extends Eo{constructor(e,t,n){super(),this.createRequest=e,Ro(this,n),this._opts=n,this._method=n.method||"GET",this._uri=t,this._data=void 0!==n.data?n.data:null,this._create()}_create(){var e;const t=Po(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;const n=this._xhr=this.createRequest(t);try{n.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0);for(let e in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&n.setRequestHeader(e,this._opts.extraHeaders[e])}}catch(je){}if("POST"===this._method)try{n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(je){}try{n.setRequestHeader("Accept","*/*")}catch(je){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(n),"withCredentials"in n&&(n.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(n.timeout=this._opts.requestTimeout),n.onreadystatechange=()=>{var e;3===n.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(n.getResponseHeader("set-cookie"))),4===n.readyState&&(200===n.status||1223===n.status?this._onLoad():this.setTimeoutFn((()=>{this._onError("number"===typeof n.status?n.status:0)}),0))},n.send(this._data)}catch(je){return void this.setTimeoutFn((()=>{this._onError(je)}),0)}"undefined"!==typeof document&&(this._index=Io.requestsCount++,Io.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if("undefined"!==typeof this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=Fo,e)try{this._xhr.abort()}catch(je){}"undefined"!==typeof document&&delete Io.requests[this._index],this._xhr=null}}_onLoad(){const e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}if(Io.requestsCount=0,Io.requests={},"undefined"!==typeof document)if("function"===typeof attachEvent)attachEvent("onunload",Uo);else if("function"===typeof addEventListener){addEventListener("onpagehide"in Co?"pagehide":"unload",Uo,!1)}function Uo(){for(let e in Io.requests)Io.requests.hasOwnProperty(e)&&Io.requests[e].abort()}const Bo=function(){const e=Vo({xdomain:!1});return e&&null!==e.responseType}();function Vo(e){const t=e.xdomain;try{if("undefined"!==typeof XMLHttpRequest&&(!t||Do))return new XMLHttpRequest}catch(je){}if(!t)try{return new(Co[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(je){}}const zo="undefined"!==typeof navigator&&"string"===typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class qo extends jo{get name(){return"websocket"}doOpen(){const e=this.uri(),t=this.opts.protocols,n=zo?{}:Po(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,n)}catch(Qm){return this.emitReserved("error",Qm)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;ao(n,this.supportsBinary,(e=>{try{this.doWrite(n,e)}catch(je){}r&&ko((()=>{this.writable=!0,this.emitReserved("drain")}),this.setTimeoutFn)}))}}doClose(){"undefined"!==typeof this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=_o()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}const Qo=Co.WebSocket||Co.MozWebSocket;const Ho={websocket:class extends qo{createSocket(e,t,n){return zo?new Qo(e,t,n):t?new Qo(e,t):new Qo(e)}doWrite(e,t){this.ws.send(t)}},webtransport:class extends jo{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(Qm){return this.emitReserved("error",Qm)}this._transport.closed.then((()=>{this.onClose()})).catch((e=>{this.onError("webtransport error",e)})),this._transport.ready.then((()=>{this._transport.createBidirectionalStream().then((e=>{const t=function(e,t){wo||(wo=new TextDecoder);const n=[];let r=0,i=-1,o=!1;return new TransformStream({transform(s,a){for(n.push(s);;){if(0===r){if(xo(n)<1)break;const e=So(n,1);o=128===(128&e[0]),i=127&e[0],r=i<126?3:126===i?1:2}else if(1===r){if(xo(n)<2)break;const e=So(n,2);i=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),r=3}else if(2===r){if(xo(n)<8)break;const e=So(n,8),t=new DataView(e.buffer,e.byteOffset,e.length),o=t.getUint32(0);if(o>Math.pow(2,21)-1){a.enqueue(ro);break}i=o*Math.pow(2,32)+t.getUint32(4),r=3}else{if(xo(n)<i)break;const e=So(n,i);a.enqueue(mo(o?e:wo.decode(e),t)),r=0}if(0===i||i>e){a.enqueue(ro);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=e.readable.pipeThrough(t).getReader(),r=bo();r.readable.pipeTo(e.writable),this._writer=r.writable.getWriter();const i=()=>{n.read().then((e=>{let{done:t,value:n}=e;t||(this.onPacket(n),i())})).catch((e=>{}))};i();const o={type:"open"};this.query.sid&&(o.data='{"sid":"'.concat(this.query.sid,'"}')),this._writer.write(o).then((()=>this.onOpen()))}))}))}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;this._writer.write(n).then((()=>{r&&ko((()=>{this.writable=!0,this.emitReserved("drain")}),this.setTimeoutFn)}))}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}},polling:class extends Mo{constructor(e){super(e);const t=e&&e.forceBase64;this.supportsBinary=Bo&&!t}request(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.assign(e,{xd:this.xd},this.opts),new Io(Vo,this.uri(),e)}}},$o=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Wo=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Ko(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),r=e.indexOf("]");-1!=n&&-1!=r&&(e=e.substring(0,n)+e.substring(n,r).replace(/:/g,";")+e.substring(r,e.length));let i=$o.exec(e||""),o={},s=14;for(;s--;)o[Wo[s]]=i[s]||"";return-1!=n&&-1!=r&&(o.source=t,o.host=o.host.substring(1,o.host.length-1).replace(/;/g,":"),o.authority=o.authority.replace("[","").replace("]","").replace(/;/g,":"),o.ipv6uri=!0),o.pathNames=function(e,t){const n=/\/{2,9}/g,r=t.replace(n,"/").split("/");"/"!=t.slice(0,1)&&0!==t.length||r.splice(0,1);"/"==t.slice(-1)&&r.splice(r.length-1,1);return r}(0,o.path),o.queryKey=function(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,(function(e,t,r){t&&(n[t]=r)})),n}(0,o.query),o}const Yo="function"===typeof addEventListener&&"function"===typeof removeEventListener,Go=[];Yo&&addEventListener("offline",(()=>{Go.forEach((e=>e()))}),!1);class Xo extends Eo{constructor(e,t){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"===typeof e&&(t=e,e=null),e){const n=Ko(e);t.hostname=n.host,t.secure="https"===n.protocol||"wss"===n.protocol,t.port=n.port,n.query&&(t.query=n.query)}else t.host&&(t.hostname=Ko(t.host).host);Ro(this,t),this.secure=null!=t.secure?t.secure:"undefined"!==typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!==typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!==typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach((e=>{const t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e})),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"===typeof this.opts.query&&(this.opts.query=function(e){let t={},n=e.split("&");for(let r=0,i=n.length;r<i;r++){let e=n[r].split("=");t[decodeURIComponent(e[0])]=decodeURIComponent(e[1])}return t}(this.opts.query)),Yo&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},Go.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){const t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);const n=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](n)}_open(){if(0===this.transports.length)return void this.setTimeoutFn((()=>{this.emitReserved("error","No transports available")}),0);const e=this.opts.rememberUpgrade&&Xo.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";const t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",(e=>this._onClose("transport close",e)))}onOpen(){this.readyState="open",Xo.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const t=new Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn((()=>{this._onClose("ping timeout")}),e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let n=0;n<this.writeBuffer.length;n++){const r=this.writeBuffer[n].data;if(r&&(e+="string"===typeof(t=r)?function(e){let t=0,n=0;for(let r=0,i=e.length;r<i;r++)t=e.charCodeAt(r),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(r++,n+=4);return n}(t):Math.ceil(1.33*(t.byteLength||t.size))),n>0&&e>this._maxPayload)return this.writeBuffer.slice(0,n);e+=2}var t;return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,ko((()=>{this._onClose("ping timeout")}),this.setTimeoutFn)),e}write(e,t,n){return this._sendPacket("message",e,t,n),this}send(e,t,n){return this._sendPacket("message",e,t,n),this}_sendPacket(e,t,n,r){if("function"===typeof t&&(r=t,t=void 0),"function"===typeof n&&(r=n,n=null),"closing"===this.readyState||"closed"===this.readyState)return;(n=n||{}).compress=!1!==n.compress;const i={type:e,data:t,options:n};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),r&&this.once("flush",r),this.flush()}close(){const e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},n=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",(()=>{this.upgrading?n():e()})):this.upgrading?n():e()),this}_onError(e){if(Xo.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Yo&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const e=Go.indexOf(this._offlineEventListener);-1!==e&&Go.splice(e,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}Xo.protocol=4;class Jo extends Xo{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),n=!1;Xo.priorWebsocketSuccess=!1;const r=()=>{n||(t.send([{type:"ping",data:"probe"}]),t.once("packet",(e=>{if(!n)if("pong"===e.type&&"probe"===e.data){if(this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;Xo.priorWebsocketSuccess="websocket"===t.name,this.transport.pause((()=>{n||"closed"!==this.readyState&&(u(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())}))}else{const e=new Error("probe error");e.transport=t.name,this.emitReserved("upgradeError",e)}})))};function i(){n||(n=!0,u(),t.close(),t=null)}const o=e=>{const n=new Error("probe error: "+e);n.transport=t.name,i(),this.emitReserved("upgradeError",n)};function s(){o("transport closed")}function a(){o("socket closed")}function l(e){t&&e.name!==t.name&&i()}const u=()=>{t.removeListener("open",r),t.removeListener("error",o),t.removeListener("close",s),this.off("close",a),this.off("upgrading",l)};t.once("open",r),t.once("error",o),t.once("close",s),this.once("close",a),this.once("upgrading",l),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn((()=>{n||t.open()}),200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){const t=[];for(let n=0;n<e.length;n++)~this.transports.indexOf(e[n])&&t.push(e[n]);return t}}class Zo extends Jo{constructor(e){const t="object"===typeof e?e:arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(!t.transports||t.transports&&"string"===typeof t.transports[0])&&(t.transports=(t.transports||["polling","websocket","webtransport"]).map((e=>Ho[e])).filter((e=>!!e))),super(e,t)}}const es="function"===typeof ArrayBuffer,ts=Object.prototype.toString,ns="function"===typeof Blob||"undefined"!==typeof Blob&&"[object BlobConstructor]"===ts.call(Blob),rs="function"===typeof File||"undefined"!==typeof File&&"[object FileConstructor]"===ts.call(File);function is(e){return es&&(e instanceof ArrayBuffer||(e=>"function"===typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer)(e))||ns&&e instanceof Blob||rs&&e instanceof File}function os(e,t){if(!e||"object"!==typeof e)return!1;if(Array.isArray(e)){for(let t=0,n=e.length;t<n;t++)if(os(e[t]))return!0;return!1}if(is(e))return!0;if(e.toJSON&&"function"===typeof e.toJSON&&1===arguments.length)return os(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&os(e[n]))return!0;return!1}function ss(e){const t=[],n=e.data,r=e;return r.data=as(n,t),r.attachments=t.length,{packet:r,buffers:t}}function as(e,t){if(!e)return e;if(is(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}if(Array.isArray(e)){const n=new Array(e.length);for(let r=0;r<e.length;r++)n[r]=as(e[r],t);return n}if("object"===typeof e&&!(e instanceof Date)){const n={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=as(e[r],t));return n}return e}function ls(e,t){return e.data=us(e.data,t),delete e.attachments,e}function us(e,t){if(!e)return e;if(e&&!0===e._placeholder){if("number"===typeof e.num&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=us(e[n],t);else if("object"===typeof e)for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=us(e[n],t));return e}const cs=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],ds=5;var fs;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(fs||(fs={}));class hs{constructor(e){this.replacer=e}encode(e){return e.type!==fs.EVENT&&e.type!==fs.ACK||!os(e)?[this.encodeAsString(e)]:this.encodeAsBinary({type:e.type===fs.EVENT?fs.BINARY_EVENT:fs.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id})}encodeAsString(e){let t=""+e.type;return e.type!==fs.BINARY_EVENT&&e.type!==fs.BINARY_ACK||(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){const t=ss(e),n=this.encodeAsString(t.packet),r=t.buffers;return r.unshift(n),r}}function ps(e){return"[object Object]"===Object.prototype.toString.call(e)}class ms extends Eo{constructor(e){super(),this.reviver=e}add(e){let t;if("string"===typeof e){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(e);const n=t.type===fs.BINARY_EVENT;n||t.type===fs.BINARY_ACK?(t.type=n?fs.EVENT:fs.ACK,this.reconstructor=new ys(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else{if(!is(e)&&!e.base64)throw new Error("Unknown type: "+e);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");t=this.reconstructor.takeBinaryData(e),t&&(this.reconstructor=null,super.emitReserved("decoded",t))}}decodeString(e){let t=0;const n={type:Number(e.charAt(0))};if(void 0===fs[n.type])throw new Error("unknown packet type "+n.type);if(n.type===fs.BINARY_EVENT||n.type===fs.BINARY_ACK){const r=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);const i=e.substring(r,t);if(i!=Number(i)||"-"!==e.charAt(t))throw new Error("Illegal attachments");n.attachments=Number(i)}if("/"===e.charAt(t+1)){const r=t+1;for(;++t;){if(","===e.charAt(t))break;if(t===e.length)break}n.nsp=e.substring(r,t)}else n.nsp="/";const r=e.charAt(t+1);if(""!==r&&Number(r)==r){const r=t+1;for(;++t;){const n=e.charAt(t);if(null==n||Number(n)!=n){--t;break}if(t===e.length)break}n.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){const r=this.tryParse(e.substr(t));if(!ms.isPayloadValid(n.type,r))throw new Error("invalid payload");n.data=r}return n}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(je){return!1}}static isPayloadValid(e,t){switch(e){case fs.CONNECT:return ps(t);case fs.DISCONNECT:return void 0===t;case fs.CONNECT_ERROR:return"string"===typeof t||ps(t);case fs.EVENT:case fs.BINARY_EVENT:return Array.isArray(t)&&("number"===typeof t[0]||"string"===typeof t[0]&&-1===cs.indexOf(t[0]));case fs.ACK:case fs.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class ys{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const e=ls(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function gs(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const vs=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class bs extends Eo{constructor(e,t,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const e=this.io;this.subs=[gs(e,"open",this.onopen.bind(this)),gs(e,"packet",this.onpacket.bind(this)),gs(e,"error",this.onerror.bind(this)),gs(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.unshift("message"),this.emit.apply(this,t),this}emit(e){var t,n,r;if(vs.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');for(var i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];if(o.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(o),this;const a={type:fs.EVENT,data:o,options:{}};if(a.options.compress=!1!==this.flags.compress,"function"===typeof o[o.length-1]){const e=this.ids++,t=o.pop();this._registerAckCallback(e,t),a.id=e}const l=null===(n=null===(t=this.io.engine)||void 0===t?void 0:t.transport)||void 0===n?void 0:n.writable,u=this.connected&&!(null===(r=this.io.engine)||void 0===r?void 0:r._hasPingExpired());return this.flags.volatile&&!l||(u?(this.notifyOutgoingListeners(a),this.packet(a)):this.sendBuffer.push(a)),this.flags={},this}_registerAckCallback(e,t){var n,r=this;const i=null!==(n=this.flags.timeout)&&void 0!==n?n:this._opts.ackTimeout;if(void 0===i)return void(this.acks[e]=t);const o=this.io.setTimeoutFn((()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&this.sendBuffer.splice(t,1);t.call(this,new Error("operation has timed out"))}),i),s=function(){r.io.clearTimeoutFn(o);for(var e=arguments.length,n=new Array(e),i=0;i<e;i++)n[i]=arguments[i];t.apply(r,n)};s.withError=!0,this.acks[e]=s}emitWithAck(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return new Promise(((t,r)=>{const i=(e,n)=>e?r(e):t(n);i.withError=!0,n.push(i),this.emit(e,...n)}))}_addToQueue(e){var t=this;let n;"function"===typeof e[e.length-1]&&(n=e.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((function(e){if(r!==t._queue[0])return;if(null!==e)r.tryCount>t._opts.retries&&(t._queue.shift(),n&&n(e));else if(t._queue.shift(),n){for(var i=arguments.length,o=new Array(i>1?i-1:0),s=1;s<i;s++)o[s-1]=arguments[s];n(null,...o)}return r.pending=!1,t._drainQueue()})),this._queue.push(r),this._drainQueue()}_drainQueue(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.connected||0===this._queue.length)return;const t=this._queue[0];t.pending&&!e||(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){"function"==typeof this.auth?this.auth((e=>{this._sendConnectPacket(e)})):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:fs.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach((e=>{if(!this.sendBuffer.some((t=>String(t.id)===e))){const t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,new Error("socket has been disconnected"))}}))}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case fs.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case fs.EVENT:case fs.BINARY_EVENT:this.onevent(e);break;case fs.ACK:case fs.BINARY_ACK:this.onack(e);break;case fs.DISCONNECT:this.ondisconnect();break;case fs.CONNECT_ERROR:this.destroy();const t=new Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){const t=e.data||[];null!=e.id&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const t=this._anyListeners.slice();for(const n of t)n.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&"string"===typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){const t=this;let n=!1;return function(){if(!n){n=!0;for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];t.packet({type:fs.ACK,id:e,data:i})}}}onack(e){const t=this.acks[e.id];"function"===typeof t&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach((e=>this.emitEvent(e))),this.receiveBuffer=[],this.sendBuffer.forEach((e=>{this.notifyOutgoingListeners(e),this.packet(e)})),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach((e=>e())),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:fs.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){const t=this._anyListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const t=this._anyOutgoingListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const t=this._anyOutgoingListeners.slice();for(const n of t)n.apply(this,e.data)}}}function ws(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}ws.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=0==(1&Math.floor(10*t))?e-n:e+n}return 0|Math.min(e,this.max)},ws.prototype.reset=function(){this.attempts=0},ws.prototype.setMin=function(e){this.ms=e},ws.prototype.setMax=function(e){this.max=e},ws.prototype.setJitter=function(e){this.jitter=e};class xs extends Eo{constructor(e,n){var r;super(),this.nsps={},this.subs=[],e&&"object"===typeof e&&(n=e,e=void 0),(n=n||{}).path=n.path||"/socket.io",this.opts=n,Ro(this,n),this.reconnection(!1!==n.reconnection),this.reconnectionAttempts(n.reconnectionAttempts||1/0),this.reconnectionDelay(n.reconnectionDelay||1e3),this.reconnectionDelayMax(n.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(r=n.randomizationFactor)&&void 0!==r?r:.5),this.backoff=new ws({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==n.timeout?2e4:n.timeout),this._readyState="closed",this.uri=e;const i=n.parser||t;this.encoder=new i.Encoder,this.decoder=new i.Decoder,this._autoConnect=!1!==n.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new Zo(this.uri,this.opts);const t=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const r=gs(t,"open",(function(){n.onopen(),e&&e()})),i=t=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},o=gs(t,"error",i);if(!1!==this._timeout){const e=this._timeout,n=this.setTimeoutFn((()=>{r(),i(new Error("timeout")),t.close()}),e);this.opts.autoUnref&&n.unref(),this.subs.push((()=>{this.clearTimeoutFn(n)}))}return this.subs.push(r),this.subs.push(o),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const e=this.engine;this.subs.push(gs(e,"ping",this.onping.bind(this)),gs(e,"data",this.ondata.bind(this)),gs(e,"error",this.onerror.bind(this)),gs(e,"close",this.onclose.bind(this)),gs(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(je){this.onclose("parse error",je)}}ondecoded(e){ko((()=>{this.emitReserved("packet",e)}),this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let n=this.nsps[e];return n?this._autoConnect&&!n.active&&n.connect():(n=new bs(this,e,t),this.nsps[e]=n),n}_destroy(e){const t=Object.keys(this.nsps);for(const n of t){if(this.nsps[n].active)return}this._close()}_packet(e){const t=this.encoder.encode(e);for(let n=0;n<t.length;n++)this.engine.write(t[n],e.options)}cleanup(){this.subs.forEach((e=>e())),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var n;this.cleanup(),null===(n=this.engine)||void 0===n||n.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const t=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn((()=>{e.skipReconnect||(this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open((t=>{t?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):e.onreconnect()})))}),t);this.opts.autoUnref&&n.unref(),this.subs.push((()=>{this.clearTimeoutFn(n)}))}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}const Ss={};function Es(e,t){"object"===typeof e&&(t=e,e=void 0);const n=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2?arguments[2]:void 0,r=e;n=n||"undefined"!==typeof location&&location,null==e&&(e=n.protocol+"//"+n.host),"string"===typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?n.protocol+e:n.host+e),/^(https?|wss?):\/\//.test(e)||(e="undefined"!==typeof n?n.protocol+"//"+e:"https://"+e),r=Ko(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const i=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+i+":"+r.port+t,r.href=r.protocol+"://"+i+(n&&n.port===r.port?"":":"+r.port),r}(e,(t=t||{}).path||"/socket.io"),r=n.source,i=n.id,o=n.path,s=Ss[i]&&o in Ss[i].nsps;let a;return t.forceNew||t["force new connection"]||!1===t.multiplex||s?a=new xs(r,t):(Ss[i]||(Ss[i]=new xs(r,t)),a=Ss[i]),n.query&&!t.query&&(t.query=n.queryKey),a.socket(n.path,t)}Object.assign(Es,{Manager:xs,Socket:bs,io:Es,connect:Es});const ks=(0,i.createContext)(),Cs=e=>{let{children:t}=e;const[n,r]=(0,i.useState)(null),[o,s]=(0,i.useState)(!1);(0,i.useEffect)((()=>{const e=Es({NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_BACKEND_URL||"http://localhost:5000",{autoConnect:!0,reconnection:!0,reconnectionDelay:1e3,reconnectionAttempts:5,maxReconnectionAttempts:5});return e.on("connect",(()=>{s(!0),console.log("Socket connected:",e.id)})),e.on("disconnect",(e=>{s(!1),console.log("Socket disconnected:",e)})),e.on("connect_error",(e=>{console.error("Socket connection error:",e),s(!1)})),r(e),()=>{e.close()}}),[]);const a={socket:n,isConnected:o};return(0,Ei.jsx)(ks.Provider,{value:a,children:t})};var Ps=n(362),As=n.n(Ps);const Ts=(0,i.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Rs=(0,i.createContext)({}),_s=(0,i.createContext)(null),Os="undefined"!==typeof document,js=Os?i.useLayoutEffect:i.useEffect,Ns=(0,i.createContext)({strict:!1}),Ls=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),Ds="data-"+Ls("framerAppearId");function Fs(e){return e&&"object"===typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function Ms(e){return"string"===typeof e||Array.isArray(e)}function Is(e){return null!==e&&"object"===typeof e&&"function"===typeof e.start}const Us=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Bs=["initial",...Us];function Vs(e){return Is(e.animate)||Bs.some((t=>Ms(e[t])))}function zs(e){return Boolean(Vs(e)||e.variants)}function qs(e){const{initial:t,animate:n}=function(e,t){if(Vs(e)){const{initial:t,animate:n}=e;return{initial:!1===t||Ms(t)?t:void 0,animate:Ms(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,i.useContext)(Rs));return(0,i.useMemo)((()=>({initial:t,animate:n})),[Qs(t),Qs(n)])}function Qs(e){return Array.isArray(e)?e.join(" "):e}const Hs={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},$s={};for(const n in Hs)$s[n]={isEnabled:e=>Hs[n].some((t=>!!e[t]))};const Ws=(0,i.createContext)({}),Ks=(0,i.createContext)({}),Ys=Symbol.for("motionComponentSymbol");function Gs(e){let{preloadedFeatures:t,createVisualElement:n,useRender:r,useVisualState:o,Component:s}=e;t&&function(e){for(const t in e)$s[t]=Oe(Oe({},$s[t]),e[t])}(t);const a=(0,i.forwardRef)((function(e,a){let l;const u=Oe(Oe(Oe({},(0,i.useContext)(Ts)),e),{},{layoutId:Xs(e)}),{isStatic:c}=u,d=qs(e),f=o(e,c);if(!c&&Os){d.visualElement=function(e,t,n,r){const{visualElement:o}=(0,i.useContext)(Rs),s=(0,i.useContext)(Ns),a=(0,i.useContext)(_s),l=(0,i.useContext)(Ts).reducedMotion,u=(0,i.useRef)();r=r||s.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:o,props:n,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));const c=u.current;(0,i.useInsertionEffect)((()=>{c&&c.update(n,a)}));const d=(0,i.useRef)(Boolean(n[Ds]&&!window.HandoffComplete));return js((()=>{c&&(c.render(),d.current&&c.animationState&&c.animationState.animateChanges())})),(0,i.useEffect)((()=>{c&&(c.updateFeatures(),!d.current&&c.animationState&&c.animationState.animateChanges(),d.current&&(d.current=!1,window.HandoffComplete=!0))})),c}(s,f,u,n);const e=(0,i.useContext)(Ks),r=(0,i.useContext)(Ns).strict;d.visualElement&&(l=d.visualElement.loadFeatures(u,r,t,e))}return i.createElement(Rs.Provider,{value:d},l&&d.visualElement?i.createElement(l,Oe({visualElement:d.visualElement},u)):null,r(s,e,function(e,t,n){return(0,i.useCallback)((r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&("function"===typeof n?n(r):Fs(n)&&(n.current=r))}),[t])}(f,d.visualElement,a),f,c,d.visualElement))}));return a[Ys]=s,a}function Xs(e){let{layoutId:t}=e;const n=(0,i.useContext)(Ws).id;return n&&void 0!==t?n+"-"+t:t}function Js(e){function t(t){return Gs(e(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}))}if("undefined"===typeof Proxy)return t;const n=new Map;return new Proxy(t,{get:(e,r)=>(n.has(r)||n.set(r,t(r)),n.get(r))})}const Zs=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ea(e){return"string"===typeof e&&!e.includes("-")&&!!(Zs.indexOf(e)>-1||/[A-Z]/.test(e))}const ta={};const na=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ra=new Set(na);function ia(e,t){let{layout:n,layoutId:r}=t;return ra.has(e)||e.startsWith("origin")||(n||void 0!==r)&&(!!ta[e]||"opacity"===e)}const oa=e=>Boolean(e&&e.getVelocity),sa={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},aa=na.length;const la=e=>t=>"string"===typeof t&&t.startsWith(e),ua=la("--"),ca=la("var(--"),da=(e,t)=>t&&"number"===typeof e?t.transform(e):e,fa=(e,t,n)=>Math.min(Math.max(n,e),t),ha={test:e=>"number"===typeof e,parse:parseFloat,transform:e=>e},pa=Oe(Oe({},ha),{},{transform:e=>fa(0,1,e)}),ma=Oe(Oe({},ha),{},{default:1}),ya=e=>Math.round(1e5*e)/1e5,ga=/(-)?([\d]*\.?[\d])+/g,va=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,ba=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function wa(e){return"string"===typeof e}const xa=e=>({test:t=>wa(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>"".concat(t).concat(e)}),Sa=xa("deg"),Ea=xa("%"),ka=xa("px"),Ca=xa("vh"),Pa=xa("vw"),Aa=Oe(Oe({},Ea),{},{parse:e=>Ea.parse(e)/100,transform:e=>Ea.transform(100*e)}),Ta=Oe(Oe({},ha),{},{transform:Math.round}),Ra={borderWidth:ka,borderTopWidth:ka,borderRightWidth:ka,borderBottomWidth:ka,borderLeftWidth:ka,borderRadius:ka,radius:ka,borderTopLeftRadius:ka,borderTopRightRadius:ka,borderBottomRightRadius:ka,borderBottomLeftRadius:ka,width:ka,maxWidth:ka,height:ka,maxHeight:ka,size:ka,top:ka,right:ka,bottom:ka,left:ka,padding:ka,paddingTop:ka,paddingRight:ka,paddingBottom:ka,paddingLeft:ka,margin:ka,marginTop:ka,marginRight:ka,marginBottom:ka,marginLeft:ka,rotate:Sa,rotateX:Sa,rotateY:Sa,rotateZ:Sa,scale:ma,scaleX:ma,scaleY:ma,scaleZ:ma,skew:Sa,skewX:Sa,skewY:Sa,distance:ka,translateX:ka,translateY:ka,translateZ:ka,x:ka,y:ka,z:ka,perspective:ka,transformPerspective:ka,opacity:pa,originX:Aa,originY:Aa,originZ:ka,zIndex:Ta,fillOpacity:pa,strokeOpacity:pa,numOctaves:Ta};function _a(e,t,n,r){const{style:i,vars:o,transform:s,transformOrigin:a}=e;let l=!1,u=!1,c=!0;for(const d in t){const e=t[d];if(ua(d)){o[d]=e;continue}const n=Ra[d],r=da(e,n);if(ra.has(d)){if(l=!0,s[d]=r,!c)continue;e!==(n.default||0)&&(c=!1)}else d.startsWith("origin")?(u=!0,a[d]=r):i[d]=r}if(t.transform||(l||r?i.transform=function(e,t,n,r){let{enableHardwareAcceleration:i=!0,allowTransformNone:o=!0}=t,s="";for(let a=0;a<aa;a++){const t=na[a];void 0!==e[t]&&(s+="".concat(sa[t]||t,"(").concat(e[t],") "))}return i&&!e.z&&(s+="translateZ(0)"),s=s.trim(),r?s=r(e,n?"":s):o&&n&&(s="none"),s}(e.transform,n,c,r):i.transform&&(i.transform="none")),u){const{originX:e="50%",originY:t="50%",originZ:n=0}=a;i.transformOrigin="".concat(e," ").concat(t," ").concat(n)}}const Oa=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ja(e,t,n){for(const r in t)oa(t[r])||ia(r,n)||(e[r]=t[r])}function Na(e,t,n){const r={};return ja(r,e.style||{},e),Object.assign(r,function(e,t,n){let{transformTemplate:r}=e;return(0,i.useMemo)((()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return _a(e,t,{enableHardwareAcceleration:!n},r),Object.assign({},e.vars,e.style)}),[t])}(e,t,n)),e.transformValues?e.transformValues(r):r}function La(e,t,n){const r={},i=Na(e,t,n);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===e.drag?"none":"pan-".concat("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const Da=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Fa(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Da.has(e)}let Ma=e=>!Fa(e);try{(Ia=require("@emotion/is-prop-valid").default)&&(Ma=e=>e.startsWith("on")?!Fa(e):Ia(e))}catch(Hm){}var Ia;function Ua(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Ba(e,t,n){return"string"===typeof e?e:ka.transform(t+n*e)}const Va={offset:"stroke-dashoffset",array:"stroke-dasharray"},za={offset:"strokeDashoffset",array:"strokeDasharray"};const qa=["attrX","attrY","attrScale","originX","originY","pathLength","pathSpacing","pathOffset"];function Qa(e,t,n,r,i){let{attrX:o,attrY:s,attrScale:a,originX:l,originY:u,pathLength:c,pathSpacing:d=1,pathOffset:f=0}=t;if(_a(e,Ua(t,qa),n,i),r)return void(e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox));e.attrs=e.style,e.style={};const{attrs:h,style:p,dimensions:m}=e;h.transform&&(m&&(p.transform=h.transform),delete h.transform),m&&(void 0!==l||void 0!==u||p.transform)&&(p.transformOrigin=function(e,t,n){const r=Ba(t,e.x,e.width),i=Ba(n,e.y,e.height);return"".concat(r," ").concat(i)}(m,void 0!==l?l:.5,void 0!==u?u:.5)),void 0!==o&&(h.x=o),void 0!==s&&(h.y=s),void 0!==a&&(h.scale=a),void 0!==c&&function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];e.pathLength=1;const o=i?Va:za;e[o.offset]=ka.transform(-r);const s=ka.transform(t),a=ka.transform(n);e[o.array]="".concat(s," ").concat(a)}(h,c,d,f,!1)}const Ha=()=>Oe(Oe({},{style:{},transform:{},transformOrigin:{},vars:{}}),{},{attrs:{}}),$a=e=>"string"===typeof e&&"svg"===e.toLowerCase();function Wa(e,t,n,r){const o=(0,i.useMemo)((()=>{const n=Ha();return Qa(n,t,{enableHardwareAcceleration:!1},$a(r),e.transformTemplate),Oe(Oe({},n.attrs),{},{style:Oe({},n.style)})}),[t]);if(e.style){const t={};ja(t,e.style,e),o.style=Oe(Oe({},t),o.style)}return o}function Ka(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(t,n,r,o,s)=>{let{latestValues:a}=o;const l=(ea(t)?Wa:La)(n,a,s,t),u=function(e,t,n){const r={};for(const i in e)"values"===i&&"object"===typeof e.values||(Ma(i)||!0===n&&Fa(i)||!t&&!Fa(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"===typeof t,e),c=Oe(Oe(Oe({},u),l),{},{ref:r}),{children:d}=n,f=(0,i.useMemo)((()=>oa(d)?d.get():d),[d]);return(0,i.createElement)(t,Oe(Oe({},c),{},{children:f}))}}function Ya(e,t,n,r){let{style:i,vars:o}=t;Object.assign(e.style,i,r&&r.getProjectionStyles(n));for(const s in o)e.style.setProperty(s,o[s])}const Ga=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Xa(e,t,n,r){Ya(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Ga.has(i)?i:Ls(i),t.attrs[i])}function Ja(e,t){const{style:n}=e,r={};for(const i in n)(oa(n[i])||t.style&&oa(t.style[i])||ia(i,e))&&(r[i]=n[i]);return r}function Za(e,t){const n=Ja(e,t);for(const r in e)if(oa(e[r])||oa(t[r])){n[-1!==na.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]}return n}function el(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};return"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,i)),"string"===typeof t&&(t=e.variants&&e.variants[t]),"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,i)),t}function tl(e){const t=(0,i.useRef)(null);return null===t.current&&(t.current=e()),t.current}const nl=e=>Array.isArray(e);function rl(e){const t=oa(e)?e.get():e;return n=t,Boolean(n&&"object"===typeof n&&n.mix&&n.toValue)?t.toValue():t;var n}const il=["transitionEnd","transition"];const ol=e=>(t,n)=>{const r=(0,i.useContext)(Rs),o=(0,i.useContext)(_s),s=()=>function(e,t,n,r){let{scrapeMotionValuesFromProps:i,createRenderState:o,onMount:s}=e;const a={latestValues:sl(t,n,r,i),renderState:o()};return s&&(a.mount=e=>s(t,e,a)),a}(e,t,r,o);return n?s():tl(s)};function sl(e,t,n,r){const i={},o=r(e,{});for(const f in o)i[f]=rl(o[f]);let{initial:s,animate:a}=e;const l=Vs(e),u=zs(e);t&&u&&!l&&!1!==e.inherit&&(void 0===s&&(s=t.initial),void 0===a&&(a=t.animate));let c=!!n&&!1===n.initial;c=c||!1===s;const d=c?a:s;if(d&&"boolean"!==typeof d&&!Is(d)){(Array.isArray(d)?d:[d]).forEach((t=>{const n=el(e,t);if(!n)return;const{transitionEnd:r,transition:o}=n,s=Ua(n,il);for(const e in s){let t=s[e];if(Array.isArray(t)){t=t[c?t.length-1:0]}null!==t&&(i[e]=t)}for(const e in r)i[e]=r[e]}))}return i}const al=e=>e;class ll{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){const t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}const ul=["prepare","read","update","preRender","render","postRender"];const{schedule:cl,cancel:dl,state:fl,steps:hl}=function(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=ul.reduce(((e,t)=>(e[t]=function(e){let t=new ll,n=new ll,r=0,i=!1,o=!1;const s=new WeakSet,a={schedule:function(e){const o=arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&i,a=o?t:n;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&s.add(e),a.add(e)&&o&&i&&(r=t.order.length),e},cancel:e=>{n.remove(e),s.delete(e)},process:l=>{if(i)o=!0;else{if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let n=0;n<r;n++){const r=t.order[n];r(l),s.has(r)&&(a.schedule(r),e())}i=!1,o&&(o=!1,a.process(l))}}};return a}((()=>n=!0)),e)),{}),s=e=>o[e].process(i),a=()=>{const o=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(o-i.timestamp,40),1),i.timestamp=o,i.isProcessing=!0,ul.forEach(s),i.isProcessing=!1,n&&t&&(r=!1,e(a))},l=ul.reduce(((t,s)=>{const l=o[s];return t[s]=function(t){let o=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return n||(n=!0,r=!0,i.isProcessing||e(a)),l.schedule(t,o,s)},t}),{});return{schedule:l,cancel:e=>ul.forEach((t=>o[t].cancel(e))),state:i,steps:o}}("undefined"!==typeof requestAnimationFrame?requestAnimationFrame:al,!0),pl={useVisualState:ol({scrapeMotionValuesFromProps:Za,createRenderState:Ha,onMount:(e,t,n)=>{let{renderState:r,latestValues:i}=n;cl.read((()=>{try{r.dimensions="function"===typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(je){r.dimensions={x:0,y:0,width:0,height:0}}})),cl.render((()=>{Qa(r,i,{enableHardwareAcceleration:!1},$a(t.tagName),e.transformTemplate),Xa(t,r)}))}})},ml={useVisualState:ol({scrapeMotionValuesFromProps:Ja,createRenderState:Oa})};function yl(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const gl=e=>"mouse"===e.pointerType?"number"!==typeof e.button||e.button<=0:!1!==e.isPrimary;function vl(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page";return{point:{x:e[t+"X"],y:e[t+"Y"]}}}function bl(e,t,n,r){return yl(e,t,(e=>t=>gl(t)&&e(t,vl(t)))(n),r)}const wl=(e,t)=>n=>t(e(n)),xl=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(wl)};function Sl(e){let t=null;return()=>{const n=()=>{t=null};return null===t&&(t=e,n)}}const El=Sl("dragHorizontal"),kl=Sl("dragVertical");function Cl(e){let t=!1;if("y"===e)t=kl();else if("x"===e)t=El();else{const e=El(),n=kl();e&&n?t=()=>{e(),n()}:(e&&e(),n&&n())}return t}function Pl(){const e=Cl(!0);return!e||(e(),!1)}class Al{constructor(e){this.isMounted=!1,this.node=e}update(){}}function Tl(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End");return bl(e.current,n,((n,i)=>{if("touch"===n.pointerType||Pl())return;const o=e.getProps();e.animationState&&o.whileHover&&e.animationState.setActive("whileHover",t),o[r]&&cl.update((()=>o[r](n,i)))}),{passive:!e.getProps()[r]})}const Rl=(e,t)=>!!t&&(e===t||Rl(e,t.parentElement));function _l(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,vl(n))}const Ol=["root"],jl=new WeakMap,Nl=new WeakMap,Ll=e=>{const t=jl.get(e.target);t&&t(e)},Dl=e=>{e.forEach(Ll)};function Fl(e,t,n){const r=function(e){let{root:t}=e,n=Ua(e,Ol);const r=t||document;Nl.has(r)||Nl.set(r,{});const i=Nl.get(r),o=JSON.stringify(n);return i[o]||(i[o]=new IntersectionObserver(Dl,Oe({root:t},n))),i[o]}(t);return jl.set(e,n),r.observe(e),()=>{jl.delete(e),r.unobserve(e)}}const Ml={some:0,all:1};const Il={inView:{Feature:class extends Al{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"===typeof r?r:Ml[r]};return Fl(this.node.current,o,(e=>{const{isIntersecting:t}=e;if(this.isInView===t)return;if(this.isInView=t,i&&!t&&this.hasEnteredView)return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);const{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)}))}mount(){this.startObserver()}update(){if("undefined"===typeof IntersectionObserver)return;const{props:e,prevProps:t}=this.node,n=["amount","margin","root"].some(function(e){let{viewport:t={}}=e,{viewport:n={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e=>t[e]!==n[e]}(e,t));n&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Al{constructor(){super(...arguments),this.removeStartListeners=al,this.removeEndListeners=al,this.removeAccessibleListeners=al,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();const n=this.node.getProps(),r=bl(window,"pointerup",((e,t)=>{if(!this.checkPressEnd())return;const{onTap:n,onTapCancel:r,globalTapTarget:i}=this.node.getProps();cl.update((()=>{i||Rl(this.node.current,e.target)?n&&n(e,t):r&&r(e,t)}))}),{passive:!(n.onTap||n.onPointerUp)}),i=bl(window,"pointercancel",((e,t)=>this.cancelPress(e,t)),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=xl(r,i),this.startPress(e,t)},this.startAccessiblePress=()=>{const e=yl(this.node.current,"keydown",(e=>{if("Enter"!==e.key||this.isPressing)return;this.removeEndListeners(),this.removeEndListeners=yl(this.node.current,"keyup",(e=>{"Enter"===e.key&&this.checkPressEnd()&&_l("up",((e,t)=>{const{onTap:n}=this.node.getProps();n&&cl.update((()=>n(e,t)))}))})),_l("down",((e,t)=>{this.startPress(e,t)}))})),t=yl(this.node.current,"blur",(()=>{this.isPressing&&_l("cancel",((e,t)=>this.cancelPress(e,t)))}));this.removeAccessibleListeners=xl(e,t)}}startPress(e,t){this.isPressing=!0;const{onTapStart:n,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&cl.update((()=>n(e,t)))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;return this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Pl()}cancelPress(e,t){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&cl.update((()=>n(e,t)))}mount(){const e=this.node.getProps(),t=bl(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),n=yl(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=xl(t,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}},focus:{Feature:class extends Al{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(je){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=xl(yl(this.node.current,"focus",(()=>this.onFocus())),yl(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Al{mount(){this.unmount=xl(Tl(this.node,!0),Tl(this.node,!1))}unmount(){}}}};function Ul(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Bl(e,t,n){const r=e.getProps();return el(r,t,void 0!==n?n:r.custom,function(e){const t={};return e.values.forEach(((e,n)=>t[n]=e.get())),t}(e),function(e){const t={};return e.values.forEach(((e,n)=>t[n]=e.getVelocity())),t}(e))}let Vl=al,zl=al;const ql=e=>1e3*e,Ql=e=>e/1e3,Hl=!1,$l=e=>Array.isArray(e)&&"number"===typeof e[0];function Wl(e){return Boolean(!e||"string"===typeof e&&Yl[e]||$l(e)||Array.isArray(e)&&e.every(Wl))}const Kl=e=>{let[t,n,r,i]=e;return"cubic-bezier(".concat(t,", ").concat(n,", ").concat(r,", ").concat(i,")")},Yl={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Kl([0,.65,.55,1]),circOut:Kl([.55,0,1,.45]),backIn:Kl([.31,.01,.66,-.59]),backOut:Kl([.33,1.53,.69,.99])};function Gl(e){if(e)return $l(e)?Kl(e):Array.isArray(e)?e.map(Gl):Yl[e]}const Xl=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function Jl(e,t,n,r){if(e===t&&n===r)return al;const i=t=>function(e,t,n,r,i){let o,s,a=0;do{s=t+(n-t)/2,o=Xl(s,r,i)-e,o>0?n=s:t=s}while(Math.abs(o)>1e-7&&++a<12);return s}(t,0,1,e,n);return e=>0===e||1===e?e:Xl(i(e),t,r)}const Zl=Jl(.42,0,1,1),eu=Jl(0,0,.58,1),tu=Jl(.42,0,.58,1),nu=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,ru=e=>t=>1-e(1-t),iu=e=>1-Math.sin(Math.acos(e)),ou=ru(iu),su=nu(iu),au=Jl(.33,1.53,.69,.99),lu=ru(au),uu=nu(lu),cu={linear:al,easeIn:Zl,easeInOut:tu,easeOut:eu,circIn:iu,circInOut:su,circOut:ou,backIn:lu,backInOut:uu,backOut:au,anticipate:e=>(e*=2)<1?.5*lu(e):.5*(2-Math.pow(2,-10*(e-1)))},du=e=>{if(Array.isArray(e)){zl(4===e.length,"Cubic bezier arrays must contain four numerical values.");const[t,n,r,i]=e;return Jl(t,n,r,i)}return"string"===typeof e?(zl(void 0!==cu[e],"Invalid easing type '".concat(e,"'")),cu[e]):e},fu=(e,t)=>n=>Boolean(wa(n)&&ba.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),hu=(e,t,n)=>r=>{if(!wa(r))return r;const[i,o,s,a]=r.match(ga);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:void 0!==a?parseFloat(a):1}},pu=Oe(Oe({},ha),{},{transform:e=>Math.round((e=>fa(0,255,e))(e))}),mu={test:fu("rgb","red"),parse:hu("red","green","blue"),transform:e=>{let{red:t,green:n,blue:r,alpha:i=1}=e;return"rgba("+pu.transform(t)+", "+pu.transform(n)+", "+pu.transform(r)+", "+ya(pa.transform(i))+")"}};const yu={test:fu("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:mu.transform},gu={test:fu("hsl","hue"),parse:hu("hue","saturation","lightness"),transform:e=>{let{hue:t,saturation:n,lightness:r,alpha:i=1}=e;return"hsla("+Math.round(t)+", "+Ea.transform(ya(n))+", "+Ea.transform(ya(r))+", "+ya(pa.transform(i))+")"}},vu={test:e=>mu.test(e)||yu.test(e)||gu.test(e),parse:e=>mu.test(e)?mu.parse(e):gu.test(e)?gu.parse(e):yu.parse(e),transform:e=>wa(e)?e:e.hasOwnProperty("red")?mu.transform(e):gu.transform(e)},bu=(e,t,n)=>-n*e+n*t+e;function wu(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}const xu=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},Su=[yu,mu,gu];function Eu(e){const t=(n=e,Su.find((e=>e.test(n))));var n;zl(Boolean(t),"'".concat(e,"' is not an animatable color. Use the equivalent color code instead."));let r=t.parse(e);return t===gu&&(r=function(e){let{hue:t,saturation:n,lightness:r,alpha:i}=e;t/=360,n/=100,r/=100;let o=0,s=0,a=0;if(n){const e=r<.5?r*(1+n):r+n-r*n,i=2*r-e;o=wu(i,e,t+1/3),s=wu(i,e,t),a=wu(i,e,t-1/3)}else o=s=a=r;return{red:Math.round(255*o),green:Math.round(255*s),blue:Math.round(255*a),alpha:i}}(r)),r}const ku=(e,t)=>{const n=Eu(e),r=Eu(t),i=Oe({},n);return e=>(i.red=xu(n.red,r.red,e),i.green=xu(n.green,r.green,e),i.blue=xu(n.blue,r.blue,e),i.alpha=bu(n.alpha,r.alpha,e),mu.transform(i))};const Cu={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:al},Pu={regex:va,countKey:"Colors",token:"${c}",parse:vu.parse},Au={regex:ga,countKey:"Numbers",token:"${n}",parse:ha.parse};function Tu(e,t){let{regex:n,countKey:r,token:i,parse:o}=t;const s=e.tokenised.match(n);s&&(e["num"+r]=s.length,e.tokenised=e.tokenised.replace(n,i),e.values.push(...s.map(o)))}function Ru(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Tu(n,Cu),Tu(n,Pu),Tu(n,Au),n}function _u(e){return Ru(e).values}function Ou(e){const{values:t,numColors:n,numVars:r,tokenised:i}=Ru(e),o=t.length;return e=>{let t=i;for(let i=0;i<o;i++)t=i<r?t.replace(Cu.token,e[i]):i<r+n?t.replace(Pu.token,vu.transform(e[i])):t.replace(Au.token,ya(e[i]));return t}}const ju=e=>"number"===typeof e?0:e;const Nu={test:function(e){var t,n;return isNaN(e)&&wa(e)&&((null===(t=e.match(ga))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(va))||void 0===n?void 0:n.length)||0)>0},parse:_u,createTransformer:Ou,getAnimatableNone:function(e){const t=_u(e);return Ou(e)(t.map(ju))}},Lu=(e,t)=>n=>"".concat(n>0?t:e);function Du(e,t){return"number"===typeof e?n=>bu(e,t,n):vu.test(e)?ku(e,t):e.startsWith("var(")?Lu(e,t):Iu(e,t)}const Fu=(e,t)=>{const n=[...e],r=n.length,i=e.map(((e,n)=>Du(e,t[n])));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}},Mu=(e,t)=>{const n=Oe(Oe({},e),t),r={};for(const i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=Du(e[i],t[i]));return e=>{for(const t in r)n[t]=r[t](e);return n}},Iu=(e,t)=>{const n=Nu.createTransformer(t),r=Ru(e),i=Ru(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?xl(Fu(r.values,i.values),n):(Vl(!0,"Complex values '".concat(e,"' and '").concat(t,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.")),Lu(e,t))},Uu=(e,t,n)=>{const r=t-e;return 0===r?1:(n-e)/r},Bu=(e,t)=>n=>bu(e,t,n);function Vu(e,t,n){const r=[],i=n||("number"===typeof(o=e[0])?Bu:"string"===typeof o?vu.test(o)?ku:Iu:Array.isArray(o)?Fu:"object"===typeof o?Mu:Bu);var o;const s=e.length-1;for(let a=0;a<s;a++){let n=i(e[a],e[a+1]);if(t){const e=Array.isArray(t)?t[a]||al:t;n=xl(e,n)}r.push(n)}return r}function zu(e,t){let{clamp:n=!0,ease:r,mixer:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const o=e.length;if(zl(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=Vu(t,r,i),a=s.length,l=t=>{let n=0;if(a>1)for(;n<e.length-2&&!(t<e[n+1]);n++);const r=Uu(e[n],e[n+1],t);return s[n](r)};return n?t=>l(fa(e[0],e[o-1],t)):l}function qu(e){const t=[0];return function(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Uu(0,t,r);e.push(bu(n,1,i))}}(t,e.length-1),t}function Qu(e){let{duration:t=300,keyframes:n,times:r,ease:i="easeInOut"}=e;const o=(e=>Array.isArray(e)&&"number"!==typeof e[0])(i)?i.map(du):du(i),s={done:!1,value:n[0]},a=function(e,t){return e.map((e=>e*t))}(r&&r.length===n.length?r:qu(n),t),l=zu(a,n,{ease:Array.isArray(o)?o:(u=n,c=o,u.map((()=>c||tu)).splice(0,u.length-1))});var u,c;return{calculatedDuration:t,next:e=>(s.value=l(e),s.done=e>=t,s)}}function Hu(e,t){return t?e*(1e3/t):0}function $u(e,t,n){const r=Math.max(t-5,0);return Hu(n-e(r),t-r)}const Wu=.001;function Ku(e){let t,n,{duration:r=800,bounce:i=.25,velocity:o=0,mass:s=1}=e;Vl(r<=ql(10),"Spring duration must be 10 seconds or less");let a=1-i;a=fa(.05,1,a),r=fa(.01,10,Ql(r)),a<1?(t=e=>{const t=e*a,n=t*r,i=t-o,s=Gu(e,a),l=Math.exp(-n);return Wu-i/s*l},n=e=>{const n=e*a*r,i=n*o+o,s=Math.pow(a,2)*Math.pow(e,2)*r,l=Math.exp(-n),u=Gu(Math.pow(e,2),a);return(-t(e)+Wu>0?-1:1)*((i-s)*l)/u}):(t=e=>Math.exp(-e*r)*((e-o)*r+1)-.001,n=e=>Math.exp(-e*r)*(r*r*(o-e)));const l=function(e,t,n){let r=n;for(let i=1;i<Yu;i++)r-=e(r)/t(r);return r}(t,n,5/r);if(r=ql(r),isNaN(l))return{stiffness:100,damping:10,duration:r};{const e=Math.pow(l,2)*s;return{stiffness:e,damping:2*a*Math.sqrt(s*e),duration:r}}}const Yu=12;function Gu(e,t){return e*Math.sqrt(1-t*t)}const Xu=["keyframes","restDelta","restSpeed"],Ju=["duration","bounce"],Zu=["stiffness","damping","mass"];function ec(e,t){return t.some((t=>void 0!==e[t]))}function tc(e){let{keyframes:t,restDelta:n,restSpeed:r}=e,i=Ua(e,Xu);const o=t[0],s=t[t.length-1],a={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:d,velocity:f,isResolvedFromDuration:h}=function(e){let t=Oe({velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1},e);if(!ec(e,Zu)&&ec(e,Ju)){const n=Ku(e);t=Oe(Oe(Oe({},t),n),{},{mass:1}),t.isResolvedFromDuration=!0}return t}(Oe(Oe({},i),{},{velocity:-Ql(i.velocity||0)})),p=f||0,m=u/(2*Math.sqrt(l*c)),y=s-o,g=Ql(Math.sqrt(l/c)),v=Math.abs(y)<5;let b;if(r||(r=v?.01:2),n||(n=v?.005:.5),m<1){const e=Gu(g,m);b=t=>{const n=Math.exp(-m*g*t);return s-n*((p+m*g*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}}else if(1===m)b=e=>s-Math.exp(-g*e)*(y+(p+g*y)*e);else{const e=g*Math.sqrt(m*m-1);b=t=>{const n=Math.exp(-m*g*t),r=Math.min(e*t,300);return s-n*((p+m*g*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}return{calculatedDuration:h&&d||null,next:e=>{const t=b(e);if(h)a.done=e>=d;else{let i=p;0!==e&&(i=m<1?$u(b,e,t):0);const o=Math.abs(i)<=r,l=Math.abs(s-t)<=n;a.done=o&&l}return a.value=a.done?s:t,a}}}function nc(e){let{keyframes:t,velocity:n=0,power:r=.8,timeConstant:i=325,bounceDamping:o=10,bounceStiffness:s=500,modifyTarget:a,min:l,max:u,restDelta:c=.5,restSpeed:d}=e;const f=t[0],h={done:!1,value:f},p=e=>void 0===l?u:void 0===u||Math.abs(l-e)<Math.abs(u-e)?l:u;let m=r*n;const y=f+m,g=void 0===a?y:a(y);g!==y&&(m=g-f);const v=e=>-m*Math.exp(-e/i),b=e=>g+v(e),w=e=>{const t=v(e),n=b(e);h.done=Math.abs(t)<=c,h.value=h.done?g:n};let x,S;const E=e=>{var t;(t=h.value,void 0!==l&&t<l||void 0!==u&&t>u)&&(x=e,S=tc({keyframes:[h.value,p(h.value)],velocity:$u(b,e,h.value),damping:o,stiffness:s,restDelta:c,restSpeed:d}))};return E(0),{calculatedDuration:null,next:e=>{let t=!1;return S||void 0!==x||(t=!0,w(e),E(e)),void 0!==x&&e>x?S.next(e-x):(!t&&w(e),h)}}}const rc=e=>{const t=t=>{let{timestamp:n}=t;return e(n)};return{start:()=>cl.update(t,!0),stop:()=>dl(t),now:()=>fl.isProcessing?fl.timestamp:performance.now()}};function ic(e){let t=0;let n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}const oc=["autoplay","delay","driver","keyframes","type","repeat","repeatDelay","repeatType","onPlay","onStop","onComplete","onUpdate"],sc={decay:nc,inertia:nc,tween:Qu,keyframes:Qu,spring:tc};function ac(e){let t,n,{autoplay:r=!0,delay:i=0,driver:o=rc,keyframes:s,type:a="keyframes",repeat:l=0,repeatDelay:u=0,repeatType:c="loop",onPlay:d,onStop:f,onComplete:h,onUpdate:p}=e,m=Ua(e,oc),y=1,g=!1;const v=()=>{n=new Promise((e=>{t=e}))};let b;v();const w=sc[a]||Qu;let x;w!==Qu&&"number"!==typeof s[0]&&(x=zu([0,100],s,{clamp:!1}),s=[0,100]);const S=w(Oe(Oe({},m),{},{keyframes:s}));let E;"mirror"===c&&(E=w(Oe(Oe({},m),{},{keyframes:[...s].reverse(),velocity:-(m.velocity||0)})));let k="idle",C=null,P=null,A=null;null===S.calculatedDuration&&l&&(S.calculatedDuration=ic(S));const{calculatedDuration:T}=S;let R=1/0,_=1/0;null!==T&&(R=T+u,_=R*(l+1)-u);let O=0;const j=e=>{if(null===P)return;y>0&&(P=Math.min(P,e)),y<0&&(P=Math.min(e-_/y,P)),O=null!==C?C:Math.round(e-P)*y;const t=O-i*(y>=0?1:-1),n=y>=0?t<0:t>_;O=Math.max(t,0),"finished"===k&&null===C&&(O=_);let r=O,o=S;if(l){const e=Math.min(O,_)/R;let t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,t=Math.min(t,l+1);Boolean(t%2)&&("reverse"===c?(n=1-n,u&&(n-=u/R)):"mirror"===c&&(o=E)),r=fa(0,1,n)*R}const a=n?{done:!1,value:s[0]}:o.next(r);x&&(a.value=x(a.value));let{done:d}=a;n||null===T||(d=y>=0?O>=_:O<=0);const f=null===C&&("finished"===k||"running"===k&&d);return p&&p(a.value),f&&D(),a},N=()=>{b&&b.stop(),b=void 0},L=()=>{k="idle",N(),t(),v(),P=A=null},D=()=>{k="finished",h&&h(),N(),t()},F=()=>{if(g)return;b||(b=o(j));const e=b.now();d&&d(),null!==C?P=e-C:P&&"finished"!==k||(P=e),"finished"===k&&v(),A=P,C=null,k="running",b.start()};r&&F();const M={then:(e,t)=>n.then(e,t),get time(){return Ql(O)},set time(e){e=ql(e),O=e,null===C&&b&&0!==y?P=b.now()-e/y:C=e},get duration(){const e=null===S.calculatedDuration?ic(S):S.calculatedDuration;return Ql(e)},get speed(){return y},set speed(e){e!==y&&b&&(y=e,M.time=Ql(O))},get state(){return k},play:F,pause:()=>{k="paused",C=O},stop:()=>{g=!0,"idle"!==k&&(k="idle",f&&f(),L())},cancel:()=>{null!==A&&j(A),L()},complete:()=>{k="finished"},sample:e=>(P=0,j(e))};return M}const lc=["onUpdate","onComplete"],uc=function(e){let t;return()=>(void 0===t&&(t=e()),t)}((()=>Object.hasOwnProperty.call(Element.prototype,"animate"))),cc=new Set(["opacity","clipPath","filter","transform","backgroundColor"]);function dc(e,t,n){let{onUpdate:r,onComplete:i}=n,o=Ua(n,lc);if(!(uc()&&cc.has(t)&&!o.repeatDelay&&"mirror"!==o.repeatType&&0!==o.damping&&"inertia"!==o.type))return!1;let s,a,l=!1,u=!1;const c=()=>{a=new Promise((e=>{s=e}))};c();let{keyframes:d,duration:f=300,ease:h,times:p}=o;if(((e,t)=>"spring"===t.type||"backgroundColor"===e||!Wl(t.ease))(t,o)){const e=ac(Oe(Oe({},o),{},{repeat:0,delay:0}));let t={done:!1,value:d[0]};const n=[];let r=0;for(;!t.done&&r<2e4;)t=e.sample(r),n.push(t.value),r+=10;p=void 0,d=n,f=r-10,h="linear"}const m=function(e,t,n){let{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:a,times:l}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const u={[t]:n};l&&(u.offset=l);const c=Gl(a);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===s?"alternate":"normal"})}(e.owner.current,t,d,Oe(Oe({},o),{},{duration:f,ease:h,times:p})),y=()=>{u=!1,m.cancel()},g=()=>{u=!0,cl.update(y),s(),c()};m.onfinish=()=>{u||(e.set(function(e,t){let{repeat:n,repeatType:r="loop"}=t;return e[n&&"loop"!==r&&n%2===1?0:e.length-1]}(d,o)),i&&i(),g())};return{then:(e,t)=>a.then(e,t),attachTimeline:e=>(m.timeline=e,m.onfinish=null,al),get time(){return Ql(m.currentTime||0)},set time(e){m.currentTime=ql(e)},get speed(){return m.playbackRate},set speed(e){m.playbackRate=e},get duration(){return Ql(f)},play:()=>{l||(m.play(),dl(y))},pause:()=>m.pause(),stop:()=>{if(l=!0,"idle"===m.playState)return;const{currentTime:t}=m;if(t){const n=ac(Oe(Oe({},o),{},{autoplay:!1}));e.setWithVelocity(n.sample(t-10).value,n.sample(t).value,10)}g()},complete:()=>{u||m.finish()},cancel:g}}const fc={type:"spring",stiffness:500,damping:25,restSpeed:10},hc={type:"keyframes",duration:.8},pc={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},mc=(e,t)=>{let{keyframes:n}=t;return n.length>2?hc:ra.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===n[1]?2*Math.sqrt(550):30,restSpeed:10}:fc:pc},yc=(e,t)=>"zIndex"!==e&&(!("number"!==typeof t&&!Array.isArray(t))||!("string"!==typeof t||!Nu.test(t)&&"0"!==t||t.startsWith("url("))),gc=new Set(["brightness","contrast","saturate","opacity"]);function vc(e){const[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;const[r]=n.match(ga)||[];if(!r)return e;const i=n.replace(r,"");let o=gc.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const bc=/([a-z-]*)\(.*?\)/g,wc=Oe(Oe({},Nu),{},{getAnimatableNone:e=>{const t=e.match(bc);return t?t.map(vc).join(" "):e}}),xc=Oe(Oe({},Ra),{},{color:vu,backgroundColor:vu,outlineColor:vu,fill:vu,stroke:vu,borderColor:vu,borderTopColor:vu,borderRightColor:vu,borderBottomColor:vu,borderLeftColor:vu,filter:wc,WebkitFilter:wc}),Sc=e=>xc[e];function Ec(e,t){let n=Sc(e);return n!==wc&&(n=Nu),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const kc=e=>/^0[^.\s]+$/.test(e);function Cc(e){return"number"===typeof e?0===e:null!==e?"none"===e||"0"===e||kc(e):void 0}const Pc=["when","delay","delayChildren","staggerChildren","staggerDirection","repeat","repeatType","repeatDelay","from","elapsed"];function Ac(e,t){return e[t]||e.default||e}const Tc=!1,Rc=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return i=>{const o=Ac(r,e)||{},s=o.delay||r.delay||0;let{elapsed:a=0}=r;a-=ql(s);const l=function(e,t,n,r){const i=yc(t,n);let o;o=Array.isArray(n)?[...n]:[null,n];const s=void 0!==r.from?r.from:e.get();let a;const l=[];for(let u=0;u<o.length;u++)null===o[u]&&(o[u]=0===u?s:o[u-1]),Cc(o[u])&&l.push(u),"string"===typeof o[u]&&"none"!==o[u]&&"0"!==o[u]&&(a=o[u]);if(i&&l.length&&a)for(let u=0;u<l.length;u++)o[l[u]]=Ec(t,a);return o}(t,e,n,o),u=l[0],c=l[l.length-1],d=yc(e,u),f=yc(e,c);Vl(d===f,"You are trying to animate ".concat(e,' from "').concat(u,'" to "').concat(c,'". ').concat(u," is not an animatable value - to enable this animation set ").concat(u," to a value animatable to ").concat(c," via the `style` property."));let h=Oe(Oe({keyframes:l,velocity:t.getVelocity(),ease:"easeOut"},o),{},{delay:-a,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}});if(function(e){let{when:t,delay:n,delayChildren:r,staggerChildren:i,staggerDirection:o,repeat:s,repeatType:a,repeatDelay:l,from:u,elapsed:c}=e,d=Ua(e,Pc);return!!Object.keys(d).length}(o)||(h=Oe(Oe({},h),mc(e,h))),h.duration&&(h.duration=ql(h.duration)),h.repeatDelay&&(h.repeatDelay=ql(h.repeatDelay)),!d||!f||Hl||!1===o.type||Tc)return function(e){let{keyframes:t,delay:n,onUpdate:r,onComplete:i}=e;const o=()=>(r&&r(t[t.length-1]),i&&i(),{time:0,speed:1,duration:0,play:al,pause:al,stop:al,then:e=>(e(),Promise.resolve()),cancel:al,complete:al});return n?ac({keyframes:[0,1],duration:0,delay:n,onComplete:o}):o()}(Hl?Oe(Oe({},h),{},{delay:0}):h);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const n=dc(t,e,h);if(n)return n}return ac(h)}};function _c(e){return Boolean(oa(e)&&e.add)}const Oc=e=>/^\-?\d*\.?\d+$/.test(e);function jc(e,t){-1===e.indexOf(t)&&e.push(t)}function Nc(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Lc{constructor(){this.subscriptions=[]}add(e){return jc(this.subscriptions,e),()=>Nc(this.subscriptions,e)}notify(e,t,n){const r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){const r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Dc={current:void 0};class Fc{constructor(e){var t=this;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=function(e){let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t.prev=t.current,t.current=e;const{delta:r,timestamp:i}=fl;t.lastUpdated!==i&&(t.timeDelta=r,t.lastUpdated=i,cl.postRender(t.scheduleVelocityCheck)),t.prev!==t.current&&t.events.change&&t.events.change.notify(t.current),t.events.velocityChange&&t.events.velocityChange.notify(t.getVelocity()),n&&t.events.renderRequest&&t.events.renderRequest.notify(t.current)},this.scheduleVelocityCheck=()=>cl.postRender(this.velocityCheck),this.velocityCheck=e=>{let{timestamp:t}=e;t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=(e=>!isNaN(parseFloat(e)))(this.current),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new Lc);const n=this.events[e].add(t);return"change"===e?()=>{n(),cl.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=e,this.timeDelta=n}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return Dc.current&&Dc.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Hu(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise((t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Mc(e,t){return new Fc(e,t)}const Ic=e=>t=>t.test(e),Uc=[ha,ka,Ea,Sa,Pa,Ca,{test:e=>"auto"===e,parse:e=>e}],Bc=e=>Uc.find(Ic(e)),Vc=[...Uc,vu,Nu],zc=["transitionEnd","transition"];function qc(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Mc(n))}function Qc(e,t){const n=Bl(e,t);let r=n?e.makeTargetAnimatable(n,!1):{},{transitionEnd:i={},transition:o={}}=r,s=Ua(r,zc);s=Oe(Oe({},s),i);for(const l in s){qc(e,l,(a=s[l],nl(a)?a[a.length-1]||0:a))}var a}function Hc(e,t){if(!t)return;return(t[e]||t.default||t).from}const $c=["transition","transitionEnd"];function Wc(e,t){let{protectedKeys:n,needsAnimating:r}=e;const i=n.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,i}function Kc(e,t){const n=e.get();if(!Array.isArray(t))return n!==t;for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}function Yc(e,t){let{delay:n=0,transitionOverride:r,type:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=e.makeTargetAnimatable(t),{transition:s=e.getDefaultTransition(),transitionEnd:a}=o,l=Ua(o,$c);const u=e.getValue("willChange");r&&(s=r);const c=[],d=i&&e.animationState&&e.animationState.getState()[i];for(const f in l){const t=e.getValue(f),r=l[f];if(!t||void 0===r||d&&Wc(d,f))continue;const i=Oe({delay:n,elapsed:0},Ac(s||{},f));if(window.HandoffAppearAnimations){const n=e.getProps()[Ds];if(n){const e=window.HandoffAppearAnimations(n,f,t,cl);null!==e&&(i.elapsed=e,i.isHandoff=!0)}}let o=!i.isHandoff&&!Kc(t,r);if("spring"===i.type&&(t.getVelocity()||i.velocity)&&(o=!1),t.animation&&(o=!1),o)continue;t.start(Rc(f,t,r,e.shouldReduceMotion&&ra.has(f)?{type:!1}:i));const a=t.animation;_c(u)&&(u.add(f),a.then((()=>u.remove(f)))),c.push(a)}return a&&Promise.all(c).then((()=>{a&&Qc(e,a)})),c}function Gc(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=Bl(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(Yc(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const{delayChildren:o=0,staggerChildren:s,staggerDirection:a}=i;return function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=arguments.length>5?arguments[5]:void 0;const s=[],a=(e.variantChildren.size-1)*r,l=1===i?function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r}:function(){return a-(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r};return Array.from(e.variantChildren).sort(Xc).forEach(((e,r)=>{e.notify("AnimationStart",t),s.push(Gc(e,t,Oe(Oe({},o),{},{delay:n+l(r)})).then((()=>e.notify("AnimationComplete",t))))})),Promise.all(s)}(e,t,o+r,s,a,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[e,t]="beforeChildren"===a?[o,s]:[s,o];return e().then((()=>t()))}return Promise.all([o(),s(n.delay)])}function Xc(e,t){return e.sortNodePosition(t)}const Jc=["transition","transitionEnd"],Zc=[...Us].reverse(),ed=Us.length;function td(e){return t=>Promise.all(t.map((t=>{let{animation:n,options:r}=t;return function(e,t){let n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t)){const i=t.map((t=>Gc(e,t,r)));n=Promise.all(i)}else if("string"===typeof t)n=Gc(e,t,r);else{const i="function"===typeof t?Bl(e,t,r.custom):t;n=Promise.all(Yc(e,i,r))}return n.then((()=>e.notify("AnimationComplete",t)))}(e,n,r)})))}function nd(e){let t=td(e);const n={animate:id(!0),whileInView:id(),whileHover:id(),whileTap:id(),whileDrag:id(),whileFocus:id(),exit:id()};let r=!0;const i=(t,n)=>{const r=Bl(e,n);if(r){const{transition:e,transitionEnd:n}=r,i=Ua(r,Jc);t=Oe(Oe(Oe({},t),i),n)}return t};function o(o,s){const a=e.getProps(),l=e.getVariantContext(!0)||{},u=[],c=new Set;let d={},f=1/0;for(let t=0;t<ed;t++){const h=Zc[t],p=n[h],m=void 0!==a[h]?a[h]:l[h],y=Ms(m),g=h===s?p.isActive:null;!1===g&&(f=t);let v=m===l[h]&&m!==a[h]&&y;if(v&&r&&e.manuallyAnimateOnMount&&(v=!1),p.protectedKeys=Oe({},d),!p.isActive&&null===g||!m&&!p.prevProp||Is(m)||"boolean"===typeof m)continue;let b=rd(p.prevProp,m)||h===s&&p.isActive&&!v&&y||t>f&&y,w=!1;const x=Array.isArray(m)?m:[m];let S=x.reduce(i,{});!1===g&&(S={});const{prevResolvedValues:E={}}=p,k=Oe(Oe({},E),S),C=e=>{b=!0,c.has(e)&&(w=!0,c.delete(e)),p.needsAnimating[e]=!0};for(const e in k){const t=S[e],n=E[e];if(d.hasOwnProperty(e))continue;let r=!1;r=nl(t)&&nl(n)?!Ul(t,n):t!==n,r?void 0!==t?C(e):c.add(e):void 0!==t&&c.has(e)?C(e):p.protectedKeys[e]=!0}p.prevProp=m,p.prevResolvedValues=S,p.isActive&&(d=Oe(Oe({},d),S)),r&&e.blockInitialAnimation&&(b=!1),!b||v&&!w||u.push(...x.map((e=>({animation:e,options:Oe({type:h},o)}))))}if(c.size){const t={};c.forEach((n=>{const r=e.getBaseTarget(n);void 0!==r&&(t[n]=r)})),u.push({animation:t})}let h=Boolean(u.length);return!r||!1!==a.initial&&a.initial!==a.animate||e.manuallyAnimateOnMount||(h=!1),r=!1,h?t(u):Promise.resolve()}return{animateChanges:o,setActive:function(t,r,i){var s;if(n[t].isActive===r)return Promise.resolve();null===(s=e.variantChildren)||void 0===s||s.forEach((e=>{var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,r)})),n[t].isActive=r;const a=o(i,t);for(const e in n)n[e].protectedKeys={};return a},setAnimateFunction:function(n){t=n(e)},getState:()=>n}}function rd(e,t){return"string"===typeof t?t!==e:!!Array.isArray(t)&&!Ul(t,e)}function id(){return{isActive:arguments.length>0&&void 0!==arguments[0]&&arguments[0],protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}let od=0;const sd={animation:{Feature:class extends Al{constructor(e){super(e),e.animationState||(e.animationState=nd(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),Is(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}},exit:{Feature:class extends Al{constructor(){super(...arguments),this.id=od++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:t,custom:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;const i=this.node.animationState.setActive("exit",!e,{custom:null!==n&&void 0!==n?n:this.node.getProps().custom});t&&!e&&i.then((()=>t(this.id)))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}}},ad=(e,t)=>Math.abs(e-t);class ld{constructor(e,t){let{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const e=dd(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){const n=ad(e.x,t.x),r=ad(e.y,t.y);return Math.sqrt(n**2+r**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;const{point:r}=e,{timestamp:i}=fl;this.history.push(Oe(Oe({},r),{},{timestamp:i}));const{onStart:o,onMove:s}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),s&&s(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=ud(t,this.transformPagePoint),cl.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();const{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=dd("pointercancel"===e.type?this.lastMoveEventInfo:ud(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!gl(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;const o=ud(vl(e),this.transformPagePoint),{point:s}=o,{timestamp:a}=fl;this.history=[Oe(Oe({},s),{},{timestamp:a})];const{onSessionStart:l}=t;l&&l(e,dd(o,this.history)),this.removeListeners=xl(bl(this.contextWindow,"pointermove",this.handlePointerMove),bl(this.contextWindow,"pointerup",this.handlePointerUp),bl(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),dl(this.updatePoint)}}function ud(e,t){return t?{point:t(e.point)}:e}function cd(e,t){return{x:e.x-t.x,y:e.y-t.y}}function dd(e,t){let{point:n}=e;return{point:n,delta:cd(n,hd(t)),offset:cd(n,fd(t)),velocity:pd(t,.1)}}function fd(e){return e[0]}function hd(e){return e[e.length-1]}function pd(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=hd(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>ql(t)));)n--;if(!r)return{x:0,y:0};const o=Ql(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function md(e){return e.max-e.min}function yd(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.01;return Math.abs(e-t)<=n}function gd(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=bu(t.min,t.max,e.origin),e.scale=md(n)/md(t),(yd(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=bu(n.min,n.max,e.origin)-e.originPoint,(yd(e.translate)||isNaN(e.translate))&&(e.translate=0)}function vd(e,t,n,r){gd(e.x,t.x,n.x,r?r.originX:void 0),gd(e.y,t.y,n.y,r?r.originY:void 0)}function bd(e,t,n){e.min=n.min+t.min,e.max=e.min+md(t)}function wd(e,t,n){e.min=t.min-n.min,e.max=e.min+md(t)}function xd(e,t,n){wd(e.x,t.x,n.x),wd(e.y,t.y,n.y)}function Sd(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function Ed(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}const kd=.35;function Cd(e,t,n){return{min:Pd(e,t),max:Pd(e,n)}}function Pd(e,t){return"number"===typeof e?e:e[t]||0}function Ad(e){return[e("x"),e("y")]}function Td(e){let{top:t,left:n,right:r,bottom:i}=e;return{x:{min:n,max:r},y:{min:t,max:i}}}function Rd(e){return void 0===e||1===e}function _d(e){let{scale:t,scaleX:n,scaleY:r}=e;return!Rd(t)||!Rd(n)||!Rd(r)}function Od(e){return _d(e)||jd(e)||e.z||e.rotate||e.rotateX||e.rotateY}function jd(e){return Nd(e.x)||Nd(e.y)}function Nd(e){return e&&"0%"!==e}function Ld(e,t,n){return n+t*(e-n)}function Dd(e,t,n,r,i){return void 0!==i&&(e=Ld(e,i,r)),Ld(e,n,r)+t}function Fd(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;e.min=Dd(e.min,t,n,r,i),e.max=Dd(e.max,t,n,r,i)}function Md(e,t){let{x:n,y:r}=t;Fd(e.x,n.translate,n.scale,n.originPoint),Fd(e.y,r.translate,r.scale,r.originPoint)}function Id(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Ud(e,t){e.min=e.min+t,e.max=e.max+t}function Bd(e,t,n){let[r,i,o]=n;const s=void 0!==t[o]?t[o]:.5,a=bu(e.min,e.max,s);Fd(e,t[r],t[i],a,t.scale)}const Vd=["x","scaleX","originX"],zd=["y","scaleY","originY"];function qd(e,t){Bd(e.x,t,Vd),Bd(e.y,t,zd)}function Qd(e,t){return Td(function(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}const Hd=e=>{let{current:t}=e;return t?t.ownerDocument.defaultView:null},$d=new WeakMap;class Wd{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=e}start(e){let{snapToCursor:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:r}=this.getProps();this.panSession=new ld(e,{onSessionStart:e=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(vl(e,"page").point)},onStart:(e,t)=>{const{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Cl(n),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ad((e=>{let t=this.getAxisMotionValue(e).get()||0;if(Ea.test(t)){const{projection:n}=this.visualElement;if(n&&n.layout){const r=n.layout.layoutBox[e];if(r){t=md(r)*(parseFloat(t)/100)}}}this.originPoint[e]=t})),i&&cl.update((()=>i(e,t)),!1,!0);const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{const{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openGlobalLock)return;const{offset:s}=t;if(r&&null===this.currentDirection)return this.currentDirection=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=null;Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x");return n}(s),void(null!==this.currentDirection&&i&&i(this.currentDirection));this.updateAxis("x",t.point,s),this.updateAxis("y",t.point,s),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>Ad((e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())}))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:Hd(this.visualElement)})}stop(e,t){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=t;this.startAnimation(r);const{onDragEnd:i}=this.getProps();i&&cl.update((()=>i(e,t)))}cancel(){this.isDragging=!1;const{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){const{drag:r}=this.getProps();if(!n||!Kd(e,r,this.currentDirection))return;const i=this.getAxisMotionValue(e);let o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,t,n){let{min:r,max:i}=t;return void 0!==r&&e<r?e=n?bu(r,e,n.min):Math.max(e,r):void 0!==i&&e>i&&(e=n?bu(i,e,n.max):Math.min(e,i)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){var e;const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&Fs(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!r)&&function(e,t){let{top:n,left:r,bottom:i,right:o}=t;return{x:Sd(e.x,r,o),y:Sd(e.y,n,i)}}(r.layoutBox,t),this.elastic=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:kd;return!1===e?e=0:!0===e&&(e=kd),{x:Cd(e,"left","right"),y:Cd(e,"top","bottom")}}(n),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Ad((e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){const n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(r.layoutBox[e],this.constraints[e]))}))}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!Fs(e))return!1;const n=e.current;zl(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");const{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const i=function(e,t,n){const r=Qd(e,n),{scroll:i}=t;return i&&(Ud(r.x,i.offset.x),Ud(r.y,i.offset.y)),r}(n,r.root,this.visualElement.getTransformPagePoint());let o=function(e,t){return{x:Ed(e.x,t.x),y:Ed(e.y,t.y)}}(r.layout.layoutBox,i);if(t){const e=t(function(e){let{x:t,y:n}=e;return{top:n.min,right:t.max,bottom:n.max,left:t.min}}(o));this.hasMutatedConstraints=!!e,e&&(o=Td(e))}return o}startAnimation(e){const{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:s}=this.getProps(),a=this.constraints||{},l=Ad((s=>{if(!Kd(s,t,this.currentDirection))return;let l=a&&a[s]||{};o&&(l={min:0,max:0});const u=r?200:1e6,c=r?40:1e7,d=Oe(Oe({type:"inertia",velocity:n?e[s]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10},i),l);return this.startAxisValueAnimation(s,d)}));return Promise.all(l).then(s)}startAxisValueAnimation(e,t){const n=this.getAxisMotionValue(e);return n.start(Rc(e,n,0,t))}stopAnimation(){Ad((e=>this.getAxisMotionValue(e).stop()))}pauseAnimation(){Ad((e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()}))}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){const t="_drag"+e.toUpperCase(),n=this.visualElement.getProps(),r=n[t];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){Ad((t=>{const{drag:n}=this.getProps();if(!Kd(t,n,this.currentDirection))return;const{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){const{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-bu(n,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!Fs(t)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};Ad((e=>{const t=this.getAxisMotionValue(e);if(t){const n=t.get();r[e]=function(e,t){let n=.5;const r=md(e),i=md(t);return i>r?n=Uu(t.min,t.max-r,e.min):r>i&&(n=Uu(e.min,e.max-i,t.min)),fa(0,1,n)}({min:n,max:n},this.constraints[e])}}));const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),Ad((t=>{if(!Kd(t,e,null))return;const n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(bu(i,o,r[t]))}))}addListeners(){if(!this.visualElement.current)return;$d.set(this.visualElement,this);const e=bl(this.visualElement.current,"pointerdown",(e=>{const{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)})),t=()=>{const{dragConstraints:e}=this.getProps();Fs(e)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),t();const i=yl(window,"resize",(()=>this.scalePositionWithinConstraints())),o=n.addEventListener("didUpdate",(e=>{let{delta:t,hasLayoutChanged:n}=e;this.isDragging&&n&&(Ad((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{i(),e(),r(),o&&o()}}getProps(){const e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=kd,dragMomentum:s=!0}=e;return Oe(Oe({},e),{},{drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:s})}}function Kd(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}const Yd=e=>(t,n)=>{e&&cl.update((()=>e(t,n)))};const Gd={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Xd(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const Jd={correct:(e,t)=>{if(!t.target)return e;if("string"===typeof e){if(!ka.test(e))return e;e=parseFloat(e)}const n=Xd(e,t.target.x),r=Xd(e,t.target.y);return"".concat(n,"% ").concat(r,"%")}},Zd={correct:(e,t)=>{let{treeScale:n,projectionDelta:r}=t;const i=e,o=Nu.parse(e);if(o.length>5)return i;const s=Nu.createTransformer(e),a="number"!==typeof o[0]?1:0,l=r.x.scale*n.x,u=r.y.scale*n.y;o[0+a]/=l,o[1+a]/=u;const c=bu(l,u,.5);return"number"===typeof o[2+a]&&(o[2+a]/=c),"number"===typeof o[3+a]&&(o[3+a]/=c),s(o)}};class ef extends i.Component{componentDidMount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;var o;o=nf,Object.assign(ta,o),i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",(()=>{this.safeToRemove()})),i.setOptions(Oe(Oe({},i.options),{},{onExitComplete:()=>this.safeToRemove()}))),Gd.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,o=n.projection;return o?(o.isPresent=i,r||e.layoutDependency!==t||void 0===t?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||cl.postRender((()=>{const e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask((()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function tf(e){const[t,n]=function(){const e=(0,i.useContext)(_s);if(null===e)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,o=(0,i.useId)();return(0,i.useEffect)((()=>r(o)),[]),!t&&n?[!1,()=>n&&n(o)]:[!0]}(),r=(0,i.useContext)(Ws);return i.createElement(ef,Oe(Oe({},e),{},{layoutGroup:r,switchLayoutGroup:(0,i.useContext)(Ks),isPresent:t,safeToRemove:n}))}const nf={borderRadius:Oe(Oe({},Jd),{},{applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]}),borderTopLeftRadius:Jd,borderTopRightRadius:Jd,borderBottomLeftRadius:Jd,borderBottomRightRadius:Jd,boxShadow:Zd},rf=["TopLeft","TopRight","BottomLeft","BottomRight"],of=rf.length,sf=e=>"string"===typeof e?parseFloat(e):e,af=e=>"number"===typeof e||ka.test(e);function lf(e,t){return void 0!==e[t]?e[t]:e.borderRadius}const uf=df(0,.5,ou),cf=df(.5,.95,al);function df(e,t,n){return r=>r<e?0:r>t?1:n(Uu(e,t,r))}function ff(e,t){e.min=t.min,e.max=t.max}function hf(e,t){ff(e.x,t.x),ff(e.y,t.y)}function pf(e,t,n,r,i){return e=Ld(e-=t,1/n,r),void 0!==i&&(e=Ld(e,1/i,r)),e}function mf(e,t,n,r,i){let[o,s,a]=n;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;Ea.test(t)&&(t=parseFloat(t),t=bu(s.min,s.max,t/100)-s.min);if("number"!==typeof t)return;let a=bu(o.min,o.max,r);e===o&&(a-=t),e.min=pf(e.min,t,n,a,i),e.max=pf(e.max,t,n,a,i)}(e,t[o],t[s],t[a],t.scale,r,i)}const yf=["x","scaleX","originX"],gf=["y","scaleY","originY"];function vf(e,t,n,r){mf(e.x,t,yf,n?n.x:void 0,r?r.x:void 0),mf(e.y,t,gf,n?n.y:void 0,r?r.y:void 0)}function bf(e){return 0===e.translate&&1===e.scale}function wf(e){return bf(e.x)&&bf(e.y)}function xf(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Sf(e){return md(e.x)/md(e.y)}class Ef{constructor(){this.members=[]}add(e){jc(this.members,e),e.scheduleRender()}remove(e){if(Nc(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){const t=this.members.findIndex((t=>e===t));if(0===t)return!1;let n;for(let r=t;r>=0;r--){const e=this.members[r];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(e,t){const n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach((e=>{const{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((e=>{e.instance&&e.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function kf(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r="translate3d(".concat(i,"px, ").concat(o,"px, 0) ")),1===t.x&&1===t.y||(r+="scale(".concat(1/t.x,", ").concat(1/t.y,") ")),n){const{rotate:e,rotateX:t,rotateY:i}=n;e&&(r+="rotate(".concat(e,"deg) ")),t&&(r+="rotateX(".concat(t,"deg) ")),i&&(r+="rotateY(".concat(i,"deg) "))}const s=e.x.scale*t.x,a=e.y.scale*t.y;return 1===s&&1===a||(r+="scale(".concat(s,", ").concat(a,")")),r||"none"}const Cf=(e,t)=>e.depth-t.depth;class Pf{constructor(){this.children=[],this.isDirty=!1}add(e){jc(this.children,e),this.isDirty=!0}remove(e){Nc(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Cf),this.isDirty=!1,this.children.forEach(e)}}const Af=["","X","Y","Z"],Tf={visibility:"hidden"};let Rf=0;const _f={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Of(e){let{attachResizeListener:t,defaultParent:n,measureScroll:r,checkIsScrollRoot:i,resetTransform:o}=e;return class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===n||void 0===n?void 0:n();this.id=Rf++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,_f.totalNodes=_f.resolvedTargetDeltas=_f.recalculatedProjection=0,this.nodes.forEach(Lf),this.nodes.forEach(Vf),this.nodes.forEach(zf),this.nodes.forEach(Df),function(e){window.MotionDebug&&window.MotionDebug.record(e)}(_f)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=t?t.root||t:this,this.path=t?[...t.path,t]:[],this.parent=t,this.depth=t?t.depth+1:0;for(let n=0;n<this.path.length;n++)this.path[n].shouldResetTransform=!0;this.root===this&&(this.nodes=new Pf)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new Lc),this.eventHandlers.get(e).add(t)}notifyListeners(e){const t=this.eventHandlers.get(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t&&t.notify(...r)}hasListeners(e){return this.eventHandlers.has(e)}mount(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.root.hasTreeAnimated;if(this.instance)return;var r;this.isSVG=(r=e)instanceof SVGElement&&"svg"!==r.tagName,this.instance=e;const{layoutId:i,layout:o,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(o||i)&&(this.isLayoutDirty=!0),t){let n;const r=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){const n=performance.now(),r=i=>{let{timestamp:o}=i;const s=o-n;s>=t&&(dl(r),e(s-t))};return cl.read(r,!0),()=>dl(r)}(r,250),Gd.hasAnimatedSinceResize&&(Gd.hasAnimatedSinceResize=!1,this.nodes.forEach(Bf))}))}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||o)&&this.addEventListener("didUpdate",(e=>{let{delta:t,hasLayoutChanged:n,hasRelativeTargetChanged:r,layout:i}=e;if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const o=this.options.transition||s.getDefaultTransition()||Kf,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=s.getProps(),u=!this.targetLayout||!xf(this.targetLayout,i)||r,c=!n&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||c||n&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,c);const e=Oe(Oe({},Ac(o,"layout")),{},{onPlay:a,onComplete:l});(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else n||Bf(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,dl(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(qf),this.animationId++)}getTransformTemplate(){const{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let i=0;i<this.path.length;i++){const e=this.path[i];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;const r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Mf);this.isUpdating||this.nodes.forEach(If),this.isUpdating=!1,this.nodes.forEach(Uf),this.nodes.forEach(jf),this.nodes.forEach(Nf),this.clearAllSnapshots();const e=performance.now();fl.delta=fa(0,1e3/60,e-fl.timestamp),fl.timestamp=e,fl.isProcessing=!0,hl.update.process(fl),hl.preRender.process(fl),hl.render.process(fl),fl.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask((()=>this.update())))}clearAllSnapshots(){this.nodes.forEach(Ff),this.sharedNodes.forEach(Qf)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,cl.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){cl.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),(!this.options.alwaysMeasureLayout||!this.isLead())&&!this.isLayoutDirty)return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const e=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:i(this.instance),offset:r(this.instance)})}resetTransform(){if(!o)return;const e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!wf(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,i=r!==this.prevTransformTemplateValue;e&&(t||Od(this.latestValues)||i)&&(o(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=this.measurePageBox();let n=this.removeElementScroll(t);var r;return e&&(n=this.removeTransform(n)),Xf((r=n).x),Xf(r.y),{animationId:this.root.animationId,measuredBox:t,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const t=e.measureViewportBox(),{scroll:n}=this.root;return n&&(Ud(t.x,n.offset.x),Ud(t.y,n.offset.y)),t}removeElementScroll(e){const t={x:{min:0,max:0},y:{min:0,max:0}};hf(t,e);for(let n=0;n<this.path.length;n++){const r=this.path[n],{scroll:i,options:o}=r;if(r!==this.root&&i&&o.layoutScroll){if(i.isRoot){hf(t,e);const{scroll:n}=this.root;n&&(Ud(t.x,-n.offset.x),Ud(t.y,-n.offset.y))}Ud(t.x,i.offset.x),Ud(t.y,i.offset.y)}}return t}applyTransform(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n={x:{min:0,max:0},y:{min:0,max:0}};hf(n,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];!t&&e.options.layoutScroll&&e.scroll&&e!==e.root&&qd(n,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),Od(e.latestValues)&&qd(n,e.latestValues)}return Od(this.latestValues)&&qd(n,this.latestValues),n}removeTransform(e){const t={x:{min:0,max:0},y:{min:0,max:0}};hf(t,e);for(let n=0;n<this.path.length;n++){const e=this.path[n];if(!e.instance)continue;if(!Od(e.latestValues))continue;_d(e.latestValues)&&e.updateSnapshot();const r={x:{min:0,max:0},y:{min:0,max:0}};hf(r,e.measurePageBox()),vf(t,e.latestValues,e.snapshot?e.snapshot.layoutBox:void 0,r)}return Od(this.latestValues)&&vf(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options=Oe(Oe(Oe({},this.options),e),{},{crossfade:void 0===e.crossfade||e.crossfade})}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==fl.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];var t;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const r=Boolean(this.resumingFrom)||this!==n;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;const{layout:i,layoutId:o}=this.options;if(this.layout&&(i||o)){if(this.resolvedRelativeTargetAt=fl.timestamp,!this.targetDelta&&!this.relativeTarget){const e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},xd(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),hf(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var s,a,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,l=this.relativeParent.target,bd(s.x,a.x,l.x),bd(s.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):hf(this.target,this.layout.layoutBox),Md(this.target,this.targetDelta)):hf(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},xd(this.relativeTargetOrigin,this.target,e.target),hf(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}_f.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!_d(this.parent.latestValues)&&!jd(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;const t=this.getLead(),n=Boolean(this.resumingFrom)||this!==t;let r=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===fl.timestamp&&(r=!1),r)return;const{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!o)return;hf(this.layoutCorrected,this.layout.layoutBox);const s=this.treeScale.x,a=this.treeScale.y;!function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const i=n.length;if(!i)return;let o,s;t.x=t.y=1;for(let a=0;a<i;a++){o=n[a],s=o.projectionDelta;const i=o.instance;i&&i.style&&"contents"===i.style.display||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&qd(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,Md(e,s)),r&&Od(o.latestValues)&&qd(e,o.latestValues))}t.x=Id(t.x),t.y=Id(t.y)}(this.layoutCorrected,this.treeScale,this.path,n),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox);const{target:l}=t;if(!l)return void(this.projectionTransform&&(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionTransform="none",this.scheduleRender()));this.projectionDelta||(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}});const u=this.projectionTransform;vd(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=kf(this.projectionDelta,this.treeScale),this.projectionTransform===u&&this.treeScale.x===s&&this.treeScale.y===a||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),_f.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.options.scheduleRender&&this.options.scheduleRender(),e){const e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.snapshot,r=n?n.latestValues:{},i=Oe({},this.latestValues),o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;const s={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(Wf));let d;this.animationProgress=0,this.mixTargetDelta=t=>{const n=t/1e3;Hf(o.x,e.x,n),Hf(o.y,e.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(xd(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),function(e,t,n,r){$f(e.x,t.x,n.x,r),$f(e.y,t.y,n.y,r)}(this.relativeTarget,this.relativeTargetOrigin,s,n),d&&function(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}(this.relativeTarget,d)&&(this.isProjectionDirty=!1),d||(d={x:{min:0,max:0},y:{min:0,max:0}}),hf(d,this.relativeTarget)),a&&(this.animationValues=i,function(e,t,n,r,i,o){i?(e.opacity=bu(0,void 0!==n.opacity?n.opacity:1,uf(r)),e.opacityExit=bu(void 0!==t.opacity?t.opacity:1,0,cf(r))):o&&(e.opacity=bu(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,r));for(let s=0;s<of;s++){const i="border".concat(rf[s],"Radius");let o=lf(t,i),a=lf(n,i);void 0===o&&void 0===a||(o||(o=0),a||(a=0),0===o||0===a||af(o)===af(a)?(e[i]=Math.max(bu(sf(o),sf(a),r),0),(Ea.test(a)||Ea.test(o))&&(e[i]+="%")):e[i]=a)}(t.rotate||n.rotate)&&(e.rotate=bu(t.rotate||0,n.rotate||0,r))}(i,r,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(dl(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=cl.update((()=>{Gd.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,n){const r=oa(e)?e:Mc(e);return r.start(Rc("",r,t,n)),r.animation}(0,1e3,Oe(Oe({},e),{},{onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}})),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const e=this.getLead();let{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&Jf(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const t=md(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;const r=md(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}hf(t,n),qd(t,i),vd(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new Ef);this.sharedNodes.get(e).add(t);const n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){const e=this.getStack();return!e||e.lead===this}getLead(){var e;const{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;const{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){const{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote(){let{needsReset:e,transition:t,preserveFollowOpacity:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){const e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){const{visualElement:e}=this.options;if(!e)return;let t=!1;const{latestValues:n}=e;if((n.rotate||n.rotateX||n.rotateY||n.rotateZ)&&(t=!0),!t)return;const r={};for(let i=0;i<Af.length;i++){const t="rotate"+Af[i];n[t]&&(r[t]=n[t],e.setStaticValue(t,0))}e.render();for(const i in r)e.setStaticValue(i,r[i]);e.scheduleRender()}getProjectionStyles(e){var t,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Tf;const r={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=rl(null===e||void 0===e?void 0:e.pointerEvents)||"",r.transform=i?i(this.latestValues,""):"none",r;const o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){const t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rl(null===e||void 0===e?void 0:e.pointerEvents)||""),this.hasProjected&&!Od(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}const s=o.animationValues||o.latestValues;this.applyTransformsToTarget(),r.transform=kf(this.projectionDeltaWithTransform,this.treeScale,s),i&&(r.transform=i(s,r.transform));const{x:a,y:l}=this.projectionDelta;r.transformOrigin="".concat(100*a.origin,"% ").concat(100*l.origin,"% 0"),o.animationValues?r.opacity=o===this?null!==(n=null!==(t=s.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:r.opacity=o===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0;for(const u in ta){if(void 0===s[u])continue;const{correct:e,applyTo:t}=ta[u],n="none"===r.transform?s[u]:e(s[u],o);if(t){const e=t.length;for(let i=0;i<e;i++)r[t[i]]=n}else r[u]=n}return this.options.layoutId&&(r.pointerEvents=o===this?rl(null===e||void 0===e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()})),this.root.nodes.forEach(Mf),this.root.sharedNodes.clear()}}}function jf(e){e.updateLayout()}function Nf(e){var t;const n=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:t,measuredBox:r}=e.layout,{animationType:i}=e.options,o=n.source!==e.layout.source;"size"===i?Ad((e=>{const r=o?n.measuredBox[e]:n.layoutBox[e],i=md(r);r.min=t[e].min,r.max=r.min+i})):Jf(i,n.layoutBox,t)&&Ad((r=>{const i=o?n.measuredBox[r]:n.layoutBox[r],s=md(t[r]);i.max=i.min+s,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+s)}));const s={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};vd(s,t,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?vd(a,e.applyTransform(r,!0),n.measuredBox):vd(a,t,n.layoutBox);const l=!wf(s);let u=!1;if(!e.resumeFrom){const r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){const{snapshot:i,layout:o}=r;if(i&&o){const s={x:{min:0,max:0},y:{min:0,max:0}};xd(s,n.layoutBox,i.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};xd(a,t,o.layoutBox),xf(s,a)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=s,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:a,layoutDelta:s,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){const{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function Lf(e){_f.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Df(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Ff(e){e.clearSnapshot()}function Mf(e){e.clearMeasurements()}function If(e){e.isLayoutDirty=!1}function Uf(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Bf(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Vf(e){e.resolveTargetDelta()}function zf(e){e.calcProjection()}function qf(e){e.resetRotation()}function Qf(e){e.removeLeadSnapshot()}function Hf(e,t,n){e.translate=bu(t.translate,0,n),e.scale=bu(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function $f(e,t,n,r){e.min=bu(t.min,n.min,r),e.max=bu(t.max,n.max,r)}function Wf(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}const Kf={duration:.45,ease:[.4,0,.1,1]},Yf=e=>"undefined"!==typeof navigator&&navigator.userAgent.toLowerCase().includes(e),Gf=Yf("applewebkit/")&&!Yf("chrome/")?Math.round:al;function Xf(e){e.min=Gf(e.min),e.max=Gf(e.max)}function Jf(e,t,n){return"position"===e||"preserve-aspect"===e&&!yd(Sf(t),Sf(n),.2)}const Zf=Of({attachResizeListener:(e,t)=>yl(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),eh={current:void 0},th=Of({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!eh.current){const e=new Zf({});e.mount(window),e.setOptions({layoutScroll:!0}),eh.current=e}return eh.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),nh={pan:{Feature:class extends Al{constructor(){super(...arguments),this.removePointerDownListener=al}onPointerDown(e){this.session=new ld(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Hd(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:Yd(e),onStart:Yd(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&cl.update((()=>r(e,t)))}}}mount(){this.removePointerDownListener=bl(this.node.current,"pointerdown",(e=>this.onPointerDown(e)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Al{constructor(e){super(e),this.removeGroupControls=al,this.removeListeners=al,this.controls=new Wd(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||al}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:th,MeasureLayout:tf}};const rh=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function ih(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;zl(n<=4,'Max CSS variable fallback depth detected in property "'.concat(e,'". This may indicate a circular fallback dependency.'));const[r,i]=function(e){const t=rh.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const e=o.trim();return Oc(e)?parseFloat(e):e}return ca(i)?ih(i,t,n+1):i}function oh(e,t,n){let r=Object.assign({},(function(e){if(null==e)throw new TypeError("Cannot destructure "+e)}(t),t));const i=e.current;if(!(i instanceof Element))return{target:r,transitionEnd:n};n&&(n=Oe({},n)),e.values.forEach((e=>{const t=e.get();if(!ca(t))return;const n=ih(t,i);n&&e.set(n)}));for(const o in r){const e=r[o];if(!ca(e))continue;const t=ih(e,i);t&&(r[o]=t,n||(n={}),void 0===n[o]&&(n[o]=e))}return{target:r,transitionEnd:n}}const sh=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),ah=e=>sh.has(e),lh=e=>e===ha||e===ka,uh=(e,t)=>parseFloat(e.split(", ")[t]),ch=(e,t)=>(n,r)=>{let{transform:i}=r;if("none"===i||!i)return 0;const o=i.match(/^matrix3d\((.+)\)$/);if(o)return uh(o[1],t);{const t=i.match(/^matrix\((.+)\)$/);return t?uh(t[1],e):0}},dh=new Set(["x","y","z"]),fh=na.filter((e=>!dh.has(e)));const hh={width:(e,t)=>{let{x:n}=e,{paddingLeft:r="0",paddingRight:i="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(i)},height:(e,t)=>{let{y:n}=e,{paddingTop:r="0",paddingBottom:i="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(i)},top:(e,t)=>{let{top:n}=t;return parseFloat(n)},left:(e,t)=>{let{left:n}=t;return parseFloat(n)},bottom:(e,t)=>{let{y:n}=e,{top:r}=t;return parseFloat(r)+(n.max-n.min)},right:(e,t)=>{let{x:n}=e,{left:r}=t;return parseFloat(r)+(n.max-n.min)},x:ch(4,13),y:ch(5,14)};hh.translateX=hh.x,hh.translateY=hh.y;const ph=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};t=Oe({},t),r=Oe({},r);const i=Object.keys(t).filter(ah);let o=[],s=!1;const a=[];if(i.forEach((i=>{const l=e.getValue(i);if(!e.hasValue(i))return;let u=n[i],c=Bc(u);const d=t[i];let f;if(nl(d)){const e=d.length,t=null===d[0]?1:0;u=d[t],c=Bc(u);for(let n=t;n<e&&null!==d[n];n++)f?zl(Bc(d[n])===f,"All keyframes must be of the same type"):(f=Bc(d[n]),zl(f===c||lh(c)&&lh(f),"Keyframes must be of the same dimension as the current value"))}else f=Bc(d);if(c!==f)if(lh(c)&&lh(f)){const e=l.get();"string"===typeof e&&l.set(parseFloat(e)),"string"===typeof d?t[i]=parseFloat(d):Array.isArray(d)&&f===ka&&(t[i]=d.map(parseFloat))}else(null===c||void 0===c?void 0:c.transform)&&(null===f||void 0===f?void 0:f.transform)&&(0===u||0===d)?0===u?l.set(f.transform(u)):t[i]=c.transform(d):(s||(o=function(e){const t=[];return fh.forEach((n=>{const r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))})),t.length&&e.render(),t}(e),s=!0),a.push(i),r[i]=void 0!==r[i]?r[i]:t[i],l.jump(d))})),a.length){const n=a.indexOf("height")>=0?window.pageYOffset:null,i=((e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,a={};"none"===s&&t.setStaticValue("display",e.display||"block"),n.forEach((e=>{a[e]=hh[e](r,o)})),t.render();const l=t.measureViewportBox();return n.forEach((n=>{const r=t.getValue(n);r&&r.jump(a[n]),e[n]=hh[n](l,o)})),e})(t,e,a);return o.length&&o.forEach((t=>{let[n,r]=t;e.getValue(n).set(r)})),e.render(),Os&&null!==n&&window.scrollTo({top:n}),{target:i,transitionEnd:r}}return{target:t,transitionEnd:r}};function mh(e,t,n,r){return(e=>Object.keys(e).some(ah))(t)?ph(e,t,n,r):{target:t,transitionEnd:r}}const yh={current:null},gh={current:!1};const vh=new WeakMap,bh=["willChange"],wh=["children"],xh=Object.keys($s),Sh=xh.length,Eh=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],kh=Bs.length;class Ch{constructor(e){let{parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o}=e,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>cl.render(this.render,!1,!0);const{latestValues:a,renderState:l}=o;this.latestValues=a,this.baseTarget=Oe({},a),this.initialValues=n.initial?Oe({},a):{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=Vs(n),this.isVariantNode=zs(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const u=this.scrapeMotionValuesFromProps(n,{}),{willChange:c}=u,d=Ua(u,bh);for(const f in d){const e=d[f];void 0!==a[f]&&oa(e)&&(e.set(a[f],!1),_c(c)&&c.add(f))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,vh.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((e,t)=>this.bindToMotionValue(t,e))),gh.current||function(){if(gh.current=!0,Os)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>yh.current=e.matches;e.addListener(t),t()}else yh.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||yh.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){vh.delete(this.current),this.projection&&this.projection.unmount(),dl(this.notifyUpdate),dl(this.render),this.valueSubscriptions.forEach((e=>e())),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){const n=ra.has(e),r=t.on("change",(t=>{this.latestValues[e]=t,this.props.onUpdate&&cl.update(this.notifyUpdate,!1,!0),n&&this.projection&&(this.projection.isTransformDirty=!0)})),i=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,(()=>{r(),i()}))}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures(e,t,n,r){let i,o,{children:s}=e,a=Ua(e,wh);for(let l=0;l<Sh;l++){const e=xh[l],{isEnabled:t,Feature:n,ProjectionNode:r,MeasureLayout:s}=$s[e];r&&(i=r),t(a)&&(!this.features[e]&&n&&(this.features[e]=new n(this)),s&&(o=s))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&i){this.projection=new i(this.latestValues,this.parent&&this.parent.projection);const{layoutId:e,layout:t,drag:n,dragConstraints:o,layoutScroll:s,layoutRoot:l}=a;this.projection.setOptions({layoutId:e,layout:t,alwaysMeasureLayout:Boolean(n)||o&&Fs(o),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"===typeof t?t:"both",initialPromotionConfig:r,layoutScroll:s,layoutRoot:l})}return o}updateFeatures(){for(const e in this.features){const t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let n=0;n<Eh.length;n++){const t=Eh[n];this.propEventSubscriptions[t]&&(this.propEventSubscriptions[t](),delete this.propEventSubscriptions[t]);const r=e["on"+t];r&&(this.propEventSubscriptions[t]=this.on(t,r))}this.prevMotionValues=function(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(oa(o))e.addValue(i,o),_c(r)&&r.add(i);else if(oa(s))e.addValue(i,Mc(o,{owner:e})),_c(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const t=e.getValue(i);!t.hasAnimated&&t.set(o)}else{const t=e.getStaticValue(i);e.addValue(i,Mc(void 0!==t?t:o,{owner:e}))}}for(const i in n)void 0===t[i]&&e.removeValue(i);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(){if(arguments.length>0&&void 0!==arguments[0]&&arguments[0])return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}const e={};for(let t=0;t<kh;t++){const n=Bs[t],r=this.props[n];(Ms(r)||!1===r)&&(e[n]=r)}return e}addVariantChild(e){const t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);const t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=Mc(t,{owner:this}),this.addValue(e,n)),n}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;const{initial:n}=this.props,r="string"===typeof n||"object"===typeof n?null===(t=el(this.props,n))||void 0===t?void 0:t[e]:void 0;if(n&&void 0!==r)return r;const i=this.getBaseTargetFromProps(this.props,e);return void 0===i||oa(i)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new Lc),this.events[e].add(t)}notify(e){if(this.events[e]){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this.events[e].notify(...n)}}}const Ph=["transition","transitionEnd"];class Ah extends Ch{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,t){let{vars:n,style:r}=t;delete n[e],delete r[e]}makeTargetAnimatableFromInstance(e,t,n){let{transition:r,transitionEnd:i}=e,o=Ua(e,Ph),{transformValues:s}=t,a=function(e,t,n){const r={};for(const i in e){const e=Hc(i,t);if(void 0!==e)r[i]=e;else{const e=n.getValue(i);e&&(r[i]=e.get())}}return r}(o,r||{},this);if(s&&(i&&(i=s(i)),o&&(o=s(o)),a&&(a=s(a))),n){!function(e,t,n){var r,i;const o=Object.keys(t).filter((t=>!e.hasValue(t))),s=o.length;var a;if(s)for(let l=0;l<s;l++){const s=o[l],u=t[s];let c=null;Array.isArray(u)&&(c=u[0]),null===c&&(c=null!==(i=null!==(r=n[s])&&void 0!==r?r:e.readValue(s))&&void 0!==i?i:t[s]),void 0!==c&&null!==c&&("string"===typeof c&&(Oc(c)||kc(c))?c=parseFloat(c):(a=c,!Vc.find(Ic(a))&&Nu.test(u)&&(c=Ec(s,u))),e.addValue(s,Mc(c,{owner:e})),void 0===n[s]&&(n[s]=c),null!==c&&e.setBaseTarget(s,c))}}(this,o,a);const e=((e,t,n,r)=>{const i=oh(e,t,r);return mh(e,t=i.target,n,r=i.transitionEnd)})(this,o,a,i);i=e.transitionEnd,o=e.target}return Oe({transition:r,transitionEnd:i},o)}}class Th extends Ah{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(ra.has(t)){const e=Sc(t);return e&&e.default||0}{const r=(n=e,window.getComputedStyle(n)),i=(ua(t)?r.getPropertyValue(t):r[t])||0;return"string"===typeof i?i.trim():i}var n}measureInstanceViewportBox(e,t){let{transformPagePoint:n}=t;return Qd(e,n)}build(e,t,n,r){_a(e,t,n,r.transformTemplate)}scrapeMotionValuesFromProps(e,t){return Ja(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;oa(e)&&(this.childSubscription=e.on("change",(e=>{this.current&&(this.current.textContent="".concat(e))})))}renderInstance(e,t,n,r){Ya(e,t,n,r)}}class Rh extends Ah{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(ra.has(t)){const e=Sc(t);return e&&e.default||0}return t=Ga.has(t)?t:Ls(t),e.getAttribute(t)}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}scrapeMotionValuesFromProps(e,t){return Za(e,t)}build(e,t,n,r){Qa(e,t,n,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,n,r){Xa(e,t,0,r)}mount(e){this.isSVGTag=$a(e.tagName),super.mount(e)}}const _h=(e,t)=>ea(e)?new Rh(t,{enableHardwareAcceleration:!1}):new Th(t,{enableHardwareAcceleration:!0}),Oh={layout:{ProjectionNode:th,MeasureLayout:tf}},jh=Oe(Oe(Oe(Oe({},sd),Il),nh),Oh),Nh=Js(((e,t)=>function(e,t,n,r){let{forwardMotionProps:i=!1}=t;return Oe(Oe({},ea(e)?pl:ml),{},{preloadedFeatures:n,useRender:Ka(i),createVisualElement:r,Component:e})}(e,t,jh,_h)));function Lh(){const e=(0,i.useRef)(!1);return js((()=>(e.current=!0,()=>{e.current=!1})),[]),e}class Dh extends i.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function Fh(e){let{children:t,isPresent:n}=e;const r=(0,i.useId)(),o=(0,i.useRef)(null),s=(0,i.useRef)({width:0,height:0,top:0,left:0});return(0,i.useInsertionEffect)((()=>{const{width:e,height:t,top:i,left:a}=s.current;if(n||!o.current||!e||!t)return;o.current.dataset.motionPopId=r;const l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule('\n          [data-motion-pop-id="'.concat(r,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(i,"px !important;\n            left: ").concat(a,"px !important;\n          }\n        ")),()=>{document.head.removeChild(l)}}),[n]),i.createElement(Dh,{isPresent:n,childRef:o,sizeRef:s},i.cloneElement(t,{ref:o}))}const Mh=e=>{let{children:t,initial:n,isPresent:r,onExitComplete:o,custom:s,presenceAffectsLayout:a,mode:l}=e;const u=tl(Ih),c=(0,i.useId)(),d=(0,i.useMemo)((()=>({id:c,initial:n,isPresent:r,custom:s,onExitComplete:e=>{u.set(e,!0);for(const t of u.values())if(!t)return;o&&o()},register:e=>(u.set(e,!1),()=>u.delete(e))})),a?void 0:[r]);return(0,i.useMemo)((()=>{u.forEach(((e,t)=>u.set(t,!1)))}),[r]),i.useEffect((()=>{!r&&!u.size&&o&&o()}),[r]),"popLayout"===l&&(t=i.createElement(Fh,{isPresent:r},t)),i.createElement(_s.Provider,{value:d},t)};function Ih(){return new Map}const Uh=e=>e.key||"";const Bh=e=>{let{children:t,custom:n,initial:r=!0,onExitComplete:o,exitBeforeEnter:s,presenceAffectsLayout:a=!0,mode:l="sync"}=e;zl(!s,"Replace exitBeforeEnter with mode='wait'");const u=(0,i.useContext)(Ws).forceRender||function(){const e=Lh(),[t,n]=(0,i.useState)(0),r=(0,i.useCallback)((()=>{e.current&&n(t+1)}),[t]);return[(0,i.useCallback)((()=>cl.postRender(r)),[r]),t]}()[0],c=Lh(),d=function(e){const t=[];return i.Children.forEach(e,(e=>{(0,i.isValidElement)(e)&&t.push(e)})),t}(t);let f=d;const h=(0,i.useRef)(new Map).current,p=(0,i.useRef)(f),m=(0,i.useRef)(new Map).current,y=(0,i.useRef)(!0);var g;if(js((()=>{y.current=!1,function(e,t){e.forEach((e=>{const n=Uh(e);t.set(n,e)}))}(d,m),p.current=f})),g=()=>{y.current=!0,m.clear(),h.clear()},(0,i.useEffect)((()=>()=>g()),[]),y.current)return i.createElement(i.Fragment,null,f.map((e=>i.createElement(Mh,{key:Uh(e),isPresent:!0,initial:!!r&&void 0,presenceAffectsLayout:a,mode:l},e))));f=[...f];const v=p.current.map(Uh),b=d.map(Uh),w=v.length;for(let i=0;i<w;i++){const e=v[i];-1!==b.indexOf(e)||h.has(e)||h.set(e,void 0)}return"wait"===l&&h.size&&(f=[]),h.forEach(((e,t)=>{if(-1!==b.indexOf(t))return;const r=m.get(t);if(!r)return;const s=v.indexOf(t);let y=e;if(!y){const e=()=>{h.delete(t);const e=Array.from(m.keys()).filter((e=>!b.includes(e)));if(e.forEach((e=>m.delete(e))),p.current=d.filter((n=>{const r=Uh(n);return r===t||e.includes(r)})),!h.size){if(!1===c.current)return;u(),o&&o()}};y=i.createElement(Mh,{key:Uh(r),isPresent:!1,onExitComplete:e,custom:n,presenceAffectsLayout:a,mode:l},r),h.set(t,y)}f.splice(s,0,y)})),f=f.map((e=>{const t=e.key;return h.has(t)?e:i.createElement(Mh,{key:Uh(e),isPresent:!0,presenceAffectsLayout:a,mode:l},e)})),i.createElement(i.Fragment,null,h.size?f:f.map((e=>(0,i.cloneElement)(e))))};var Vh=n(754),zh=n.n(Vh),qh=n(962),Qh=n.n(qh),Hh=n(567),$h=n.n(Hh),Wh=n(735),Kh=n.n(Wh),Yh=n(419),Gh=n.n(Yh);const Xh=()=>{const e=(()=>{const e=(0,i.useContext)(ki);if(!e)throw new Error("useAnalysisContext must be used within an AnalysisProvider");return e})();if(!e)throw new Error("useAnalysis must be used within an AnalysisProvider");const{analyses:t,currentAnalysis:n,loading:r,error:o,starting:s,pagination:a,fetchAnalyses:l,startAnalysis:u,getAnalysisStatus:c,getAnalysisResult:d,getAnalysisInsights:f,getAnalysisKPIs:h,getAnalysisRecommendations:p,generateAnalysisReport:m,rerunAnalysis:y,deleteAnalysis:g,setCurrentAnalysis:v,pollAnalysisStatus:b,clearAnalyses:w,getAnalysesByStatus:x,getRunningAnalyses:S,getCompletedAnalyses:E,getFailedAnalyses:k,hasRunningAnalyses:C}=e,[P,A]=(0,i.useState)(!0),T=(0,i.useCallback)((async function(e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const n={fileIds:e,analysisType:arguments.length>1&&void 0!==arguments[1]?arguments[1]:"comprehensive",settings:Oe({includeVisualization:!0,includeInsights:!0,includeRecommendations:!0},t.settings),agents:t.agents||["executor","expresser","reviewer"],priority:t.priority||"normal"},r=await u(n);return"pending"!==r.status&&"running"!==r.status||b(r.analysisId),r}),[u,b]),R=(0,i.useCallback)((async e=>{try{const[t,n,r,i,o]=await Promise.allSettled([c(e),d(e),f(e),h(e),p(e)]);return{status:"fulfilled"===t.status?t.value:null,result:"fulfilled"===n.status?n.value:null,insights:"fulfilled"===r.status?r.value:null,kpis:"fulfilled"===i.status?i.value:null,recommendations:"fulfilled"===o.status?o.value:null,errors:{status:"rejected"===t.status?t.reason:null,result:"rejected"===n.status?n.reason:null,insights:"rejected"===r.status?r.reason:null,kpis:"rejected"===i.status?i.reason:null,recommendations:"rejected"===o.status?o.reason:null}}}catch(o){throw console.error("Failed to get full analysis details:",o),o}}),[c,d,f,h,p]),_=(0,i.useCallback)((e=>t.find((t=>t.analysisId===e))),[t]),O=(0,i.useCallback)((e=>t.filter((t=>t.fileIds&&t.fileIds.includes(e)))),[t]),j=(0,i.useCallback)((()=>{const e=t.length,n=S().length,r=E().length,i=k().length;return{total:e,running:n,completed:r,failed:i,cancelled:t.filter((e=>"cancelled"===e.status)).length,successRate:e>0?r/e*100:0,failureRate:e>0?i/e*100:0,averageDuration:r>0?E().reduce(((e,t)=>e+(t.duration||0)),0)/r:0}}),[t,S,E,k]),N=(0,i.useCallback)((async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"pdf";try{await R(e);const n={includeCharts:!0,includeInsights:!0,includeKPIs:!0,includeRecommendations:!0,includeRawData:"json"===t,template:"comprehensive"};return await m(e,t,n)}catch(o){throw console.error("Failed to generate comprehensive report:",o),o}}),[R,m]),L=(0,i.useCallback)((async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"json";try{const n=await R(e),r={analysis:_(e),details:n,exportedAt:(new Date).toISOString(),format:t};if("json"===t){const t=new Blob([JSON.stringify(r,null,2)],{type:"application/json"}),n=window.URL.createObjectURL(t),i=document.createElement("a");return i.href=n,i.download="analysis-".concat(e,"-").concat(Date.now(),".json"),document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(n),!0}return await m(e,t)}catch(o){throw console.error("Failed to export analysis data:",o),o}}),[R,_,m]),D=(0,i.useCallback)((async e=>{const t=[];for(const n of e)try{await g(n),t.push({analysisId:n,success:!0})}catch(o){t.push({analysisId:n,success:!1,error:o})}return t}),[g]),F=(0,i.useCallback)((async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=[];for(const r of e)try{const e=await y(r,t);n.push({analysisId:r,success:!0,result:e}),"pending"!==e.status&&"running"!==e.status||b(r)}catch(o){n.push({analysisId:r,success:!1,error:o})}return n}),[y,b]),M=(0,i.useCallback)((async()=>{const e=S().map((e=>c(e.analysisId)));try{await Promise.allSettled(e)}catch(o){console.error("Failed to refresh running analyses:",o)}}),[S,c]);(0,i.useEffect)((()=>{if(!P)return;if(S().length>0){const e=setInterval(M,5e3);return()=>clearInterval(e)}}),[P,S,M]);const I=(0,i.useCallback)((e=>{let n=[...t];if(e.status&&(n=n.filter((t=>t.status===e.status))),e.type&&(n=n.filter((t=>t.analysisType===e.type))),e.dateFrom){const t=new Date(e.dateFrom);n=n.filter((e=>new Date(e.createdAt)>=t))}if(e.dateTo){const t=new Date(e.dateTo);n=n.filter((e=>new Date(e.createdAt)<=t))}if(e.search){const t=e.search.toLowerCase();n=n.filter((e=>{var n;return e.analysisId.toLowerCase().includes(t)||((null===(n=e.metadata)||void 0===n?void 0:n.filename)||"").toLowerCase().includes(t)}))}return n}),[t]);return{analyses:t,currentAnalysis:n,loading:r,error:o,starting:s,pagination:a,autoRefresh:P,fetchAnalyses:l,startAnalysis:u,startAnalysisWithFiles:T,getAnalysisStatus:c,getAnalysisResult:d,getAnalysisInsights:f,getAnalysisKPIs:h,getAnalysisRecommendations:p,generateAnalysisReport:m,generateComprehensiveReport:N,rerunAnalysis:y,deleteAnalysis:g,setCurrentAnalysis:v,clearAnalyses:w,getFullAnalysisDetails:R,exportAnalysisData:L,deleteMultipleAnalyses:D,rerunMultipleAnalyses:F,pollAnalysisStatus:b,refreshRunningAnalyses:M,getAnalysesByStatus:x,getRunningAnalyses:S,getCompletedAnalyses:E,getFailedAnalyses:k,getAnalysisById:_,getAnalysesByFileId:O,getAnalysisStats:j,filterAnalyses:I,setAutoRefresh:A,hasRunningAnalyses:C,totalAnalyses:t.length,hasAnalyses:t.length>0,canStartNewAnalysis:!s&&!C()}};var Jh=n(845);const Zh=()=>{const{sessionId:e}=re(),[t,n]=(0,i.useState)("upload"),[r,o]=(0,i.useState)(null),{uploadFile:s,startAnalysis:a,getAnalysisResults:l,uploadStatus:u,analysisStatus:c,isLoading:d}=Xh(),{agentStatuses:f,connectToAgentUpdates:h}=(0,Jh.useAgentStatus)();(0,i.useEffect)((()=>{e&&(n("analysis"),h(e))}),[e,h]);const p=async e=>{try{const t=await s(e);t.success&&(Ht.success("File uploaded successfully!"),n("analysis"),m(t.sessionId))}catch(t){Ht.error("Failed to upload file"),console.error(t)}},m=async t=>{try{(await a(t||e)).success&&Ht.success("Analysis started!")}catch(n){Ht.error("Failed to start analysis"),console.error(n)}},y=async()=>{try{const t=await l(e);o(t),n("results"),Ht.success("Analysis completed!")}catch(t){Ht.error("Failed to get results"),console.error(t)}};return(0,Ei.jsx)("div",{className:"dashboard",children:(0,Ei.jsx)("div",{className:"dashboard-container",children:(0,Ei.jsx)(Bh,{mode:"wait",children:(()=>{switch(t){case"upload":return(0,Ei.jsxs)(Nh.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"upload-step",children:[(0,Ei.jsxs)("div",{className:"hero-section",children:[(0,Ei.jsx)("h1",{children:"AI Data Analysis Platform"}),(0,Ei.jsx)("p",{children:"Upload your business data and get enterprise-level insights powered by DeepSeek AI"}),(0,Ei.jsxs)("div",{className:"features",children:[(0,Ei.jsxs)("div",{className:"feature",children:[(0,Ei.jsx)("span",{className:"icon",children:"\ud83e\udde0"}),(0,Ei.jsx)("span",{children:"DeepSeek AI Analysis"})]}),(0,Ei.jsxs)("div",{className:"feature",children:[(0,Ei.jsx)("span",{className:"icon",children:"\u26a1"}),(0,Ei.jsx)("span",{children:"Multi-Agent Processing"})]}),(0,Ei.jsxs)("div",{className:"feature",children:[(0,Ei.jsx)("span",{className:"icon",children:"\ud83d\udcca"}),(0,Ei.jsx)("span",{children:"Real-time Insights"})]}),(0,Ei.jsxs)("div",{className:"feature",children:[(0,Ei.jsx)("span",{className:"icon",children:"\ud83c\udfaf"}),(0,Ei.jsx)("span",{children:"Actionable Recommendations"})]})]})]}),(0,Ei.jsx)(zh(),{onFileUpload:p,isLoading:d})]},"upload");case"analysis":return(0,Ei.jsxs)(Nh.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"analysis-step",children:[(0,Ei.jsxs)("div",{className:"analysis-header",children:[(0,Ei.jsx)("h2",{children:"AI Agent Swarm Processing Your Data"}),(0,Ei.jsx)("p",{children:"Our multi-agent system is analyzing your business data using advanced AI"})]}),(0,Ei.jsx)(Qh(),{agentStatuses:f,analysisStatus:c,onComplete:y}),(0,Ei.jsxs)("div",{className:"deepseek-status",children:[(0,Ei.jsxs)("div",{className:"deepseek-header",children:[(0,Ei.jsx)("span",{className:"deepseek-icon",children:"\ud83e\udde0"}),(0,Ei.jsx)("h3",{children:"DeepSeek AI Processing"}),(0,Ei.jsx)("span",{className:"status-indicator active"})]}),(0,Ei.jsxs)("div",{className:"processing-info",children:[(0,Ei.jsx)("p",{children:"Advanced language model analyzing business patterns and generating insights..."}),(0,Ei.jsxs)("div",{className:"processing-details",children:[(0,Ei.jsx)("span",{children:"\u2022 PEER Pattern: Plan \u2192 Execute \u2192 Express \u2192 Review"}),(0,Ei.jsx)("span",{children:"\u2022 DOE Pattern: Data-fining \u2192 Opinion-inject \u2192 Express"}),(0,Ei.jsx)("span",{children:"\u2022 Business Intelligence Generation"})]})]})]})]},"analysis");case"results":return(0,Ei.jsxs)(Nh.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"results-step",children:[(0,Ei.jsxs)("div",{className:"results-header",children:[(0,Ei.jsx)("h2",{children:"Business Intelligence Report"}),(0,Ei.jsx)("p",{children:"AI-powered insights and recommendations for your business"})]}),r&&(0,Ei.jsxs)(Ei.Fragment,{children:[(0,Ei.jsx)($h(),{data:r}),(0,Ei.jsx)(Kh(),{data:r}),(0,Ei.jsx)(Gh(),{data:r})]}),(0,Ei.jsxs)("div",{className:"action-bar",children:[(0,Ei.jsx)("button",{className:"btn secondary",onClick:()=>{n("upload"),o(null)},children:"Analyze New File"}),(0,Ei.jsx)("button",{className:"btn primary",children:"Download Report"}),(0,Ei.jsx)("button",{className:"btn primary",children:"Schedule Updates"})]})]},"results");default:return null}})()})})})},ep=(0,i.createContext)(),tp=()=>{const e=(0,i.useContext)(ep);return e||{isLoading:!1,setLoading:()=>{}}},np=()=>{const{isLoading:e}=tp();return e?(0,Ei.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,Ei.jsxs)("div",{className:"bg-white rounded-lg p-8 max-w-sm w-full mx-4 text-center",children:[(0,Ei.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,Ei.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Processing..."}),(0,Ei.jsx)("p",{className:"text-sm text-gray-600",children:"Please wait while we analyze your data."})]})}):null},rp=()=>{const{sessionId:e}=re(),t=ne(),{socket:n,isConnected:r}=(()=>{const e=(0,i.useContext)(ks);if(!e)throw new Error("useSocket must be used within a SocketProvider");return e})(),{setLoading:o}=tp(),[s,a]=(0,i.useState)("initializing"),[l,u]=(0,i.useState)(0),[c,d]=(0,i.useState)(""),[f,h]=(0,i.useState)([]);(0,i.useEffect)((()=>{if(e)return o(!0),n&&r&&(n.on("analysis_progress",(e=>{u(e.progress),d(e.step),a(e.status)})),n.on("analysis_log",(e=>{h((t=>[...t,Oe(Oe({},e),{},{timestamp:new Date})]))})),n.on("analysis_complete",(n=>{o(!1),t("/results/".concat(e))})),n.on("analysis_error",(e=>{o(!1),a("error"),console.error("Analysis error:",e.error)})),n.emit("join_session",{sessionId:e})),()=>{n&&(n.off("analysis_progress"),n.off("analysis_log"),n.off("analysis_complete"),n.off("analysis_error")),o(!1)};t("/")}),[e,n,r,t,o]);return(0,Ei.jsx)("div",{className:"max-w-4xl mx-auto p-6",children:(0,Ei.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,Ei.jsxs)("div",{className:"mb-6",children:[(0,Ei.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Analysis in Progress"}),(0,Ei.jsxs)("p",{className:"text-gray-600",children:["Session ID: ",(0,Ei.jsx)("span",{className:"font-mono text-sm",children:e})]})]}),(0,Ei.jsxs)("div",{className:"mb-6",children:[(0,Ei.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,Ei.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Progress"}),(0,Ei.jsxs)("span",{className:"text-sm text-gray-500",children:[l,"%"]})]}),(0,Ei.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,Ei.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(l,"%")}})})]}),(0,Ei.jsx)("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:(0,Ei.jsxs)("div",{className:"flex items-center",children:[(0,Ei.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"}),(0,Ei.jsxs)("div",{children:[(0,Ei.jsx)("p",{className:"text-sm font-medium text-gray-900",children:"Current Step"}),(0,Ei.jsx)("p",{className:"text-sm ".concat((e=>{switch(e){case"running":return"text-blue-600";case"complete":return"text-green-600";case"error":return"text-red-600";default:return"text-gray-600"}})(s)),children:c||"Initializing analysis..."})]})]})}),(0,Ei.jsxs)("div",{className:"mb-6",children:[(0,Ei.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-3",children:"Analysis Logs"}),(0,Ei.jsx)("div",{className:"bg-gray-900 rounded-lg p-4 h-64 overflow-y-auto",children:0===f.length?(0,Ei.jsx)("p",{className:"text-gray-400 text-sm",children:"Waiting for analysis to start..."}):f.map(((e,t)=>(0,Ei.jsxs)("div",{className:"text-sm mb-1",children:[(0,Ei.jsx)("span",{className:"text-gray-400",children:e.timestamp.toLocaleTimeString()}),(0,Ei.jsx)("span",{className:"text-white ml-2",children:e.message})]},t)))})]}),(0,Ei.jsxs)("div",{className:"flex justify-between items-center",children:[(0,Ei.jsx)("button",{onClick:()=>t("/"),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"\u2190 Back to Dashboard"}),"error"===s&&(0,Ei.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Retry Analysis"})]})]})})},ip=()=>{var e,t,n;const{sessionId:r,component:o}=re(),s=ne(),[a,l]=(0,i.useState)(o||"summary"),{data:u,isLoading:c,error:d}=(0,Ce.useQuery)(["analysisResults",r],(async()=>(await vi.get("/api/analysis/results/".concat(r))).data),{enabled:!!r,refetchOnWindowFocus:!1});(0,i.useEffect)((()=>{r||s("/")}),[r,s]),(0,i.useEffect)((()=>{o&&o!==a&&l(o)}),[o,a]);return c?(0,Ei.jsx)("div",{className:"max-w-6xl mx-auto p-6",children:(0,Ei.jsxs)("div",{className:"animate-pulse",children:[(0,Ei.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4 mb-6"}),(0,Ei.jsx)("div",{className:"h-64 bg-gray-200 rounded mb-4"}),(0,Ei.jsx)("div",{className:"h-32 bg-gray-200 rounded"})]})}):d?(0,Ei.jsx)("div",{className:"max-w-6xl mx-auto p-6",children:(0,Ei.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,Ei.jsx)("h2",{className:"text-lg font-medium text-red-800 mb-2",children:"Error Loading Results"}),(0,Ei.jsx)("p",{className:"text-red-600",children:d.message||"Unable to load analysis results."}),(0,Ei.jsx)("button",{onClick:()=>s("/"),className:"mt-4 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",children:"Back to Dashboard"})]})}):(0,Ei.jsxs)("div",{className:"max-w-6xl mx-auto p-6",children:[(0,Ei.jsx)("div",{className:"mb-6",children:(0,Ei.jsxs)("div",{className:"flex justify-between items-start",children:[(0,Ei.jsxs)("div",{children:[(0,Ei.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Analysis Results"}),(0,Ei.jsxs)("p",{className:"text-gray-600",children:["Session: ",(0,Ei.jsx)("span",{className:"font-mono text-sm",children:r}),(null===u||void 0===u?void 0:u.createdAt)&&(0,Ei.jsxs)("span",{className:"ml-4",children:["\u2022 Generated ",new Date(u.createdAt).toLocaleString()]})]})]}),(0,Ei.jsxs)("div",{className:"flex space-x-3",children:[(0,Ei.jsx)("button",{onClick:()=>s("/"),className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"\u2190 Back to Dashboard"}),(0,Ei.jsx)("button",{onClick:()=>window.print(),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"Export PDF"})]})]})}),(0,Ei.jsx)("div",{className:"border-b border-gray-200 mb-6",children:(0,Ei.jsx)("nav",{className:"flex space-x-8",children:[{id:"summary",label:"Summary",icon:"\ud83d\udcca"},{id:"charts",label:"Visualizations",icon:"\ud83d\udcc8"},{id:"insights",label:"Insights",icon:"\ud83d\udca1"},{id:"recommendations",label:"Recommendations",icon:"\ud83c\udfaf"},{id:"data",label:"Raw Data",icon:"\ud83d\udccb"}].map((e=>(0,Ei.jsxs)("button",{onClick:()=>{return t=e.id,l(t),void s("/results/".concat(r,"/").concat(t));var t},className:"py-2 px-1 border-b-2 font-medium text-sm transition-colors ".concat(a===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,Ei.jsx)("span",{className:"mr-2",children:e.icon}),e.label]},e.id)))})}),(0,Ei.jsxs)("div",{className:"bg-white rounded-lg shadow",children:["summary"===a&&(0,Ei.jsxs)("div",{className:"p-6",children:[(0,Ei.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Analysis Summary"}),null!==u&&void 0!==u&&u.summary?(0,Ei.jsx)("div",{className:"prose max-w-none",children:(0,Ei.jsx)("p",{className:"text-gray-700 leading-relaxed",children:u.summary})}):(0,Ei.jsx)("p",{className:"text-gray-500",children:"No summary available."})]}),"charts"===a&&(0,Ei.jsxs)("div",{className:"p-6",children:[(0,Ei.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Data Visualizations"}),(0,Ei.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:(null===u||void 0===u||null===(e=u.charts)||void 0===e?void 0:e.length)>0?u.charts.map(((e,t)=>(0,Ei.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,Ei.jsx)("h3",{className:"font-medium mb-2",children:e.title}),(0,Ei.jsx)("div",{className:"h-64 bg-gray-100 rounded flex items-center justify-center",children:(0,Ei.jsxs)("span",{className:"text-gray-500",children:["Chart: ",e.type]})})]},t))):(0,Ei.jsx)("p",{className:"text-gray-500",children:"No visualizations available."})})]}),"insights"===a&&(0,Ei.jsxs)("div",{className:"p-6",children:[(0,Ei.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Key Insights"}),(null===u||void 0===u||null===(t=u.insights)||void 0===t?void 0:t.length)>0?(0,Ei.jsx)("div",{className:"space-y-4",children:u.insights.map(((e,t)=>(0,Ei.jsxs)("div",{className:"border-l-4 border-blue-500 pl-4",children:[(0,Ei.jsx)("h3",{className:"font-medium text-gray-900",children:e.title}),(0,Ei.jsx)("p",{className:"text-gray-700 mt-1",children:e.description})]},t)))}):(0,Ei.jsx)("p",{className:"text-gray-500",children:"No insights available."})]}),"recommendations"===a&&(0,Ei.jsxs)("div",{className:"p-6",children:[(0,Ei.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Recommendations"}),(null===u||void 0===u||null===(n=u.recommendations)||void 0===n?void 0:n.length)>0?(0,Ei.jsx)("div",{className:"space-y-4",children:u.recommendations.map(((e,t)=>(0,Ei.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,Ei.jsx)("h3",{className:"font-medium text-yellow-800",children:e.title}),(0,Ei.jsx)("p",{className:"text-yellow-700 mt-1",children:e.description}),e.priority&&(0,Ei.jsxs)("span",{className:"inline-block mt-2 px-2 py-1 text-xs rounded ".concat("high"===e.priority?"bg-red-100 text-red-800":"medium"===e.priority?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:[e.priority," priority"]})]},t)))}):(0,Ei.jsx)("p",{className:"text-gray-500",children:"No recommendations available."})]}),"data"===a&&(0,Ei.jsxs)("div",{className:"p-6",children:[(0,Ei.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Raw Data"}),null!==u&&void 0!==u&&u.data?(0,Ei.jsx)("div",{className:"bg-gray-50 rounded-lg p-4 overflow-auto",children:(0,Ei.jsx)("pre",{className:"text-sm text-gray-800",children:JSON.stringify(u.data,null,2)})}):(0,Ei.jsx)("p",{className:"text-gray-500",children:"No raw data available."})]})]})]})};class op extends i.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?(0,Ei.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,Ei.jsxs)("div",{className:"max-w-md w-full bg-white shadow-lg rounded-lg p-6",children:[(0,Ei.jsxs)("div",{className:"flex items-center mb-4",children:[(0,Ei.jsx)("div",{className:"flex-shrink-0",children:(0,Ei.jsx)("svg",{className:"h-8 w-8 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,Ei.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,Ei.jsx)("div",{className:"ml-3",children:(0,Ei.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Something went wrong"})})]}),(0,Ei.jsxs)("div",{className:"text-sm text-gray-600 mb-4",children:[(0,Ei.jsx)("p",{children:"We're sorry, but something unexpected happened. Please try refreshing the page."}),!1]}),(0,Ei.jsxs)("div",{className:"flex space-x-3",children:[(0,Ei.jsx)("button",{onClick:()=>window.location.reload(),className:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:"Refresh Page"}),(0,Ei.jsx)("button",{onClick:()=>window.location.href="/",className:"flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:"Go Home"})]})]})}):this.props.children}}const sp=op,ap=new Ce.QueryClient({defaultOptions:{queries:{refetchOnWindowFocus:!1,retry:(e,t)=>{var n,r;return!((null===t||void 0===t||null===(n=t.response)||void 0===n?void 0:n.status)>=400&&(null===t||void 0===t||null===(r=t.response)||void 0===r?void 0:r.status)<500)&&e<2},staleTime:3e5,cacheTime:6e5},mutations:{retry:1}}});const lp=function(){return(0,Ei.jsx)(sp,{children:(0,Ei.jsx)(Ce.QueryClientProvider,{client:ap,children:(0,Ei.jsx)(Cs,{children:(0,Ei.jsx)(Ui,{children:(0,Ei.jsx)(eo,{children:(0,Ei.jsx)(Se,{children:(0,Ei.jsxs)("div",{className:"App min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50",children:[(0,Ei.jsx)(As(),{}),(0,Ei.jsx)("main",{className:"main-content",children:(0,Ei.jsxs)(be,{children:[(0,Ei.jsx)(ge,{path:"/",element:(0,Ei.jsx)(Zh,{})}),(0,Ei.jsx)(ge,{path:"/analysis/:sessionId",element:(0,Ei.jsx)(rp,{})}),(0,Ei.jsx)(ge,{path:"/results/:sessionId",element:(0,Ei.jsx)(ip,{})}),(0,Ei.jsx)(ge,{path:"/results/:sessionId/:component",element:(0,Ei.jsx)(ip,{})}),(0,Ei.jsx)(ge,{path:"*",element:(0,Ei.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,Ei.jsxs)("div",{className:"text-center",children:[(0,Ei.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"404"}),(0,Ei.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"Page not found"}),(0,Ei.jsx)("a",{href:"/",className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors",children:"Go Home"})]})})})]})}),(0,Ei.jsx)(Qt,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff",borderRadius:"8px",fontSize:"14px",maxWidth:"500px"},success:{style:{background:"#059669"},iconTheme:{primary:"#fff",secondary:"#059669"}},error:{style:{background:"#DC2626"},iconTheme:{primary:"#fff",secondary:"#DC2626"},duration:6e3},loading:{style:{background:"#3B82F6"}}}}),(0,Ei.jsx)(np,{})]})})})})})})})};var up=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(je){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),this.tags=[],this.ctr=0},e}(),cp=Math.abs,dp=String.fromCharCode,fp=Object.assign;function hp(e){return e.trim()}function pp(e,t,n){return e.replace(t,n)}function mp(e,t){return e.indexOf(t)}function yp(e,t){return 0|e.charCodeAt(t)}function gp(e,t,n){return e.slice(t,n)}function vp(e){return e.length}function bp(e){return e.length}function wp(e,t){return t.push(e),e}var xp=1,Sp=1,Ep=0,kp=0,Cp=0,Pp="";function Ap(e,t,n,r,i,o,s){return{value:e,root:t,parent:n,type:r,props:i,children:o,line:xp,column:Sp,length:s,return:""}}function Tp(e,t){return fp(Ap("",null,null,"",null,null,0),e,{length:-e.length},t)}function Rp(){return Cp=kp>0?yp(Pp,--kp):0,Sp--,10===Cp&&(Sp=1,xp--),Cp}function _p(){return Cp=kp<Ep?yp(Pp,kp++):0,Sp++,10===Cp&&(Sp=1,xp++),Cp}function Op(){return yp(Pp,kp)}function jp(){return kp}function Np(e,t){return gp(Pp,e,t)}function Lp(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Dp(e){return xp=Sp=1,Ep=vp(Pp=e),kp=0,[]}function Fp(e){return Pp="",e}function Mp(e){return hp(Np(kp-1,Bp(91===e?e+2:40===e?e+1:e)))}function Ip(e){for(;(Cp=Op())&&Cp<33;)_p();return Lp(e)>2||Lp(Cp)>3?"":" "}function Up(e,t){for(;--t&&_p()&&!(Cp<48||Cp>102||Cp>57&&Cp<65||Cp>70&&Cp<97););return Np(e,jp()+(t<6&&32==Op()&&32==_p()))}function Bp(e){for(;_p();)switch(Cp){case e:return kp;case 34:case 39:34!==e&&39!==e&&Bp(Cp);break;case 40:41===e&&Bp(e);break;case 92:_p()}return kp}function Vp(e,t){for(;_p()&&e+Cp!==57&&(e+Cp!==84||47!==Op()););return"/*"+Np(t,kp-1)+"*"+dp(47===e?e:_p())}function zp(e){for(;!Lp(Op());)_p();return Np(e,kp)}var qp="-ms-",Qp="-moz-",Hp="-webkit-",$p="comm",Wp="rule",Kp="decl",Yp="@keyframes";function Gp(e,t){for(var n="",r=bp(e),i=0;i<r;i++)n+=t(e[i],i,e,t)||"";return n}function Xp(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case Kp:return e.return=e.return||e.value;case $p:return"";case Yp:return e.return=e.value+"{"+Gp(e.children,r)+"}";case Wp:e.value=e.props.join(",")}return vp(n=Gp(e.children,r))?e.return=e.value+"{"+n+"}":""}function Jp(e){return Fp(Zp("",null,null,null,[""],e=Dp(e),0,[0],e))}function Zp(e,t,n,r,i,o,s,a,l){for(var u=0,c=0,d=s,f=0,h=0,p=0,m=1,y=1,g=1,v=0,b="",w=i,x=o,S=r,E=b;y;)switch(p=v,v=_p()){case 40:if(108!=p&&58==yp(E,d-1)){-1!=mp(E+=pp(Mp(v),"&","&\f"),"&\f")&&(g=-1);break}case 34:case 39:case 91:E+=Mp(v);break;case 9:case 10:case 13:case 32:E+=Ip(p);break;case 92:E+=Up(jp()-1,7);continue;case 47:switch(Op()){case 42:case 47:wp(tm(Vp(_p(),jp()),t,n),l);break;default:E+="/"}break;case 123*m:a[u++]=vp(E)*g;case 125*m:case 59:case 0:switch(v){case 0:case 125:y=0;case 59+c:-1==g&&(E=pp(E,/\f/g,"")),h>0&&vp(E)-d&&wp(h>32?nm(E+";",r,n,d-1):nm(pp(E," ","")+";",r,n,d-2),l);break;case 59:E+=";";default:if(wp(S=em(E,t,n,u,c,i,a,b,w=[],x=[],d),o),123===v)if(0===c)Zp(E,t,S,S,w,o,d,a,x);else switch(99===f&&110===yp(E,3)?100:f){case 100:case 108:case 109:case 115:Zp(e,S,S,r&&wp(em(e,S,S,0,0,i,a,b,i,w=[],d),x),i,x,d,a,r?w:x);break;default:Zp(E,S,S,S,[""],x,0,a,x)}}u=c=h=0,m=g=1,b=E="",d=s;break;case 58:d=1+vp(E),h=p;default:if(m<1)if(123==v)--m;else if(125==v&&0==m++&&125==Rp())continue;switch(E+=dp(v),v*m){case 38:g=c>0?1:(E+="\f",-1);break;case 44:a[u++]=(vp(E)-1)*g,g=1;break;case 64:45===Op()&&(E+=Mp(_p())),f=Op(),c=d=vp(b=E+=zp(jp())),v++;break;case 45:45===p&&2==vp(E)&&(m=0)}}return o}function em(e,t,n,r,i,o,s,a,l,u,c){for(var d=i-1,f=0===i?o:[""],h=bp(f),p=0,m=0,y=0;p<r;++p)for(var g=0,v=gp(e,d+1,d=cp(m=s[p])),b=e;g<h;++g)(b=hp(m>0?f[g]+" "+v:pp(v,/&\f/g,f[g])))&&(l[y++]=b);return Ap(e,t,n,0===i?Wp:a,l,u,c)}function tm(e,t,n){return Ap(e,t,n,$p,dp(Cp),gp(e,2,-2),0)}function nm(e,t,n,r){return Ap(e,t,n,Kp,gp(e,0,r),gp(e,r+1,-1),r)}var rm=function(e,t,n){for(var r=0,i=0;r=i,i=Op(),38===r&&12===i&&(t[n]=1),!Lp(i);)_p();return Np(e,kp)},im=function(e,t){return Fp(function(e,t){var n=-1,r=44;do{switch(Lp(r)){case 0:38===r&&12===Op()&&(t[n]=1),e[n]+=rm(kp-1,t,n);break;case 2:e[n]+=Mp(r);break;case 4:if(44===r){e[++n]=58===Op()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=dp(r)}}while(r=_p());return e}(Dp(e),t))},om=new WeakMap,sm=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||om.get(n))&&!r){om.set(e,!0);for(var i=[],o=im(t,i),s=n.props,a=0,l=0;a<o.length;a++)for(var u=0;u<s.length;u++,l++)e.props[l]=i[a]?o[a].replace(/&\f/g,s[u]):s[u]+" "+o[a]}}},am=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function lm(e,t){switch(function(e,t){return 45^yp(e,0)?(((t<<2^yp(e,0))<<2^yp(e,1))<<2^yp(e,2))<<2^yp(e,3):0}(e,t)){case 5103:return Hp+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Hp+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return Hp+e+Qp+e+qp+e+e;case 6828:case 4268:return Hp+e+qp+e+e;case 6165:return Hp+e+qp+"flex-"+e+e;case 5187:return Hp+e+pp(e,/(\w+).+(:[^]+)/,Hp+"box-$1$2"+qp+"flex-$1$2")+e;case 5443:return Hp+e+qp+"flex-item-"+pp(e,/flex-|-self/,"")+e;case 4675:return Hp+e+qp+"flex-line-pack"+pp(e,/align-content|flex-|-self/,"")+e;case 5548:return Hp+e+qp+pp(e,"shrink","negative")+e;case 5292:return Hp+e+qp+pp(e,"basis","preferred-size")+e;case 6060:return Hp+"box-"+pp(e,"-grow","")+Hp+e+qp+pp(e,"grow","positive")+e;case 4554:return Hp+pp(e,/([^-])(transform)/g,"$1"+Hp+"$2")+e;case 6187:return pp(pp(pp(e,/(zoom-|grab)/,Hp+"$1"),/(image-set)/,Hp+"$1"),e,"")+e;case 5495:case 3959:return pp(e,/(image-set\([^]*)/,Hp+"$1$`$1");case 4968:return pp(pp(e,/(.+:)(flex-)?(.*)/,Hp+"box-pack:$3"+qp+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Hp+e+e;case 4095:case 3583:case 4068:case 2532:return pp(e,/(.+)-inline(.+)/,Hp+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(vp(e)-1-t>6)switch(yp(e,t+1)){case 109:if(45!==yp(e,t+4))break;case 102:return pp(e,/(.+:)(.+)-([^]+)/,"$1"+Hp+"$2-$3$1"+Qp+(108==yp(e,t+3)?"$3":"$2-$3"))+e;case 115:return~mp(e,"stretch")?lm(pp(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==yp(e,t+1))break;case 6444:switch(yp(e,vp(e)-3-(~mp(e,"!important")&&10))){case 107:return pp(e,":",":"+Hp)+e;case 101:return pp(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Hp+(45===yp(e,14)?"inline-":"")+"box$3$1"+Hp+"$2$3$1"+qp+"$2box$3")+e}break;case 5936:switch(yp(e,t+11)){case 114:return Hp+e+qp+pp(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return Hp+e+qp+pp(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return Hp+e+qp+pp(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return Hp+e+qp+e+e}return e}var um=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case Kp:e.return=lm(e.value,e.length);break;case Yp:return Gp([Tp(e,{value:pp(e.value,"@","@"+Hp)})],r);case Wp:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e,t){return(e=t.exec(e))?e[0]:e}(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Gp([Tp(e,{props:[pp(t,/:(read-\w+)/,":-moz-$1")]})],r);case"::placeholder":return Gp([Tp(e,{props:[pp(t,/:(plac\w+)/,":"+Hp+"input-$1")]}),Tp(e,{props:[pp(t,/:(plac\w+)/,":-moz-$1")]}),Tp(e,{props:[pp(t,/:(plac\w+)/,qp+"input-$1")]})],r)}return""}))}}],cm=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var r,i,o=e.stylisPlugins||um,s={},a=[];r=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)s[t[n]]=!0;a.push(e)}));var l,u,c=[Xp,(u=function(e){l.insert(e)},function(e){e.root||(e=e.return)&&u(e)})],d=function(e){var t=bp(e);return function(n,r,i,o){for(var s="",a=0;a<t;a++)s+=e[a](n,r,i,o)||"";return s}}([sm,am].concat(o,c));i=function(e,t,n,r){l=n,Gp(Jp(e?e+"{"+t.styles+"}":t.styles),d),r&&(f.inserted[t.name]=!0)};var f={key:t,sheet:new up({key:t,container:r,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:s,registered:{},insert:i};return f.sheet.hydrate(a),f};var dm=function(e,t,n){var r=e.key+"-"+t.name;!1===n&&void 0===e.registered[r]&&(e.registered[r]=t.styles)},fm=function(e,t,n){dm(e,t,n);var r=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var i=t;do{e.insert(t===i?"."+r:"",i,e.sheet,!0),i=i.next}while(void 0!==i)}};var hm={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function pm(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var mm=/[A-Z]|^ms/g,ym=/_EMO_([^_]+?)_([^]*?)_EMO_/g,gm=function(e){return 45===e.charCodeAt(1)},vm=function(e){return null!=e&&"boolean"!==typeof e},bm=pm((function(e){return gm(e)?e:e.replace(mm,"-$&").toLowerCase()})),wm=function(e,t){switch(e){case"animation":case"animationName":if("string"===typeof t)return t.replace(ym,(function(e,t,n){return Sm={name:t,styles:n,next:Sm},t}))}return 1===hm[e]||gm(e)||"number"!==typeof t||0===t?t:t+"px"};function xm(e,t,n){if(null==n)return"";var r=n;if(void 0!==r.__emotion_styles)return r;switch(typeof n){case"boolean":return"";case"object":var i=n;if(1===i.anim)return Sm={name:i.name,styles:i.styles,next:Sm},i.name;var o=n;if(void 0!==o.styles){var s=o.next;if(void 0!==s)for(;void 0!==s;)Sm={name:s.name,styles:s.styles,next:Sm},s=s.next;return o.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var i=0;i<n.length;i++)r+=xm(e,t,n[i])+";";else for(var o in n){var s=n[o];if("object"!==typeof s){var a=s;null!=t&&void 0!==t[a]?r+=o+"{"+t[a]+"}":vm(a)&&(r+=bm(o)+":"+wm(o,a)+";")}else if(!Array.isArray(s)||"string"!==typeof s[0]||null!=t&&void 0!==t[s[0]]){var l=xm(e,t,s);switch(o){case"animation":case"animationName":r+=bm(o)+":"+l+";";break;default:r+=o+"{"+l+"}"}}else for(var u=0;u<s.length;u++)vm(s[u])&&(r+=bm(o)+":"+wm(o,s[u])+";")}return r}(e,t,n);case"function":if(void 0!==e){var a=Sm,l=n(e);return Sm=a,xm(e,t,l)}}var u=n;if(null==t)return u;var c=t[u];return void 0!==c?c:u}var Sm,Em=/label:\s*([^\s;{]+)\s*(;|$)/g;function km(e,t,n){if(1===e.length&&"object"===typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,i="";Sm=void 0;var o=e[0];null==o||void 0===o.raw?(r=!1,i+=xm(n,t,o)):i+=o[0];for(var s=1;s<e.length;s++){if(i+=xm(n,t,e[s]),r)i+=o[s]}Em.lastIndex=0;for(var a,l="";null!==(a=Em.exec(i));)l+="-"+a[1];var u=function(e){for(var t,n=0,r=0,i=e.length;i>=4;++r,i-=4)t=***********(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=***********(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^***********(65535&n)+(59797*(n>>>16)<<16);switch(i){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=***********(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=***********(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(i)+l;return{name:u,styles:i,next:Sm}}var Cm=!!o.useInsertionEffect&&o.useInsertionEffect,Pm=Cm||function(e){return e()},Am=Cm||i.useLayoutEffect,Tm=i.createContext("undefined"!==typeof HTMLElement?cm({key:"css"}):null),Rm=(Tm.Provider,function(e){return(0,i.forwardRef)((function(t,n){var r=(0,i.useContext)(Tm);return e(t,r,n)}))}),_m=i.createContext({});var Om={}.hasOwnProperty,jm="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",Nm=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;return dm(t,n,r),Pm((function(){return fm(t,n,r)})),null},Lm=Rm((function(e,t,n){var r=e.css;"string"===typeof r&&void 0!==t.registered[r]&&(r=t.registered[r]);var o=e[jm],s=[r],a="";"string"===typeof e.className?a=function(e,t,n){var r="";return n.split(" ").forEach((function(n){void 0!==e[n]?t.push(e[n]+";"):n&&(r+=n+" ")})),r}(t.registered,s,e.className):null!=e.className&&(a=e.className+" ");var l=km(s,void 0,i.useContext(_m));a+=t.key+"-"+l.name;var u={};for(var c in e)Om.call(e,c)&&"css"!==c&&c!==jm&&(u[c]=e[c]);return u.className=a,n&&(u.ref=n),i.createElement(i.Fragment,null,i.createElement(Nm,{cache:t,serialized:l,isStringTag:"string"===typeof o}),i.createElement(o,u))})),Dm=Lm,Fm=(n(11),function(e,t){var n=arguments;if(null==t||!Om.call(t,"css"))return i.createElement.apply(void 0,n);var r=n.length,o=new Array(r);o[0]=Dm,o[1]=function(e,t){var n={};for(var r in t)Om.call(t,r)&&(n[r]=t[r]);return n[jm]=e,n}(e,t);for(var s=2;s<r;s++)o[s]=n[s];return i.createElement.apply(null,o)});!function(e){var t;t||(t=e.JSX||(e.JSX={}))}(Fm||(Fm={}));var Mm=Rm((function(e,t){var n=km([e.styles],void 0,i.useContext(_m)),r=i.useRef();return Am((function(){var e=t.key+"-global",i=new t.sheet.constructor({key:e,nonce:t.sheet.nonce,container:t.sheet.container,speedy:t.sheet.isSpeedy}),o=!1,s=document.querySelector('style[data-emotion="'+e+" "+n.name+'"]');return t.sheet.tags.length&&(i.before=t.sheet.tags[0]),null!==s&&(o=!0,s.setAttribute("data-emotion",e),i.hydrate([s])),r.current=[i,o],function(){i.flush()}}),[t]),Am((function(){var e=r.current,i=e[0];if(e[1])e[1]=!1;else{if(void 0!==n.next&&fm(t,n.next,!0),i.tags.length){var o=i.tags[i.tags.length-1].nextElementSibling;i.before=o,i.flush()}t.insert("",n,i,!1)}}),[t,n.name]),null}));const[Im,Um]=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{name:t,strict:n=!0,hookName:r="useContext",providerName:o="Provider",errorMessage:s,defaultValue:a}=e,l=(0,i.createContext)(a);return l.displayName=t,[l.Provider,function e(){const t=(0,i.useContext)(l);if(!t&&n){var a;const t=new Error(null!==s&&void 0!==s?s:(u=o,"".concat(r," returned `undefined`. Seems you forgot to wrap component within ").concat(u)));throw t.name="ContextError",null===(a=Error.captureStackTrace)||void 0===a||a.call(Error,t,e),t}var u;return t},l]}({name:"ChakraContext",strict:!0,providerName:"<ChakraProvider />"});function Bm(e){const{value:t,children:n}=e;return(0,Ei.jsxs)(Im,{value:t,children:[!t._config.disableLayers&&(0,Ei.jsx)(Mm,{styles:t.layers.atRule}),(0,Ei.jsx)(Mm,{styles:t._global}),n]})}var Vm=n(10),zm=n.n(Vm);class qm extends i.Component{constructor(e){super(e),this.state={hasError:!1,error:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Application Error:",e,t)}render(){return this.state.hasError?(0,Ei.jsxs)("div",{style:{padding:"20px",textAlign:"center",fontFamily:"Arial, sans-serif"},children:[(0,Ei.jsx)("h1",{children:"Something went wrong."}),(0,Ei.jsx)("p",{children:"We're sorry, but something went wrong. Please refresh the page."}),(0,Ei.jsx)("button",{onClick:()=>window.location.reload(),style:{padding:"10px 20px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},children:"Refresh Page"})]}):this.props.children}}s.createRoot(document.getElementById("root")).render((0,Ei.jsx)(i.StrictMode,{children:(0,Ei.jsx)(qm,{children:(0,Ei.jsx)(Bm,{theme:zm(),children:(0,Ei.jsx)(eo,{children:(0,Ei.jsx)(Ui,{children:(0,Ei.jsx)(lp,{})})})})})}))})()})();
//# sourceMappingURL=main.2e62d032.js.map