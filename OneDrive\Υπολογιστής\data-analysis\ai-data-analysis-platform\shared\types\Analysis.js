/**
 * Analysis-related type definitions and interfaces
 */

// Analysis types
export const ANALYSIS_TYPES = {
  BASIC: 'basic',
  COMPREHENSIVE: 'comprehensive',
  STATISTICAL: 'statistical',
  PREDICTIVE: 'predictive',
  EXPLORATORY: 'exploratory',
  CUSTOM: 'custom'
};

// Analysis status
export const ANALYSIS_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  PAUSED: 'paused'
};

// Analysis priority levels
export const ANALYSIS_PRIORITY = {
  LOW: 'low',
  NORMAL: 'normal',
  HIGH: 'high',
  URGENT: 'urgent'
};

// Data quality levels
export const DATA_QUALITY = {
  EXCELLENT: 'excellent',
  GOOD: 'good',
  FAIR: 'fair',
  POOR: 'poor',
  UNKNOWN: 'unknown'
};

// Insight types
export const INSIGHT_TYPES = {
  TREND: 'trend',
  ANOMALY: 'anomaly',
  CORRELATION: 'correlation',
  PATTERN: 'pattern',
  OUTLIER: 'outlier',
  SEASONAL: 'seasonal',
  RECOMMENDATION: 'recommendation'
};

// Analysis configuration
export class AnalysisConfig {
  constructor({
    type = ANALYSIS_TYPES.COMPREHENSIVE,
    priority = ANALYSIS_PRIORITY.NORMAL,
    fileIds = [],
    agents = ['executor', 'expresser', 'reviewer'],
    settings = {},
    timeout = 1800000, // 30 minutes default
    autoSave = true,
    notifications = true
  } = {}) {
    this.type = type;
    this.priority = priority;
    this.fileIds = fileIds;
    this.agents = agents;
    this.settings = {
      includeVisualization: true,
      includeInsights: true,
      includeRecommendations: true,
      includePredictions: false,
      confidenceThreshold: 0.8,
      maxDuration: timeout,
      ...settings
    };
    this.timeout = timeout;
    this.autoSave = autoSave;
    this.notifications = notifications;
    this.createdAt = new Date();
  }

  // Validate configuration
  validate() {
    const errors = [];

    if (!Object.values(ANALYSIS_TYPES).includes(this.type)) {
      errors.push('Invalid analysis type');
    }

    if (!this.fileIds || this.fileIds.length === 0) {
      errors.push('At least one file must be selected');
    }

    if (!this.agents || this.agents.length === 0) {
      errors.push('At least one agent must be selected');
    }

    if (this.timeout < 60000) {
      errors.push('Timeout must be at least 1 minute');
    }

    if (this.settings.confidenceThreshold < 0 || this.settings.confidenceThreshold > 1) {
      errors.push('Confidence threshold must be between 0 and 1');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get estimated duration
  getEstimatedDuration() {
    const baseTime = 60000; // 1 minute base
    const fileMultiplier = this.fileIds.length * 30000; // 30 seconds per file
    const agentMultiplier = this.agents.length * 45000; // 45 seconds per agent
    
    const typeMultipliers = {
      [ANALYSIS_TYPES.BASIC]: 1,
      [ANALYSIS_TYPES.COMPREHENSIVE]: 2,
      [ANALYSIS_TYPES.STATISTICAL]: 1.5,
      [ANALYSIS_TYPES.PREDICTIVE]: 3,
      [ANALYSIS_TYPES.EXPLORATORY]: 2.5,
      [ANALYSIS_TYPES.CUSTOM]: 2
    };

    return (baseTime + fileMultiplier + agentMultiplier) * (typeMultipliers[this.type] || 1);
  }
}

// Analysis result
export class AnalysisResult {
  constructor({
    analysisId,
    config,
    status = ANALYSIS_STATUS.PENDING,
    progress = 0,
    startTime = null,
    endTime = null,
    data = null,
    insights = [],
    kpis = {},
    visualizations = [],
    recommendations = [],
    metadata = {},
    error = null
  } = {}) {
    this.analysisId = analysisId;
    this.config = config;
    this.status = status;
    this.progress = progress;
    this.startTime = startTime;
    this.endTime = endTime;
    this.data = data;
    this.insights = insights;
    this.kpis = kpis;
    this.visualizations = visualizations;
    this.recommendations = recommendations;
    this.metadata = metadata;
    this.error = error;
    this.createdAt = new Date();
    this.updatedAt = new Date();
  }

  // Get analysis duration
  getDuration() {
    if (!this.startTime || !this.endTime) {
      return null;
    }
    return new Date(this.endTime) - new Date(this.startTime);
  }

  // Check if analysis is complete
  isComplete() {
    return [
      ANALYSIS_STATUS.COMPLETED,
      ANALYSIS_STATUS.FAILED,
      ANALYSIS_STATUS.CANCELLED
    ].includes(this.status);
  }

  // Check if analysis was successful
  isSuccessful() {
    return this.status === ANALYSIS_STATUS.COMPLETED && !this.error;
  }

  // Get data quality assessment
  getDataQuality() {
    if (!this.metadata.dataQuality) {
      return DATA_QUALITY.UNKNOWN;
    }
    return this.metadata.dataQuality;
  }

  // Add insight
  addInsight(insight) {
    this.insights.push(new AnalysisInsight(insight));
    this.updatedAt = new Date();
  }

  // Add KPI
  addKPI(key, value, metadata = {}) {
    this.kpis[key] = new KPIValue(value, metadata);
    this.updatedAt = new Date();
  }

  // Get insights by type
  getInsightsByType(type) {
    return this.insights.filter(insight => insight.type === type);
  }

  // Get formatted status
  getFormattedStatus() {
    const statusMap = {
      [ANALYSIS_STATUS.PENDING]: { label: 'Pending', color: 'yellow' },
      [ANALYSIS_STATUS.RUNNING]: { label: 'Running', color: 'blue' },
      [ANALYSIS_STATUS.COMPLETED]: { label: 'Completed', color: 'green' },
      [ANALYSIS_STATUS.FAILED]: { label: 'Failed', color: 'red' },
      [ANALYSIS_STATUS.CANCELLED]: { label: 'Cancelled', color: 'gray' },
      [ANALYSIS_STATUS.PAUSED]: { label: 'Paused', color: 'orange' }
    };

    return statusMap[this.status] || { label: this.status, color: 'gray' };
  }
}

// Analysis insight
export class AnalysisInsight {
  constructor({
    type,
    title,
    description,
    value = null,
    confidence = 0,
    importance = 0,
    metadata = {},
    relatedData = null
  } = {}) {
    this.type = type;
    this.title = title;
    this.description = description;
    this.value = value;
    this.confidence = confidence;
    this.importance = importance;
    this.metadata = metadata;
    this.relatedData = relatedData;
    this.createdAt = new Date();
  }

  // Get confidence level description
  getConfidenceLevel() {
    if (this.confidence >= 0.9) return 'Very High';
    if (this.confidence >= 0.8) return 'High';
    if (this.confidence >= 0.6) return 'Medium';
    if (this.confidence >= 0.4) return 'Low';
    return 'Very Low';
  }

  // Get importance level description
  getImportanceLevel() {
    if (this.importance >= 0.8) return 'Critical';
    if (this.importance >= 0.6) return 'High';
    if (this.importance >= 0.4) return 'Medium';
    if (this.importance >= 0.2) return 'Low';
    return 'Minimal';
  }

  // Check if insight is actionable
  isActionable() {
    return this.type === INSIGHT_TYPES.RECOMMENDATION || 
           this.type === INSIGHT_TYPES.ANOMALY ||
           this.importance >= 0.6;
  }
}

// KPI value
export class KPIValue {
  constructor(value, {
    unit = '',
    format = 'number',
    target = null,
    trend = null,
    change = null,
    period = null,
    benchmark = null
  } = {}) {
    this.value = value;
    this.unit = unit;
    this.format = format;
    this.target = target;
    this.trend = trend; // 'up', 'down', 'stable'
    this.change = change; // percentage change
    this.period = period;
    this.benchmark = benchmark;
    this.timestamp = new Date();
  }

  // Get formatted value
  getFormattedValue() {
    if (this.value === null || this.value === undefined) {
      return 'N/A';
    }

    switch (this.format) {
      case 'currency':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD'
        }).format(this.value);
      
      case 'percentage':
        return `${(this.value * 100).toFixed(1)}%`;
      
      case 'decimal':
        return this.value.toFixed(2);
      
      case 'integer':
        return Math.round(this.value).toLocaleString();
      
      default:
        return `${this.value}${this.unit ? ' ' + this.unit : ''}`;
    }
  }

  // Check if target is met
  isTargetMet() {
    if (this.target === null) return null;
    return this.value >= this.target;
  }

  // Get performance vs target
  getTargetPerformance() {
    if (this.target === null) return null;
    return ((this.value - this.target) / this.target) * 100;
  }
}

// Analysis recommendation
export class AnalysisRecommendation {
  constructor({
    title,
    description,
    priority = 'medium',
    category = 'general',
    impact = 'medium',
    effort = 'medium',
    timeline = 'short',
    confidence = 0.7,
    relatedInsights = [],
    actionItems = []
  } = {}) {
    this.title = title;
    this.description = description;
    this.priority = priority; // low, medium, high, critical
    this.category = category; // operational, strategic, tactical, technical
    this.impact = impact; // low, medium, high
    this.effort = effort; // low, medium, high
    this.timeline = timeline; // immediate, short, medium, long
    this.confidence = confidence;
    this.relatedInsights = relatedInsights;
    this.actionItems = actionItems;
    this.createdAt = new Date();
  }

  // Get priority score
  getPriorityScore() {
    const priorityScores = { low: 1, medium: 2, high: 3, critical: 4 };
    const impactScores = { low: 1, medium: 2, high: 3 };
    const effortScores = { low: 3, medium: 2, high: 1 };

    return (
      (priorityScores[this.priority] || 2) *
      (impactScores[this.impact] || 2) *
      (effortScores[this.effort] || 2) *
      this.confidence
    );
  }

  // Check if recommendation is quick win
  isQuickWin() {
    return this.impact === 'high' && this.effort === 'low';
  }
}

// Analysis step (for tracking progress)
export class AnalysisStep {
  constructor({
    id,
    name,
    description,
    agent,
    status = ANALYSIS_STATUS.PENDING,
    progress = 0,
    startTime = null,
    endTime = null,
    result = null,
    error = null
  } = {}) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.agent = agent;
    this.status = status;
    this.progress = progress;
    this.startTime = startTime;
    this.endTime = endTime;
    this.result = result;
    this.error = error;
    this.createdAt = new Date();
  }

  // Get step duration
  getDuration() {
    if (!this.startTime || !this.endTime) {
      return null;
    }
    return new Date(this.endTime) - new Date(this.startTime);
  }

  // Check if step is complete
  isComplete() {
    return [
      ANALYSIS_STATUS.COMPLETED,
      ANALYSIS_STATUS.FAILED,
      ANALYSIS_STATUS.CANCELLED
    ].includes(this.status);
  }
}

// Export default Analysis class
export default class Analysis {
  constructor(config) {
    this.config = config instanceof AnalysisConfig ? config : new AnalysisConfig(config);
    this.result = new AnalysisResult({
      config: this.config
    });
    this.steps = [];
    this.createdAt = new Date();
  }

  // Add analysis step
  addStep(step) {
    this.steps.push(step instanceof AnalysisStep ? step : new AnalysisStep(step));
  }

  // Get current step
  getCurrentStep() {
    return this.steps.find(step => step.status === ANALYSIS_STATUS.RUNNING) ||
           this.steps.find(step => step.status === ANALYSIS_STATUS.PENDING);
  }

  // Get overall progress
  getProgress() {
    if (this.steps.length === 0) return 0;
    
    const totalSteps = this.steps.length;
    const completedSteps = this.steps.filter(step => 
      step.status === ANALYSIS_STATUS.COMPLETED
    ).length;
    
    return (completedSteps / totalSteps) * 100;
  }

  // Update status
  updateStatus(status) {
    this.result.status = status;
    this.result.updatedAt = new Date();
  }

  // Get type info
  getTypeInfo() {
    const typeMap = {
      [ANALYSIS_TYPES.BASIC]: {
        name: 'Basic Analysis',
        description: 'Quick overview with essential metrics and insights'
      },
      [ANALYSIS_TYPES.COMPREHENSIVE]: {
        name: 'Comprehensive Analysis',
        description: 'In-depth analysis with detailed insights and recommendations'
      },
      [ANALYSIS_TYPES.STATISTICAL]: {
        name: 'Statistical Analysis',
        description: 'Advanced statistical analysis with hypothesis testing'
      },
      [ANALYSIS_TYPES.PREDICTIVE]: {
        name: 'Predictive Analysis',
        description: 'Forecasting and predictive modeling analysis'
      },
      [ANALYSIS_TYPES.EXPLORATORY]: {
        name: 'Exploratory Analysis',
        description: 'Data exploration to discover patterns and relationships'
      },
      [ANALYSIS_TYPES.CUSTOM]: {
        name: 'Custom Analysis',
        description: 'Custom analysis with specific requirements'
      }
    };

    return typeMap[this.config.type] || {
      name: this.config.type,
      description: 'Unknown analysis type'
    };
  }
}

// Export all constants and classes
export {
  ANALYSIS_TYPES,
  ANALYSIS_STATUS,
  ANALYSIS_PRIORITY,
  DATA_QUALITY,
  INSIGHT_TYPES,
  AnalysisConfig,
  AnalysisResult,
  AnalysisInsight,
  KPIValue,
  AnalysisRecommendation,
  AnalysisStep
};
