import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useSocket } from '../../contexts/SocketContext';
import { useLoading } from '../Common/LoadingScreen';

const AnalysisPage = () => {
  const { sessionId } = useParams();
  const navigate = useNavigate();
  const { socket, isConnected } = useSocket();
  const { setLoading } = useLoading();
  const [analysisStatus, setAnalysisStatus] = useState('initializing');
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [logs, setLogs] = useState([]);

  useEffect(() => {
    if (!sessionId) {
      navigate('/');
      return;
    }

    setLoading(true);

    // Socket event listeners for analysis progress
    if (socket && isConnected) {
      socket.on('analysis_progress', (data) => {
        setProgress(data.progress);
        setCurrentStep(data.step);
        setAnalysisStatus(data.status);
      });

      socket.on('analysis_log', (data) => {
        setLogs(prev => [...prev, { ...data, timestamp: new Date() }]);
      });

      socket.on('analysis_complete', (data) => {
        setLoading(false);
        navigate(`/results/${sessionId}`);
      });

      socket.on('analysis_error', (data) => {
        setLoading(false);
        setAnalysisStatus('error');
        console.error('Analysis error:', data.error);
      });

      // Join the analysis session
      socket.emit('join_session', { sessionId });
    }

    return () => {
      if (socket) {
        socket.off('analysis_progress');
        socket.off('analysis_log');
        socket.off('analysis_complete');
        socket.off('analysis_error');
      }
      setLoading(false);
    };
  }, [sessionId, socket, isConnected, navigate, setLoading]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'running': return 'text-blue-600';
      case 'complete': return 'text-green-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Analysis in Progress
          </h1>
          <p className="text-gray-600">
            Session ID: <span className="font-mono text-sm">{sessionId}</span>
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Progress</span>
            <span className="text-sm text-gray-500">{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Current Step */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
            <div>
              <p className="text-sm font-medium text-gray-900">Current Step</p>
              <p className={`text-sm ${getStatusColor(analysisStatus)}`}>
                {currentStep || 'Initializing analysis...'}
              </p>
            </div>
          </div>
        </div>

        {/* Analysis Logs */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Analysis Logs</h3>
          <div className="bg-gray-900 rounded-lg p-4 h-64 overflow-y-auto">
            {logs.length === 0 ? (
              <p className="text-gray-400 text-sm">Waiting for analysis to start...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="text-sm mb-1">
                  <span className="text-gray-400">
                    {log.timestamp.toLocaleTimeString()}
                  </span>
                  <span className="text-white ml-2">{log.message}</span>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between items-center">
          <button
            onClick={() => navigate('/')}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            ← Back to Dashboard
          </button>
          
          {analysisStatus === 'error' && (
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Retry Analysis
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnalysisPage;
