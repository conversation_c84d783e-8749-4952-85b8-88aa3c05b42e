class PlannerAgent {
  constructor(deepseekAPI) {
    this.deepseekAPI = deepseekAPI;
    this.agentId = 'planner';
    this.pattern = 'PEER';
    this.status = 'idle';
    this.capabilities = [
      'business_context_analysis',
      'kpi_discovery',
      'analysis_strategy',
      'domain_expertise_injection'
    ];
  }

  async execute(data, context = {}) {
    this.status = 'processing';
    
    try {
      // Step 1: Analyze business context
      const businessContext = await this.deepseekAPI.analyzeBusinessContext(data);
      
      // Step 2: Discover relevant KPIs
      const kpis = await this.deepseekAPI.discoverKPIs(data, businessContext);
      
      // Step 3: Create analysis strategy
      const strategy = await this.createAnalysisStrategy(data, businessContext, kpis);
      
      // Step 4: Plan execution roadmap
      const roadmap = this.createExecutionRoadmap(strategy);
      
      this.status = 'completed';
      
      return {
        businessContext,
        kpis,
        strategy,
        roadmap,
        metadata: {
          agentId: this.agentId,
          pattern: this.pattern,
          timestamp: new Date().toISOString(),
          confidence: businessContext.confidence || 0.8
        }
      };
      
    } catch (error) {
      this.status = 'error';
      console.error('Planner Agent Error:', error);
      throw error;
    }
  }

  async createAnalysisStrategy(data, businessContext, kpis) {
    const dataCharacteristics = this.analyzeDataCharacteristics(data);
    
    return {
      approach: this.determineAnalysisApproach(dataCharacteristics, businessContext),
      focus_areas: this.identifyFocusAreas(kpis, businessContext),
      methodologies: this.selectMethodologies(dataCharacteristics),
      expected_outcomes: this.defineExpectedOutcomes(businessContext, kpis),
      success_criteria: this.defineSuccessCriteria(kpis)
    };
  }

  analyzeDataCharacteristics(data) {
    const characteristics = {
      row_count: data.length,
      column_count: Object.keys(data[0] || {}).length,
      numeric_columns: this.getNumericColumns(data),
      text_columns: this.getTextColumns(data),
      date_columns: this.getDateColumns(data),
      data_quality: this.assessDataQuality(data),
      time_series: this.detectTimeSeries(data)
    };
    
    return characteristics;
  }

  determineAnalysisApproach(dataCharacteristics, businessContext) {
    if (dataCharacteristics.time_series) {
      return 'time_series_analysis';
    } else if (dataCharacteristics.numeric_columns.length > 5) {
      return 'multivariate_analysis';
    } else {
      return 'descriptive_analysis';
    }
  }

  identifyFocusAreas(kpis, businessContext) {
    const areas = [];
    
    if (kpis.primary_kpis?.includes('revenue') || kpis.primary_kpis?.includes('sales')) {
      areas.push('revenue_optimization');
    }
    
    if (businessContext.businessType?.includes('Customer') || businessContext.businessType?.includes('Service')) {
      areas.push('customer_analytics');
    }
    
    areas.push('operational_efficiency', 'performance_monitoring');
    return areas;
  }

  selectMethodologies(dataCharacteristics) {
    const methodologies = ['descriptive_statistics'];
    
    if (dataCharacteristics.time_series) {
      methodologies.push('trend_analysis', 'forecasting');
    }
    
    if (dataCharacteristics.numeric_columns.length > 2) {
      methodologies.push('correlation_analysis');
    }
    
    return methodologies;
  }

  defineExpectedOutcomes(businessContext, kpis) {
    return {
      insights: `${kpis.primary_kpis?.length || 3} key business insights`,
      recommendations: '5-7 actionable recommendations',
      forecasts: 'Quarterly and annual projections',
      dashboards: 'Executive and operational dashboards',
      roi_estimate: 'Expected business impact assessment'
    };
  }

  defineSuccessCriteria(kpis) {
    return {
      accuracy: 'Analysis accuracy > 85%',
      completeness: 'All primary KPIs analyzed',
      actionability: 'All recommendations have clear implementation steps',
      business_relevance: 'Insights directly applicable to business strategy'
    };
  }

  createExecutionRoadmap(strategy) {
    return {
      phase_1: {
        name: 'Data Analysis Execution',
        duration: '5-10 minutes',
        agents: ['executor'],
        deliverables: ['statistical_analysis', 'kpi_calculations', 'forecasts']
      },
      phase_2: {
        name: 'Visualization Creation',
        duration: '3-5 minutes',
        agents: ['expresser'],
        deliverables: ['charts', 'dashboards', 'visual_insights']
      },
      phase_3: {
        name: 'Quality Review',
        duration: '2-3 minutes',
        agents: ['reviewer'],
        deliverables: ['quality_assessment', 'validation_report']
      },
      phase_4: {
        name: 'Deep Pattern Analysis',
        duration: '5-7 minutes',
        agents: ['data-fining'],
        deliverables: ['pattern_discovery', 'hidden_insights']
      },
      phase_5: {
        name: 'Business Narrative',
        duration: '3-5 minutes',
        agents: ['storyteller'],
        deliverables: ['executive_report', 'recommendations']
      }
    };
  }

  // Utility methods
  getNumericColumns(data) {
    if (!data || data.length === 0) return [];
    return Object.keys(data[0]).filter(key => 
      data.some(row => typeof row[key] === 'number' && !isNaN(row[key]))
    );
  }

  getTextColumns(data) {
    if (!data || data.length === 0) return [];
    return Object.keys(data[0]).filter(key => 
      data.some(row => typeof row[key] === 'string')
    );
  }

  getDateColumns(data) {
    if (!data || data.length === 0) return [];
    return Object.keys(data[0]).filter(key => 
      key.toLowerCase().includes('date') || 
      key.toLowerCase().includes('time') ||
      data.some(row => !isNaN(Date.parse(row[key])))
    );
  }

  assessDataQuality(data) {
    if (!data || data.length === 0) return 0;
    
    const totalCells = data.length * Object.keys(data[0]).length;
    let nullCells = 0;
    
    data.forEach(row => {
      Object.values(row).forEach(value => {
        if (value === null || value === undefined || value === '') {
          nullCells++;
        }
      });
    });
    
    return Math.max(0, 1 - (nullCells / totalCells));
  }

  detectTimeSeries(data) {
    const dateColumns = this.getDateColumns(data);
    return dateColumns.length > 0 && data.length > 5;
  }

  getStatus() {
    return {
      agentId: this.agentId,
      status: this.status,
      pattern: this.pattern,
      capabilities: this.capabilities,
      last_execution: this.lastExecution || null
    };
  }
}

module.exports = PlannerAgent;