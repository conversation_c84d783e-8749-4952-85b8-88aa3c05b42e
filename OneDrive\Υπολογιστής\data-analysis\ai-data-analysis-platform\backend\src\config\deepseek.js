const axios = require('axios');
const winston = require('winston');

class DeepSeekConfig {
  constructor() {
    this.apiKey = process.env.DEEPSEEK_API_KEY;
    this.baseURL = process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1';
    this.model = process.env.DEEPSEEK_MODEL || 'deepseek-chat';
    this.maxTokens = parseInt(process.env.DEEPSEEK_MAX_TOKENS) || 4096;
    this.temperature = parseFloat(process.env.DEEPSEEK_TEMPERATURE) || 0.7;
    this.topP = parseFloat(process.env.DEEPSEEK_TOP_P) || 0.9;
    this.timeout = parseInt(process.env.DEEPSEEK_TIMEOUT) || 30000;
    this.retryAttempts = parseInt(process.env.DEEPSEEK_RETRY_ATTEMPTS) || 3;
    this.retryDelay = parseInt(process.env.DEEPSEEK_RETRY_DELAY) || 1000;

    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/deepseek.log' })
      ]
    });

    if (!this.apiKey) {
      throw new Error('DEEPSEEK_API_KEY environment variable is required');
    }

    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        this.logger.info('DeepSeek API Request:', {
          url: config.url,
          method: config.method,
          timestamp: new Date().toISOString()
        });
        return config;
      },
      (error) => {
        this.logger.error('DeepSeek API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        this.logger.info('DeepSeek API Response:', {
          status: response.status,
          url: response.config.url,
          timestamp: new Date().toISOString()
        });
        return response;
      },
      (error) => {
        this.logger.error('DeepSeek API Response Error:', {
          status: error.response?.status,
          message: error.message,
          url: error.config?.url
        });
        return Promise.reject(error);
      }
    );
  }

  async makeRequest(endpoint, data, options = {}) {
    const config = {
      ...options,
      timeout: options.timeout || this.timeout
    };

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const response = await this.client.post(endpoint, data, config);
        return response.data;
      } catch (error) {
        if (attempt === this.retryAttempts) {
          throw error;
        }

        const isRetryable = this.isRetryableError(error);
        if (!isRetryable) {
          throw error;
        }

        const delay = this.retryDelay * Math.pow(2, attempt - 1);
        this.logger.warn(`DeepSeek API request failed, retrying in ${delay}ms (attempt ${attempt}/${this.retryAttempts})`);
        await this.sleep(delay);
      }
    }
  }

  isRetryableError(error) {
    if (!error.response) return true; // Network errors
    const status = error.response.status;
    return status >= 500 || status === 429; // Server errors or rate limiting
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getDefaultParams() {
    return {
      model: this.model,
      max_tokens: this.maxTokens,
      temperature: this.temperature,
      top_p: this.topP,
      stream: false
    };
  }

  validateApiKey() {
    return !!this.apiKey && this.apiKey.length > 0;
  }
}

// System prompts for different agent types
const SYSTEM_PROMPTS = {
  planner: `You are a data analysis planner agent. Your role is to analyze the data structure and create comprehensive analysis plans. Focus on:
- Understanding data types and relationships
- Identifying key metrics and KPIs
- Planning analysis workflows
- Suggesting appropriate analysis techniques
- Setting analysis priorities`,

  executor: `You are a data analysis executor agent. Your role is to perform statistical analysis and calculations. Focus on:
- Statistical computations and analysis
- KPI calculations and metrics
- Trend analysis and forecasting
- Data aggregations and summaries
- Pattern identification`,

  expresser: `You are a data visualization agent. Your role is to create meaningful visual representations. Focus on:
- Chart type selection and configuration
- Dashboard layout and design
- Visual storytelling
- Data presentation optimization
- Interactive visualization recommendations`,

  reviewer: `You are a quality assurance and review agent. Your role is to validate analysis results. Focus on:
- Data quality assessment
- Analysis accuracy verification
- Result validation and cross-checking
- Error detection and correction
- Quality scoring and recommendations`,

  dataFining: `You are a deep analysis mining agent. Your role is to discover hidden patterns and insights. Focus on:
- Advanced pattern recognition
- Anomaly detection
- Correlation analysis
- Deep data mining
- Predictive insights`,

  storyteller: `You are a business storytelling agent. Your role is to create compelling business narratives. Focus on:
- Business impact interpretation
- Executive summary creation
- Actionable recommendations
- Strategic insights
- ROI and business value communication`
};

module.exports = {
  DeepSeekConfig,
  SYSTEM_PROMPTS
};
