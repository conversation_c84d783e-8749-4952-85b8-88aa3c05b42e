import React, { useState, useCallback } from 'react';
import {
  Box,
  VStack,
  HStack,
  Button,
  Text,
  Progress,
  Alert,
  AlertIcon,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Badge,
  Input
} from '@chakra-ui/react';
import { FiUpload, FiX, FiFile } from 'react-icons/fi';

const FileUpload = ({ onUploadSuccess, onError }) => {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Supported file types
  const SUPPORTED_TYPES = ['text/csv', 'application/json', 'text/plain', 'application/vnd.ms-excel'];

  const handleDrag = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  }, []);

  const handleFiles = (files) => {
    const validFiles = files.filter(file => {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        onError && onError(`File ${file.name} is too large. Maximum size is 10MB.`);
        return false;
      }
      if (!SUPPORTED_TYPES.includes(file.type)) {
        onError && onError(`File ${file.name} has unsupported type. Supported types: CSV, JSON, TXT, Excel.`);
        return false;
      }
      return true;
    });

    setSelectedFiles(prev => [...prev, ...validFiles]);
  };

  const handleFileInput = (e) => {
    if (e.target.files) {
      handleFiles(Array.from(e.target.files));
    }
  };

  const removeFile = (index) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const uploadFiles = async () => {
    if (selectedFiles.length === 0) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i];
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('http://localhost:3001/api/upload', {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }

        setUploadProgress(((i + 1) / selectedFiles.length) * 100);
      }

      onUploadSuccess && onUploadSuccess(selectedFiles);
      setSelectedFiles([]);
      setUploadProgress(0);
    } catch (error) {
      onError && onError(error.message);
    } finally {
      setUploading(false);
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card>
      <CardHeader>
        <Heading size="md">📁 Upload Data Files</Heading>
      </CardHeader>
      <CardBody>
        <VStack spacing={6} align="stretch">
          {/* Upload Area */}
          <Box
            border="2px dashed"
            borderColor={dragActive ? "blue.400" : "gray.300"}
            borderRadius="lg"
            p={8}
            textAlign="center"
            bg={dragActive ? "blue.50" : "gray.50"}
            transition="all 0.2s"
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            cursor="pointer"
            _hover={{ borderColor: "blue.400", bg: "blue.50" }}
          >
            <VStack spacing={4}>
              <FiUpload size={48} color="#3182CE" />
              <Box>
                <Text fontSize="lg" fontWeight="semibold">
                  Drop files here or click to browse
                </Text>
                <Text color="gray.600" fontSize="sm">
                  Supports CSV, JSON, TXT, Excel files (max 10MB each)
                </Text>
              </Box>
              <Input
                type="file"
                multiple
                accept=".csv,.json,.txt,.xls,.xlsx"
                onChange={handleFileInput}
                style={{ display: 'none' }}
                id="file-input"
              />
              <Button
                as="label"
                htmlFor="file-input"
                colorScheme="blue"
                variant="outline"
                cursor="pointer"
              >
                Choose Files
              </Button>
            </VStack>
          </Box>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <Box>
              <Heading size="sm" mb={3}>Selected Files ({selectedFiles.length})</Heading>
              <VStack spacing={2} align="stretch">
                {selectedFiles.map((file, index) => (
                  <HStack key={index} p={3} bg="gray.100" rounded="md" justify="space-between">
                    <HStack>
                      <FiFile />
                      <VStack align="start" spacing={0}>
                        <Text fontSize="sm" fontWeight="medium">{file.name}</Text>
                        <Text fontSize="xs" color="gray.600">{formatFileSize(file.size)}</Text>
                      </VStack>
                    </HStack>
                    <Button
                      size="sm"
                      variant="ghost"
                      colorScheme="red"
                      onClick={() => removeFile(index)}
                      disabled={uploading}
                    >
                      <FiX />
                    </Button>
                  </HStack>
                ))}
              </VStack>
            </Box>
          )}

          {/* Upload Progress */}
          {uploading && (
            <Box>
              <Text fontSize="sm" mb={2}>Uploading files...</Text>
              <Progress value={uploadProgress} colorScheme="blue" rounded="md" />
              <Text fontSize="xs" color="gray.600" mt={1}>
                {Math.round(uploadProgress)}% complete
              </Text>
            </Box>
          )}

          {/* Upload Button */}
          {selectedFiles.length > 0 && (
            <Button
              colorScheme="blue"
              size="lg"
              onClick={uploadFiles}
              isLoading={uploading}
              loadingText="Uploading..."
              disabled={selectedFiles.length === 0}
            >
              Upload {selectedFiles.length} File{selectedFiles.length > 1 ? 's' : ''}
            </Button>
          )}

          {/* Info */}
          <Alert status="info" rounded="md">
            <AlertIcon />
            <Box fontSize="sm">
              <Text fontWeight="medium">Supported formats:</Text>
              <Text>CSV, JSON, TXT, Excel files up to 10MB each</Text>
            </Box>
          </Alert>
        </VStack>
      </CardBody>
    </Card>
  );
};

export default FileUpload;
