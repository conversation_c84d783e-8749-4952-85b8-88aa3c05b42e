class ExecutorAgent {
  constructor(deepseekAPI) {
    this.deepseekAPI = deepseekAPI;
    this.agentId = 'executor';
    this.pattern = 'PEER';
    this.status = 'idle';
    this.capabilities = [
      'statistical_analysis',
      'data_calculations',
      'forecasting',
      'kpi_computation',
      'trend_analysis'
    ];
  }

  async execute(data, planContext = {}) {
    this.status = 'processing';
    
    try {
      // Step 1: Perform statistical analysis
      const statisticalAnalysis = await this.deepseekAPI.performStatisticalAnalysis(data, planContext.kpis);
      
      // Step 2: Calculate KPIs
      const kpiCalculations = await this.calculateKPIs(data, planContext.kpis);
      
      // Step 3: Generate forecasts
      const forecasts = await this.deepseekAPI.generateForecasts(data, planContext.businessContext);
      
      // Step 4: Perform trend analysis
      const trendAnalysis = await this.performTrendAnalysis(data);
      
      // Step 5: Extract business insights
      const businessInsights = await this.extractBusinessInsights(statisticalAnalysis, kpiCalculations);
      
      this.status = 'completed';
      
      return {
        statisticalAnalysis,
        kpiCalculations,
        forecasts,
        trendAnalysis,
        businessInsights,
        metadata: {
          agentId: this.agentId,
          pattern: this.pattern,
          timestamp: new Date().toISOString(),
          dataQuality: this.assessDataQuality(data),
          processingTime: this.getProcessingTime()
        }
      };
      
    } catch (error) {
      this.status = 'error';
      console.error('Executor Agent Error:', error);
      throw error;
    }
  }

  async calculateKPIs(data, kpis = {}) {
    const calculations = {};
    const numericColumns = this.getNumericColumns(data);
    
    // Primary KPIs calculation
    if (kpis.primary_kpis) {
      for (const kpi of kpis.primary_kpis) {
        if (numericColumns.includes(kpi)) {
          calculations[kpi] = this.calculateKPIMetrics(data, kpi);
        }
      }
    }
    
    // Calculated KPIs
    calculations.growth_rates = this.calculateGrowthRates(data);
    calculations.ratios = this.calculateRatios(data);
    calculations.aggregates = this.calculateAggregates(data);
    
    return calculations;
  }

  calculateKPIMetrics(data, column) {
    const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
    
    if (values.length === 0) return null;
    
    const sum = values.reduce((a, b) => a + b, 0);
    const mean = sum / values.length;
    const sortedValues = values.sort((a, b) => a - b);
    const median = this.calculateMedian(sortedValues);
    const stdDev = this.calculateStandardDeviation(values, mean);
    
    return {
      current_value: values[values.length - 1],
      average: mean,
      median,
      total: sum,
      min: Math.min(...values),
      max: Math.max(...values),
      standard_deviation: stdDev,
      variance: stdDev * stdDev,
      trend: this.calculateTrend(values),
      growth_rate: this.calculateGrowthRate(values),
      percentiles: {
        p25: this.calculatePercentile(sortedValues, 25),
        p75: this.calculatePercentile(sortedValues, 75),
        p90: this.calculatePercentile(sortedValues, 90)
      }
    };
  }

  async performTrendAnalysis(data) {
    const numericColumns = this.getNumericColumns(data);
    const timeColumns = this.getTimeColumns(data);
    const trends = {};
    
    for (const column of numericColumns) {
      const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
      
      trends[column] = {
        direction: this.calculateTrend(values),
        strength: this.calculateTrendStrength(values),
        correlation_with_time: timeColumns.length > 0 ? this.calculateTimeCorrelation(data, column, timeColumns[0]) : null,
        seasonal_patterns: this.detectSeasonalPatterns(values),
        anomalies: this.detectAnomalies(values),
        volatility: this.calculateVolatility(values)
      };
    }
    
    return trends;
  }

  async extractBusinessInsights(statisticalAnalysis, kpiCalculations) {
    const insights = [];
    
    // Revenue insights
    if (kpiCalculations.revenue || kpiCalculations.sales) {
      const revenueData = kpiCalculations.revenue || kpiCalculations.sales;
      if (revenueData.growth_rate > 0.1) {
        insights.push(`Strong revenue growth of ${(revenueData.growth_rate * 100).toFixed(1)}% detected`);
      } else if (revenueData.growth_rate < -0.05) {
        insights.push(`Revenue decline of ${Math.abs(revenueData.growth_rate * 100).toFixed(1)}% requires attention`);
      }
    }
    
    // Performance insights
    if (statisticalAnalysis.correlations) {
      for (const correlation of statisticalAnalysis.correlations) {
        if (Math.abs(correlation.correlation) > 0.7) {
          insights.push(`Strong correlation (${(correlation.correlation * 100).toFixed(0)}%) between ${correlation.column1} and ${correlation.column2}`);
        }
      }
    }
    
    // Trend insights
    const growingMetrics = Object.entries(kpiCalculations)
      .filter(([key, value]) => value && value.trend === 'increasing')
      .map(([key]) => key);
    
    if (growingMetrics.length > 0) {
      insights.push(`Positive trends identified in: ${growingMetrics.join(', ')}`);
    }
    
    return insights;
  }

  // Utility methods
  getNumericColumns(data) {
    if (!data || data.length === 0) return [];
    return Object.keys(data[0]).filter(key => 
      data.some(row => typeof row[key] === 'number' && !isNaN(row[key]))
    );
  }

  getTimeColumns(data) {
    if (!data || data.length === 0) return [];
    return Object.keys(data[0]).filter(key => 
      key.toLowerCase().includes('date') || 
      key.toLowerCase().includes('time') ||
      data.some(row => !isNaN(Date.parse(row[key])))
    );
  }

  calculateMedian(sortedValues) {
    const mid = Math.floor(sortedValues.length / 2);
    return sortedValues.length % 2 !== 0 
      ? sortedValues[mid] 
      : (sortedValues[mid - 1] + sortedValues[mid]) / 2;
  }

  calculateStandardDeviation(values, mean) {
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
    return Math.sqrt(avgSquaredDiff);
  }

  calculatePercentile(sortedValues, percentile) {
    const index = (percentile / 100) * (sortedValues.length - 1);
    if (Number.isInteger(index)) {
      return sortedValues[index];
    } else {
      const lower = Math.floor(index);
      const upper = Math.ceil(index);
      const weight = index - lower;
      return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
    }
  }

  calculateTrend(values) {
    if (values.length < 3) return 'stable';
    
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    
    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
    
    const changePercent = (secondAvg - firstAvg) / firstAvg;
    
    if (changePercent > 0.05) return 'increasing';
    if (changePercent < -0.05) return 'decreasing';
    return 'stable';
  }

  calculateTrendStrength(values) {
    if (values.length < 3) return 0;
    
    const linearRegression = this.calculateLinearRegression(values);
    return Math.abs(linearRegression.correlation);
  }

  calculateLinearRegression(values) {
    const n = values.length;
    const x = Array.from({length: n}, (_, i) => i);
    const y = values;
    
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
    const sumYY = y.reduce((sum, yi) => sum + yi * yi, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    const correlation = (n * sumXY - sumX * sumY) / 
      Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));
    
    return { slope, intercept, correlation };
  }

  calculateGrowthRate(values) {
    if (values.length < 2) return 0;
    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    return firstValue !== 0 ? (lastValue - firstValue) / firstValue : 0;
  }

  calculateGrowthRates(data) {
    const numericColumns = this.getNumericColumns(data);
    const growthRates = {};
    
    for (const column of numericColumns) {
      const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
      if (values.length >= 2) {
        growthRates[column] = this.calculateGrowthRate(values);
      }
    }
    
    return growthRates;
  }

  calculateRatios(data) {
    const numericColumns = this.getNumericColumns(data);
    const ratios = {};
    
    // Calculate common business ratios
    if (numericColumns.includes('revenue') && numericColumns.includes('cost')) {
      const revenueValues = data.map(row => parseFloat(row.revenue)).filter(v => !isNaN(v));
      const costValues = data.map(row => parseFloat(row.cost)).filter(v => !isNaN(v));
      
      if (revenueValues.length === costValues.length) {
        ratios.profit_margin = revenueValues.map((rev, i) => 
          costValues[i] !== 0 ? (rev - costValues[i]) / rev : 0
        );
      }
    }
    
    return ratios;
  }

  calculateAggregates(data) {
    const numericColumns = this.getNumericColumns(data);
    const aggregates = {};
    
    for (const column of numericColumns) {
      const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
      
      if (values.length > 0) {
        aggregates[column] = {
          sum: values.reduce((a, b) => a + b, 0),
          count: values.length,
          average: values.reduce((a, b) => a + b, 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values)
        };
      }
    }
    
    return aggregates;
  }

  calculateTimeCorrelation(data, column, timeColumn) {
    // Simple implementation - can be enhanced
    const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
    return values.length > 1 ? this.calculateTrendStrength(values) : 0;
  }

  detectSeasonalPatterns(values) {
    // Basic seasonal pattern detection
    if (values.length < 12) return [];
    
    const patterns = [];
    const quarterlyAvgs = [];
    
    for (let i = 0; i < 4; i++) {
      const quarterValues = [];
      for (let j = i; j < values.length; j += 4) {
        quarterValues.push(values[j]);
      }
      if (quarterValues.length > 0) {
        quarterlyAvgs.push(quarterValues.reduce((a, b) => a + b, 0) / quarterValues.length);
      }
    }
    
    if (quarterlyAvgs.length === 4) {
      const maxQuarter = quarterlyAvgs.indexOf(Math.max(...quarterlyAvgs));
      const minQuarter = quarterlyAvgs.indexOf(Math.min(...quarterlyAvgs));
      patterns.push(`Q${maxQuarter + 1} typically strongest, Q${minQuarter + 1} weakest`);
    }
    
    return patterns;
  }

  detectAnomalies(values) {
    if (values.length < 5) return [];
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const stdDev = this.calculateStandardDeviation(values, mean);
    const threshold = 2 * stdDev;
    
    const anomalies = [];
    values.forEach((value, index) => {
      if (Math.abs(value - mean) > threshold) {
        anomalies.push({
          index,
          value,
          deviation: Math.abs(value - mean) / stdDev
        });
      }
    });
    
    return anomalies;
  }

  calculateVolatility(values) {
    if (values.length < 2) return 0;
    
    const returns = [];
    for (let i = 1; i < values.length; i++) {
      if (values[i - 1] !== 0) {
        returns.push((values[i] - values[i - 1]) / values[i - 1]);
      }
    }
    
    if (returns.length === 0) return 0;
    
    const meanReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    return this.calculateStandardDeviation(returns, meanReturn);
  }

  assessDataQuality(data) {
    if (!data || data.length === 0) return 0;
    
    const totalCells = data.length * Object.keys(data[0]).length;
    let validCells = 0;
    
    data.forEach(row => {
      Object.values(row).forEach(value => {
        if (value !== null && value !== undefined && value !== '') {
          validCells++;
        }
      });
    });
    
    return validCells / totalCells;
  }

  getProcessingTime() {
    return '3-5 minutes';
  }

  getStatus() {
    return {
      agentId: this.agentId,
      status: this.status,
      pattern: this.pattern,
      capabilities: this.capabilities,
      last_execution: this.lastExecution || null
    };
  }
}

module.exports = ExecutorAgent;
