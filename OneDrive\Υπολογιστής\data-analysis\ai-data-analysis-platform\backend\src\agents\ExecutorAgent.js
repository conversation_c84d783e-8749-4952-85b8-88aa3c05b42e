// Import enhanced AI agents
const MathematicalGeniusAgent = require('./MathematicalGeniusAgent');
const SemanticIntelligenceAgent = require('./SemanticIntelligenceAgent');
const BusinessStrategistAgent = require('./BusinessStrategistAgent');

class ExecutorAgent {
  constructor(deepseekAPI) {
    this.deepseekAPI = deepseekAPI;
    this.agentId = 'executor';
    this.pattern = 'PEER';
    this.status = 'idle';
    this.capabilities = [
      'statistical_analysis',
      'data_calculations',
      'forecasting',
      'kpi_computation',
      'trend_analysis',
      'mathematical_modeling',
      'semantic_analysis',
      'business_intelligence',
      'predictive_analytics',
      'strategic_insights'
    ];

    // Initialize enhanced AI agents
    this.enhancedAgents = {
      mathematical: new MathematicalGeniusAgent(),
      semantic: new SemanticIntelligenceAgent(),
      business: new BusinessStrategistAgent()
    };

    console.log('🚀 Enhanced ExecutorAgent initialized with AI specialist agents');
  }

  async execute(data, planContext = {}) {
    this.status = 'processing';

    try {
      // Validate input data
      if (!data || !Array.isArray(data) || data.length === 0) {
        throw new Error('Invalid or empty data provided to ExecutorAgent');
      }

      console.log(`🚀 Enhanced ExecutorAgent processing ${data.length} records with AI specialists...`);

      // ENHANCED AI ANALYSIS PIPELINE
      const startTime = Date.now();

      // Step 1: Mathematical Genius Analysis (DeepSeek Reasoner + Advanced Stats)
      console.log('🧮 Executing Mathematical Genius Agent...');
      const mathematicalAnalysis = await this.enhancedAgents.mathematical.analyze(data, planContext);

      // Step 2: Semantic Intelligence Analysis (Multi-Embedding Models)
      console.log('🔍 Executing Semantic Intelligence Agent...');
      const semanticAnalysis = await this.enhancedAgents.semantic.analyze(data, planContext);

      // Step 3: Business Strategist Analysis (DeepSeek Chat + Strategic Insights)
      console.log('💼 Executing Business Strategist Agent...');
      const businessAnalysis = await this.enhancedAgents.business.analyze(data, planContext);

      // Step 4: Legacy Analysis (for compatibility)
      const legacyKPIs = await this.calculateKPIs(data, planContext.kpis);
      const legacyTrends = await this.performTrendAnalysis(data);

      // Step 5: Synthesize all analyses into comprehensive results
      const synthesizedResults = await this.synthesizeEnhancedAnalyses({
        mathematical: mathematicalAnalysis,
        semantic: semanticAnalysis,
        business: businessAnalysis,
        legacy: { kpis: legacyKPIs, trends: legacyTrends }
      }, data);

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      this.status = 'completed';

      return {
        // Enhanced AI Results
        mathematicalAnalysis: mathematicalAnalysis.results,
        semanticAnalysis: semanticAnalysis.results,
        businessAnalysis: businessAnalysis.results,

        // Synthesized Intelligence
        synthesizedInsights: synthesizedResults,

        // Legacy compatibility
        statisticalAnalysis: mathematicalAnalysis.results.statisticalAnalysis,
        kpiCalculations: mathematicalAnalysis.results.kpiAnalysis,
        forecasts: mathematicalAnalysis.results.predictiveAnalysis,
        trendAnalysis: legacyTrends,
        businessInsights: businessAnalysis.results.executiveSummary,

        // Enhanced metadata
        metadata: {
          agentId: this.agentId,
          pattern: this.pattern,
          timestamp: new Date().toISOString(),
          dataQuality: this.assessDataQuality(data),
          processingTime: `${processingTime}ms`,
          recordsProcessed: data.length,
          analysisDepth: 'world-class',
          aiAgentsUsed: ['Mathematical Genius', 'Semantic Intelligence', 'Business Strategist'],
          confidenceScore: this.calculateOverallConfidence([
            mathematicalAnalysis,
            semanticAnalysis,
            businessAnalysis
          ]),
          enhancedCapabilities: this.capabilities
        }
      };

    } catch (error) {
      this.status = 'error';
      console.error('❌ Enhanced Executor Agent Error:', error);

      // Fallback to legacy analysis if enhanced agents fail
      console.log('🔄 Falling back to legacy analysis...');
      return await this.executeLegacyAnalysis(data, planContext);
    }
  }

  async calculateKPIs(data, kpis = {}) {
    const calculations = {};
    const numericColumns = this.getNumericColumns(data);
    
    // Primary KPIs calculation
    if (kpis.primary_kpis) {
      for (const kpi of kpis.primary_kpis) {
        if (numericColumns.includes(kpi)) {
          calculations[kpi] = this.calculateKPIMetrics(data, kpi);
        }
      }
    }
    
    // Calculated KPIs
    calculations.growth_rates = this.calculateGrowthRates(data);
    calculations.ratios = this.calculateRatios(data);
    calculations.aggregates = this.calculateAggregates(data);
    
    return calculations;
  }

  calculateKPIMetrics(data, column) {
    const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
    
    if (values.length === 0) return null;
    
    const sum = values.reduce((a, b) => a + b, 0);
    const mean = sum / values.length;
    const sortedValues = values.sort((a, b) => a - b);
    const median = this.calculateMedian(sortedValues);
    const stdDev = this.calculateStandardDeviation(values, mean);
    
    return {
      current_value: values[values.length - 1],
      average: mean,
      median,
      total: sum,
      min: Math.min(...values),
      max: Math.max(...values),
      standard_deviation: stdDev,
      variance: stdDev * stdDev,
      trend: this.calculateTrend(values),
      growth_rate: this.calculateGrowthRate(values),
      percentiles: {
        p25: this.calculatePercentile(sortedValues, 25),
        p75: this.calculatePercentile(sortedValues, 75),
        p90: this.calculatePercentile(sortedValues, 90)
      }
    };
  }

  async performTrendAnalysis(data) {
    const numericColumns = this.getNumericColumns(data);
    const timeColumns = this.getTimeColumns(data);
    const trends = {};
    
    for (const column of numericColumns) {
      const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
      
      trends[column] = {
        direction: this.calculateTrend(values),
        strength: this.calculateTrendStrength(values),
        correlation_with_time: timeColumns.length > 0 ? this.calculateTimeCorrelation(data, column, timeColumns[0]) : null,
        seasonal_patterns: this.detectSeasonalPatterns(values),
        anomalies: this.detectAnomalies(values),
        volatility: this.calculateVolatility(values)
      };
    }
    
    return trends;
  }

  async extractBusinessInsights(statisticalAnalysis, kpiCalculations) {
    const insights = [];
    
    // Revenue insights
    if (kpiCalculations.revenue || kpiCalculations.sales) {
      const revenueData = kpiCalculations.revenue || kpiCalculations.sales;
      if (revenueData.growth_rate > 0.1) {
        insights.push(`Strong revenue growth of ${(revenueData.growth_rate * 100).toFixed(1)}% detected`);
      } else if (revenueData.growth_rate < -0.05) {
        insights.push(`Revenue decline of ${Math.abs(revenueData.growth_rate * 100).toFixed(1)}% requires attention`);
      }
    }
    
    // Performance insights
    if (statisticalAnalysis.correlations && Array.isArray(statisticalAnalysis.correlations)) {
      for (const correlation of statisticalAnalysis.correlations) {
        if (correlation && correlation.correlation && Math.abs(correlation.correlation) > 0.7) {
          insights.push(`Strong correlation (${(correlation.correlation * 100).toFixed(0)}%) between ${correlation.column1} and ${correlation.column2}`);
        }
      }
    }
    
    // Trend insights
    const growingMetrics = Object.entries(kpiCalculations)
      .filter(([key, value]) => value && value.trend === 'increasing')
      .map(([key]) => key);
    
    if (growingMetrics.length > 0) {
      insights.push(`Positive trends identified in: ${growingMetrics.join(', ')}`);
    }
    
    return insights;
  }

  // Utility methods
  getNumericColumns(data) {
    if (!data || !Array.isArray(data) || data.length === 0) return [];
    if (!data[0] || typeof data[0] !== 'object') return [];

    return Object.keys(data[0]).filter(key =>
      data.some(row => {
        const value = row && row[key];
        return typeof value === 'number' && !isNaN(value);
      })
    );
  }

  getTimeColumns(data) {
    if (!data || data.length === 0) return [];
    return Object.keys(data[0]).filter(key => 
      key.toLowerCase().includes('date') || 
      key.toLowerCase().includes('time') ||
      data.some(row => !isNaN(Date.parse(row[key])))
    );
  }

  calculateMedian(sortedValues) {
    const mid = Math.floor(sortedValues.length / 2);
    return sortedValues.length % 2 !== 0 
      ? sortedValues[mid] 
      : (sortedValues[mid - 1] + sortedValues[mid]) / 2;
  }

  calculateStandardDeviation(values, mean) {
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
    return Math.sqrt(avgSquaredDiff);
  }

  calculatePercentile(sortedValues, percentile) {
    const index = (percentile / 100) * (sortedValues.length - 1);
    if (Number.isInteger(index)) {
      return sortedValues[index];
    } else {
      const lower = Math.floor(index);
      const upper = Math.ceil(index);
      const weight = index - lower;
      return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
    }
  }

  calculateTrend(values) {
    if (values.length < 3) return 'stable';
    
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    
    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
    
    const changePercent = (secondAvg - firstAvg) / firstAvg;
    
    if (changePercent > 0.05) return 'increasing';
    if (changePercent < -0.05) return 'decreasing';
    return 'stable';
  }

  calculateTrendStrength(values) {
    if (values.length < 3) return 0;
    
    const linearRegression = this.calculateLinearRegression(values);
    return Math.abs(linearRegression.correlation);
  }

  calculateLinearRegression(values) {
    const n = values.length;
    const x = Array.from({length: n}, (_, i) => i);
    const y = values;
    
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
    const sumYY = y.reduce((sum, yi) => sum + yi * yi, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    const correlation = (n * sumXY - sumX * sumY) / 
      Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));
    
    return { slope, intercept, correlation };
  }

  calculateGrowthRate(values) {
    if (values.length < 2) return 0;
    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    return firstValue !== 0 ? (lastValue - firstValue) / firstValue : 0;
  }

  calculateGrowthRates(data) {
    const numericColumns = this.getNumericColumns(data);
    const growthRates = {};
    
    for (const column of numericColumns) {
      const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
      if (values.length >= 2) {
        growthRates[column] = this.calculateGrowthRate(values);
      }
    }
    
    return growthRates;
  }

  calculateRatios(data) {
    const numericColumns = this.getNumericColumns(data);
    const ratios = {};
    
    // Calculate common business ratios
    if (numericColumns.includes('revenue') && numericColumns.includes('cost')) {
      const revenueValues = data.map(row => parseFloat(row.revenue)).filter(v => !isNaN(v));
      const costValues = data.map(row => parseFloat(row.cost)).filter(v => !isNaN(v));
      
      if (revenueValues.length === costValues.length) {
        ratios.profit_margin = revenueValues.map((rev, i) => 
          costValues[i] !== 0 ? (rev - costValues[i]) / rev : 0
        );
      }
    }
    
    return ratios;
  }

  calculateAggregates(data) {
    const numericColumns = this.getNumericColumns(data);
    const aggregates = {};
    
    for (const column of numericColumns) {
      const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
      
      if (values.length > 0) {
        aggregates[column] = {
          sum: values.reduce((a, b) => a + b, 0),
          count: values.length,
          average: values.reduce((a, b) => a + b, 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values)
        };
      }
    }
    
    return aggregates;
  }

  calculateTimeCorrelation(data, column, timeColumn) {
    // Simple implementation - can be enhanced
    const values = data.map(row => parseFloat(row[column])).filter(v => !isNaN(v));
    return values.length > 1 ? this.calculateTrendStrength(values) : 0;
  }

  detectSeasonalPatterns(values) {
    // Basic seasonal pattern detection
    if (values.length < 12) return [];
    
    const patterns = [];
    const quarterlyAvgs = [];
    
    for (let i = 0; i < 4; i++) {
      const quarterValues = [];
      for (let j = i; j < values.length; j += 4) {
        quarterValues.push(values[j]);
      }
      if (quarterValues.length > 0) {
        quarterlyAvgs.push(quarterValues.reduce((a, b) => a + b, 0) / quarterValues.length);
      }
    }
    
    if (quarterlyAvgs.length === 4) {
      const maxQuarter = quarterlyAvgs.indexOf(Math.max(...quarterlyAvgs));
      const minQuarter = quarterlyAvgs.indexOf(Math.min(...quarterlyAvgs));
      patterns.push(`Q${maxQuarter + 1} typically strongest, Q${minQuarter + 1} weakest`);
    }
    
    return patterns;
  }

  detectAnomalies(values) {
    if (values.length < 5) return [];
    
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const stdDev = this.calculateStandardDeviation(values, mean);
    const threshold = 2 * stdDev;
    
    const anomalies = [];
    values.forEach((value, index) => {
      if (Math.abs(value - mean) > threshold) {
        anomalies.push({
          index,
          value,
          deviation: Math.abs(value - mean) / stdDev
        });
      }
    });
    
    return anomalies;
  }

  calculateVolatility(values) {
    if (values.length < 2) return 0;
    
    const returns = [];
    for (let i = 1; i < values.length; i++) {
      if (values[i - 1] !== 0) {
        returns.push((values[i] - values[i - 1]) / values[i - 1]);
      }
    }
    
    if (returns.length === 0) return 0;
    
    const meanReturn = returns.reduce((a, b) => a + b, 0) / returns.length;
    return this.calculateStandardDeviation(returns, meanReturn);
  }

  assessDataQuality(data) {
    if (!data || data.length === 0) return 0;
    
    const totalCells = data.length * Object.keys(data[0]).length;
    let validCells = 0;
    
    data.forEach(row => {
      Object.values(row).forEach(value => {
        if (value !== null && value !== undefined && value !== '') {
          validCells++;
        }
      });
    });
    
    return validCells / totalCells;
  }

  getProcessingTime() {
    return '3-5 minutes';
  }

  // Enhanced analysis synthesis methods
  async synthesizeEnhancedAnalyses(analyses, data) {
    console.log('🧠 Synthesizing all AI analyses into comprehensive intelligence...');

    const synthesis = {
      executiveSummary: this.createExecutiveSynthesis(analyses),
      keyInsights: this.extractKeyInsights(analyses),
      criticalFindings: this.identifyCriticalFindings(analyses),
      actionableRecommendations: this.generateActionableRecommendations(analyses),
      riskOpportunityMatrix: this.createRiskOpportunityMatrix(analyses),
      strategicPriorities: this.identifyStrategicPriorities(analyses),
      performanceScorecard: this.createPerformanceScorecard(analyses, data),
      nextSteps: this.generateNextSteps(analyses)
    };

    return synthesis;
  }

  createExecutiveSynthesis(analyses) {
    const insights = [];

    // Mathematical insights
    if (analyses.mathematical?.results?.synthesizedInsights) {
      insights.push(`Mathematical Analysis: ${analyses.mathematical.results.synthesizedInsights.executiveSummary}`);
    }

    // Semantic insights
    if (analyses.semantic?.results?.semanticInsights) {
      insights.push(`Semantic Intelligence: Advanced customer clustering and behavioral analysis completed`);
    }

    // Business insights
    if (analyses.business?.results?.strategicInsights) {
      insights.push(`Strategic Analysis: Comprehensive market and competitive intelligence generated`);
    }

    return {
      summary: `Comprehensive AI analysis of ${analyses.mathematical?.metadata?.recordsProcessed || 0} records completed`,
      keyHighlights: insights,
      overallConfidence: this.calculateOverallConfidence([
        analyses.mathematical,
        analyses.semantic,
        analyses.business
      ]),
      analysisDepth: 'World-class multi-model AI analysis'
    };
  }

  extractKeyInsights(analyses) {
    const insights = [];

    // Extract from mathematical analysis
    if (analyses.mathematical?.results?.synthesizedInsights?.keyFindings) {
      insights.push(...analyses.mathematical.results.synthesizedInsights.keyFindings);
    }

    // Extract from semantic analysis
    if (analyses.semantic?.results?.customerClustering?.clusterInsights) {
      insights.push(...analyses.semantic.results.customerClustering.clusterInsights.map(insight =>
        `Customer Segment: ${insight.keyInsights?.join(', ') || 'Advanced clustering completed'}`
      ));
    }

    // Extract from business analysis
    if (analyses.business?.results?.strategicInsights?.criticalInsights) {
      insights.push(...analyses.business.results.strategicInsights.criticalInsights);
    }

    return insights.slice(0, 10); // Top 10 insights
  }

  identifyCriticalFindings(analyses) {
    const findings = [];

    // Mathematical critical findings
    if (analyses.mathematical?.results?.kpiAnalysis?.businessHealthScore) {
      const healthScore = analyses.mathematical.results.kpiAnalysis.businessHealthScore;
      findings.push({
        category: 'Business Health',
        finding: `Overall business health score: ${healthScore.overallScore}/100`,
        severity: healthScore.overallScore < 70 ? 'High' : healthScore.overallScore < 85 ? 'Medium' : 'Low',
        recommendation: healthScore.interpretation
      });
    }

    // Semantic critical findings
    if (analyses.semantic?.results?.customerClustering?.customerPersonas) {
      const personas = analyses.semantic.results.customerClustering.customerPersonas;
      const largestSegment = personas.reduce((max, persona) =>
        persona.size > max.size ? persona : max, personas[0] || {});

      if (largestSegment) {
        findings.push({
          category: 'Customer Segmentation',
          finding: `Largest customer segment: ${largestSegment.name} (${largestSegment.percentage}%)`,
          severity: 'Medium',
          recommendation: 'Focus marketing efforts on this dominant segment'
        });
      }
    }

    return findings;
  }

  generateActionableRecommendations(analyses) {
    const recommendations = [];

    // Mathematical recommendations
    if (analyses.mathematical?.results?.recommendations) {
      recommendations.push(...analyses.mathematical.results.recommendations);
    }

    // Semantic recommendations
    if (analyses.semantic?.results?.actionableInsights) {
      recommendations.push(...analyses.semantic.results.actionableInsights);
    }

    // Business recommendations
    if (analyses.business?.results?.actionPlan) {
      const actionPlan = analyses.business.results.actionPlan;
      if (actionPlan.shortTerm) recommendations.push(...actionPlan.shortTerm);
      if (actionPlan.mediumTerm) recommendations.push(...actionPlan.mediumTerm.slice(0, 2));
    }

    return recommendations.slice(0, 8); // Top 8 recommendations
  }

  createRiskOpportunityMatrix(analyses) {
    const matrix = {
      highImpactHighProbability: [],
      highImpactLowProbability: [],
      lowImpactHighProbability: [],
      lowImpactLowProbability: []
    };

    // Add opportunities from business analysis
    if (analyses.business?.results?.growthStrategy?.growthOpportunities) {
      matrix.highImpactHighProbability.push(...analyses.business.results.growthStrategy.growthOpportunities.slice(0, 2));
    }

    // Add risks from business analysis
    if (analyses.business?.results?.riskAssessment?.riskFactors) {
      matrix.highImpactLowProbability.push(...analyses.business.results.riskAssessment.riskFactors.slice(0, 2));
    }

    return matrix;
  }

  identifyStrategicPriorities(analyses) {
    const priorities = [];

    // From business strategist
    if (analyses.business?.results?.strategicInsights?.strategicRecommendations) {
      priorities.push(...analyses.business.results.strategicInsights.strategicRecommendations.slice(0, 3));
    }

    // From mathematical analysis
    if (analyses.mathematical?.results?.optimizationAnalysis?.optimizationOpportunities) {
      priorities.push(...analyses.mathematical.results.optimizationAnalysis.optimizationOpportunities.slice(0, 2));
    }

    return priorities;
  }

  createPerformanceScorecard(analyses, data) {
    return {
      dataQuality: this.assessDataQuality(data) * 100,
      analysisCompleteness: this.calculateAnalysisCompleteness(analyses),
      insightReliability: this.calculateInsightReliability(analyses),
      actionabilityScore: this.calculateActionabilityScore(analyses),
      overallScore: this.calculateOverallPerformanceScore(analyses, data)
    };
  }

  generateNextSteps(analyses) {
    return [
      {
        step: 'Review executive summary and key insights',
        timeline: 'Immediate',
        owner: 'Executive Team',
        priority: 'High'
      },
      {
        step: 'Implement top 3 actionable recommendations',
        timeline: '1-2 weeks',
        owner: 'Operations Team',
        priority: 'High'
      },
      {
        step: 'Deep dive into customer segmentation insights',
        timeline: '2-4 weeks',
        owner: 'Marketing Team',
        priority: 'Medium'
      },
      {
        step: 'Develop strategic action plan based on business analysis',
        timeline: '1 month',
        owner: 'Strategy Team',
        priority: 'Medium'
      }
    ];
  }

  calculateOverallConfidence(analyses) {
    const confidences = analyses
      .filter(analysis => analysis && analysis.metadata)
      .map(analysis => analysis.metadata.confidenceScore || 0.5);

    return confidences.length > 0
      ? confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length
      : 0.5;
  }

  calculateAnalysisCompleteness(analyses) {
    let completeness = 0;
    if (analyses.mathematical?.status === 'completed') completeness += 33.33;
    if (analyses.semantic?.status === 'completed') completeness += 33.33;
    if (analyses.business?.status === 'completed') completeness += 33.34;
    return completeness;
  }

  calculateInsightReliability(analyses) {
    const reliabilityScores = [];

    if (analyses.mathematical?.metadata?.confidenceScore) {
      reliabilityScores.push(analyses.mathematical.metadata.confidenceScore * 100);
    }
    if (analyses.semantic?.metadata?.confidenceScore) {
      reliabilityScores.push(analyses.semantic.metadata.confidenceScore * 100);
    }
    if (analyses.business?.metadata?.confidenceScore) {
      reliabilityScores.push(analyses.business.metadata.confidenceScore * 100);
    }

    return reliabilityScores.length > 0
      ? reliabilityScores.reduce((sum, score) => sum + score, 0) / reliabilityScores.length
      : 50;
  }

  calculateActionabilityScore(analyses) {
    let actionableItems = 0;

    if (analyses.mathematical?.results?.recommendations) {
      actionableItems += analyses.mathematical.results.recommendations.length;
    }
    if (analyses.business?.results?.actionPlan) {
      const actionPlan = analyses.business.results.actionPlan;
      actionableItems += (actionPlan.shortTerm?.length || 0) + (actionPlan.mediumTerm?.length || 0);
    }

    return Math.min(actionableItems * 10, 100); // Cap at 100
  }

  calculateOverallPerformanceScore(analyses, data) {
    const dataQuality = this.assessDataQuality(data) * 100;
    const completeness = this.calculateAnalysisCompleteness(analyses);
    const reliability = this.calculateInsightReliability(analyses);
    const actionability = this.calculateActionabilityScore(analyses);

    return (dataQuality * 0.2 + completeness * 0.3 + reliability * 0.3 + actionability * 0.2);
  }

  async executeLegacyAnalysis(data, planContext) {
    console.log('🔄 Executing legacy analysis as fallback...');

    try {
      const kpiCalculations = await this.calculateKPIs(data, planContext.kpis);
      const trendAnalysis = await this.performTrendAnalysis(data);
      const businessInsights = await this.extractBusinessInsights({}, kpiCalculations);

      return {
        statisticalAnalysis: { message: 'Legacy statistical analysis completed' },
        kpiCalculations,
        forecasts: { message: 'Basic forecasting completed' },
        trendAnalysis,
        businessInsights,
        metadata: {
          agentId: this.agentId,
          pattern: this.pattern,
          timestamp: new Date().toISOString(),
          dataQuality: this.assessDataQuality(data),
          processingTime: '1-2 minutes',
          recordsProcessed: data.length,
          analysisDepth: 'basic',
          note: 'Fallback analysis - enable AI APIs for enhanced capabilities'
        }
      };
    } catch (error) {
      console.error('❌ Legacy analysis also failed:', error);
      throw error;
    }
  }

  getStatus() {
    return {
      agentId: this.agentId,
      status: this.status,
      pattern: this.pattern,
      capabilities: this.capabilities,
      enhancedAgents: Object.keys(this.enhancedAgents),
      last_execution: this.lastExecution || null
    };
  }
}

module.exports = ExecutorAgent;
