{"name": "ai-data-analysis-backend", "version": "1.0.0", "description": "AI-powered business data analysis platform backend", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Backend build complete'", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "socket.io": "^4.7.2", "axios": "^1.5.0", "dotenv": "^16.3.1", "uuid": "^9.0.0", "compression": "^1.7.4", "helmet": "^7.0.0", "rate-limiter-flexible": "^3.0.0", "mongoose": "^7.5.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "winston": "^3.10.0", "joi": "^17.9.2", "express-validator": "^7.0.1", "express-rate-limit": "^6.10.0", "morgan": "^1.10.0", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.57.0"}, "engines": {"node": ">=18.0.0"}}