# Environment Variables
NODE_ENV=development
PORT=3001

# Database Configuration
DATABASE_URL=mongodb://localhost:27017/ai-data-analysis
DATABASE_NAME=ai_data_analysis

# DeepSeek API Configuration
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_API_URL=https://api.deepseek.com/v1

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production
JWT_EXPIRES_IN=24h

# File Upload Configuration
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads
ALLOWED_FILE_TYPES=csv,xlsx,xls,json

# CORS Configuration
FRONTEND_URL=http://localhost:3000
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Redis Configuration (for session storage)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>

# Analytics Configuration
ENABLE_ANALYTICS=true
ANALYTICS_RETENTION_DAYS=90

# Performance Configuration
MAX_CONCURRENT_ANALYSES=5
ANALYSIS_TIMEOUT_MS=300000
