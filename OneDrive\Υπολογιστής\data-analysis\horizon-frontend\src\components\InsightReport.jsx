import React from 'react';
import {
  Box,
  VStack,
  Text,
  Flex,
  useColorModeValue,
  Badge,
  Icon,
  SimpleGrid,
} from '@chakra-ui/react';

// Horizon UI Components
import Card from 'components/card/Card.js';
import IconBox from 'components/icons/IconBox.js';

import {
  MdInsights,
  MdTrendingUp,
  MdAnalytics,
  MdAssessment,
  MdBusiness,
  MdHealthAndSafety,
  MdCheckCircle,
  MdWarning,
  MdInfo,
} from 'react-icons/md';

const InsightReport = ({ agentResults, data, analysisType = 'business' }) => {
  const textColor = useColorModeValue('secondaryGray.900', 'white');
  const bgColor = useColorModeValue('white', 'navy.700');
  
  // Extract real data insights for your business template
  const getDataInsights = () => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return {
        totalRecords: 0,
        columns: [],
        dataType: 'unknown',
        keyMetrics: {},
        insights: []
      };
    }

    const headers = Object.keys(data[0]);
    const totalRecords = data.length;

    // Analyze your actual business template
    const clients = new Set(data.map(row => row['Συναλλασσόμενος']).filter(Boolean));
    const recipients = new Set(data.map(row => row['Παραλήπτης']).filter(Boolean));
    const principals = new Set(data.map(row => row['Εντολέας']).filter(Boolean));
    const salespeople = new Set(data.map(row => row['Πωλητής']).filter(Boolean));
    const leadSources = new Set(data.map(row => row['Πηγή lead']).filter(Boolean));

    // Determine business type based on data patterns
    const isHealthcareData = recipients.size > 0 && principals.size > 0 &&
      recipients.size !== principals.size; // Different recipients and principals suggest patient-doctor relationship

    // Calculate data completeness
    let filledFields = 0;
    let totalFields = 0;

    data.forEach(row => {
      Object.values(row).forEach(value => {
        totalFields++;
        if (value !== null && value !== undefined && value !== '') {
          filledFields++;
        }
      });
    });

    const dataCompleteness = totalFields > 0 ? (filledFields / totalFields) * 100 : 0;

    const keyMetrics = {
      uniqueClients: clients.size || 0,
      uniqueRecipients: recipients.size || 0,
      uniquePrincipals: principals.size || 0,
      uniqueSalespeople: salespeople.size || 0,
      uniqueLeadSources: leadSources.size || 0,
      dataCompleteness: dataCompleteness || 0,
      recordCount: totalRecords || 0,
      columnCount: headers.length || 0
    };

    return {
      totalRecords,
      columns: headers,
      dataType: isHealthcareData ? 'healthcare' : 'business',
      keyMetrics,
      insights: [
        `Analyzed ${totalRecords} ${isHealthcareData ? 'healthcare' : 'business'} records with ${headers.length} data columns`,
        `${isHealthcareData ? 'Healthcare' : 'Business'} data detected: ${clients.size} unique clients, ${salespeople.size} salespeople`,
        `Data quality: ${(dataCompleteness || 0).toFixed(1)}% complete with ${leadSources.size} lead sources identified`,
        `Business structure: ${recipients.size} recipients and ${principals.size} principals tracked`
      ]
    };
  };

  const dataInsights = getDataInsights();
  
  // Format currency for Greek locale
  const formatCurrency = (value) => {
    const safeValue = isNaN(value) || value === null || value === undefined ? 0 : Number(value);
    return new Intl.NumberFormat('el-GR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(safeValue);
  };

  const formatNumber = (value) => {
    const safeValue = isNaN(value) || value === null || value === undefined ? 0 : Number(value);
    return new Intl.NumberFormat('el-GR').format(Math.round(safeValue));
  };

  return (
    <VStack spacing="6" align="stretch">
      {/* Single Comprehensive Analysis Container */}
      <Card p="8" bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)">
        <VStack spacing="6" align="start">
          {/* Header Section */}
          <Flex align="center" w="100%">
            <IconBox
              w='64px'
              h='64px'
              bg='rgba(255,255,255,0.2)'
              icon={<Icon w='36px' h='36px' as={dataInsights.dataType === 'healthcare' ? MdHealthAndSafety : MdBusiness} color='white' />}
              me="20px"
            />
            <Box flex="1">
              <Text color="white" fontSize="2xl" fontWeight="700" lineHeight="100%">
                {dataInsights.dataType === 'healthcare' ? 'Healthcare Data Analysis Report' : 'Business Intelligence Report'}
              </Text>
              <Text color="rgba(255,255,255,0.8)" fontSize="md" fontWeight="500">
                Comprehensive analysis of {formatNumber(dataInsights.totalRecords)} Greek {dataInsights.dataType} records
              </Text>
            </Box>
            <Badge
              bg="rgba(255,255,255,0.2)"
              color="white"
              size="lg"
              borderRadius="full"
              px="4"
              py="2"
            >
              {dataInsights.keyMetrics.dataCompleteness > 80 ? 'HIGH QUALITY DATA' : 'DATA QUALITY CHECK'}
            </Badge>
          </Flex>

          {/* Executive Summary */}
          <Box w="100%">
            <Text color="white" fontSize="lg" fontWeight="600" mb="3">
              📊 Executive Summary
            </Text>
            <Text color="rgba(255,255,255,0.9)" fontSize="md" lineHeight="1.6">
              This report analyzes {formatNumber(dataInsights.totalRecords || 0)} records from your Greek {dataInsights.dataType || 'business'} business template,
              featuring {dataInsights.keyMetrics?.uniqueClients || 0} unique clients, {dataInsights.keyMetrics?.uniqueSalespeople || 0} salespeople,
              and {dataInsights.keyMetrics?.uniqueLeadSources || 0} lead sources. Data quality is {(dataInsights.keyMetrics?.dataCompleteness || 0).toFixed(1)}% complete.
            </Text>
          </Box>

          {/* Key Findings */}
          <Box w="100%">
            <Text color="white" fontSize="lg" fontWeight="600" mb="4">
              🔍 Key Findings & Insights
            </Text>
            <VStack spacing="3" align="stretch">
              {(dataInsights.insights || []).map((insight, index) => (
                <Flex key={index} align="center" bg="rgba(255,255,255,0.1)" p="3" borderRadius="8px">
                  <Icon as={MdCheckCircle} color="white" w="20px" h="20px" me="12px" />
                  <Text color="white" fontSize="sm" fontWeight="500" lineHeight="1.5">
                    {insight}
                  </Text>
                </Flex>
              ))}
            </VStack>
          </Box>

          {/* Business Metrics */}
          <Box w="100%">
            <Text color="white" fontSize="lg" fontWeight="600" mb="4">
              📈 Business Metrics Overview
            </Text>
            <SimpleGrid columns={{ base: 2, md: 4 }} spacing="4" w="100%">
              <Box bg="rgba(255,255,255,0.1)" p="4" borderRadius="8px" textAlign="center">
                <Text color="white" fontSize="2xl" fontWeight="700">
                  {formatNumber(dataInsights.keyMetrics?.uniqueClients || 0)}
                </Text>
                <Text color="rgba(255,255,255,0.8)" fontSize="sm">
                  Unique Clients
                </Text>
              </Box>
              <Box bg="rgba(255,255,255,0.1)" p="4" borderRadius="8px" textAlign="center">
                <Text color="white" fontSize="2xl" fontWeight="700">
                  {formatNumber(dataInsights.keyMetrics?.uniqueSalespeople || 0)}
                </Text>
                <Text color="rgba(255,255,255,0.8)" fontSize="sm">
                  Salespeople
                </Text>
              </Box>
              <Box bg="rgba(255,255,255,0.1)" p="4" borderRadius="8px" textAlign="center">
                <Text color="white" fontSize="2xl" fontWeight="700">
                  {formatNumber(dataInsights.keyMetrics?.uniqueLeadSources || 0)}
                </Text>
                <Text color="rgba(255,255,255,0.8)" fontSize="sm">
                  Lead Sources
                </Text>
              </Box>
              <Box bg="rgba(255,255,255,0.1)" p="4" borderRadius="8px" textAlign="center">
                <Text color="white" fontSize="2xl" fontWeight="700">
                  {(dataInsights.keyMetrics?.dataCompleteness || 0).toFixed(1)}%
                </Text>
                <Text color="rgba(255,255,255,0.8)" fontSize="sm">
                  Data Quality
                </Text>
              </Box>
            </SimpleGrid>
          </Box>

          {/* AI Agent Status */}
          <Box w="100%">
            <Text color="white" fontSize="lg" fontWeight="600" mb="4">
              🤖 AI Agent Analysis Status
            </Text>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing="3" w="100%">
              {Object.entries(agentResults || {}).map(([agentName, result], index) => {
                const agentInfo = {
                  executor: { name: 'Statistical Analysis', icon: MdAssessment },
                  expresser: { name: 'Visualization', icon: MdTrendingUp },
                  reviewer: { name: 'Quality Assurance', icon: MdCheckCircle },
                  datafining: { name: 'Pattern Discovery', icon: MdInsights },
                  storyteller: { name: 'Business Intelligence', icon: MdBusiness }
                };

                const agent = agentInfo[agentName] || { name: agentName, icon: MdInfo };
                const status = result?.status || 'pending';

                return (
                  <Flex key={agentName} align="center" bg="rgba(255,255,255,0.1)" p="3" borderRadius="8px">
                    <Icon as={agent.icon} color="white" w="20px" h="20px" me="12px" />
                    <Box flex="1">
                      <Text color="white" fontSize="sm" fontWeight="600">
                        {agent.name}
                      </Text>
                      <Text color="rgba(255,255,255,0.8)" fontSize="xs">
                        {status.toUpperCase()}
                      </Text>
                    </Box>
                    <Box
                      w="8px"
                      h="8px"
                      borderRadius="50%"
                      bg={status === 'completed' ? 'green.400' : status === 'failed' ? 'red.400' : 'orange.400'}
                    />
                  </Flex>
                );
              })}
            </SimpleGrid>
          </Box>
        </VStack>
      </Card>


    </VStack>
  );
};

export default InsightReport;
