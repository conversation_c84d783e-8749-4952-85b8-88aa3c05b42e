import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import toast from 'react-hot-toast';

import FileUpload from '../FileUpload/FileUpload';
import AgentStatus from '../AgentStatus/AgentStatus';
import KPICards from './KPICards';
import Charts from './Charts';
import BusinessReport from '../Reports/BusinessReport';

import { useAnalysis } from '../../hooks/useAnalysis';
import { useAgentStatus } from '../../hooks/useAgentStatus';
import './Dashboard.css';

const Dashboard = () => {
  const { sessionId } = useParams();
  const [currentStep, setCurrentStep] = useState('upload'); // upload, analysis, results
  const [analysisData, setAnalysisData] = useState(null);
  
  const {
    uploadFile,
    startAnalysis,
    getAnalysisResults,
    uploadStatus,
    analysisStatus,
    isLoading
  } = useAnalysis();
  
  const { agentStatuses, connectToAgentUpdates } = useAgentStatus();

  useEffect(() => {
    if (sessionId) {
      setCurrentStep('analysis');
      // Connect to real-time agent updates
      connectToAgentUpdates(sessionId);
    }
  }, [sessionId, connectToAgentUpdates]);

  const handleFileUpload = async (file) => {
    try {
      const result = await uploadFile(file);
      if (result.success) {
        toast.success('File uploaded successfully!');
        setCurrentStep('analysis');
        // Auto-start analysis
        handleStartAnalysis(result.sessionId);
      }
    } catch (error) {
      toast.error('Failed to upload file');
      console.error(error);
    }
  };

  const handleStartAnalysis = async (sid) => {
    try {
      const result = await startAnalysis(sid || sessionId);
      if (result.success) {
        toast.success('Analysis started!');
      }
    } catch (error) {
      toast.error('Failed to start analysis');
      console.error(error);
    }
  };

  const handleAnalysisComplete = async () => {
    try {
      const results = await getAnalysisResults(sessionId);
      setAnalysisData(results);
      setCurrentStep('results');
      toast.success('Analysis completed!');
    } catch (error) {
      toast.error('Failed to get results');
      console.error(error);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 'upload':
        return (
          <motion.div
            key="upload"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="upload-step"
          >
            <div className="hero-section">
              <h1>AI Data Analysis Platform</h1>
              <p>Upload your business data and get enterprise-level insights powered by DeepSeek AI</p>
              <div className="features">
                <div className="feature">
                  <span className="icon">🧠</span>
                  <span>DeepSeek AI Analysis</span>
                </div>
                <div className="feature">
                  <span className="icon">⚡</span>
                  <span>Multi-Agent Processing</span>
                </div>
                <div className="feature">
                  <span className="icon">📊</span>
                  <span>Real-time Insights</span>
                </div>
                <div className="feature">
                  <span className="icon">🎯</span>
                  <span>Actionable Recommendations</span>
                </div>
              </div>
            </div>
            <FileUpload onFileUpload={handleFileUpload} isLoading={isLoading} />
          </motion.div>
        );

      case 'analysis':
        return (
          <motion.div
            key="analysis"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="analysis-step"
          >
            <div className="analysis-header">
              <h2>AI Agent Swarm Processing Your Data</h2>
              <p>Our multi-agent system is analyzing your business data using advanced AI</p>
            </div>
            
            <AgentStatus
              agentStatuses={agentStatuses}
              analysisStatus={analysisStatus}
              onComplete={handleAnalysisComplete}
            />
            
            <div className="deepseek-status">
              <div className="deepseek-header">
                <span className="deepseek-icon">🧠</span>
                <h3>DeepSeek AI Processing</h3>
                <span className="status-indicator active"></span>
              </div>
              <div className="processing-info">
                <p>Advanced language model analyzing business patterns and generating insights...</p>
                <div className="processing-details">
                  <span>• PEER Pattern: Plan → Execute → Express → Review</span>
                  <span>• DOE Pattern: Data-fining → Opinion-inject → Express</span>
                  <span>• Business Intelligence Generation</span>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 'results':
        return (
          <motion.div
            key="results"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="results-step"
          >
            <div className="results-header">
              <h2>Business Intelligence Report</h2>
              <p>AI-powered insights and recommendations for your business</p>
            </div>
            
            {analysisData && (
              <>
                <KPICards data={analysisData} />
                <Charts data={analysisData} />
                <BusinessReport data={analysisData} />
              </>
            )}
            
            <div className="action-bar">
              <button 
                className="btn secondary"
                onClick={() => {
                  setCurrentStep('upload');
                  setAnalysisData(null);
                }}
              >
                Analyze New File
              </button>
              <button className="btn primary">
                Download Report
              </button>
              <button className="btn primary">
                Schedule Updates
              </button>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="dashboard">
      <div className="dashboard-container">
        <AnimatePresence mode="wait">
          {renderStep()}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default Dashboard;