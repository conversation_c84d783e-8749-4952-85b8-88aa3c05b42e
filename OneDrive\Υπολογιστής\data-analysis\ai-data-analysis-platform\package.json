{"name": "ai-data-analysis-platform", "version": "1.0.0", "description": "AI-powered business data analysis platform with DeepSeek integration and multi-agent system", "main": "backend/src/app.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "setup": "npm install && cd backend && npm install && cd ../frontend && npm install", "docker:build": "docker build -t ai-data-analysis-platform .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down"}, "keywords": ["ai", "data-analysis", "deepseek", "business-intelligence", "multi-agent-system", "nodejs", "react", "express"], "author": "AI Data Analysis Platform Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"concurrently": "^8.2.2", "eslint": "^8.57.0", "prettier": "^3.1.1"}, "workspaces": ["backend", "frontend"], "repository": {"type": "git", "url": "https://github.com/your-username/ai-data-analysis-platform.git"}, "bugs": {"url": "https://github.com/your-username/ai-data-analysis-platform/issues"}, "homepage": "https://github.com/your-username/ai-data-analysis-platform#readme"}