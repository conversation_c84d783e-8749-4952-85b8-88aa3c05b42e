import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { AgentProvider } from './contexts/AgentContext';
import { AnalysisProvider } from './contexts/AnalysisContext';
import { ChakraProvider } from '@chakra-ui/react';
import theme from './styles/theme';
import './styles/global.css';

// Error boundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Application Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ 
          padding: '20px', 
          textAlign: 'center',
          fontFamily: 'Arial, sans-serif'
        }}>
          <h1>Something went wrong.</h1>
          <p>We're sorry, but something went wrong. Please refresh the page.</p>
          <button 
            onClick={() => window.location.reload()}
            style={{
              padding: '10px 20px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Refresh Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Create root and render app
const root = ReactDOM.createRoot(document.getElementById('root'));

root.render(
  <React.StrictMode>
    <ErrorBoundary>
      <ChakraProvider theme={theme}>
        <AgentProvider>
          <AnalysisProvider>
            <App />
          </AnalysisProvider>
        </AgentProvider>
      </ChakraProvider>
    </ErrorBoundary>
  </React.StrictMode>
);
