import { useAgentContext } from '../contexts/AgentContext';
import { useCallback, useEffect } from 'react';

/**
 * Custom hook for managing AI agents
 */
export const useAgents = () => {
  const context = useAgentContext();
  
  if (!context) {
    throw new Error('useAgents must be used within an AgentProvider');
  }

  const {
    executions,
    agentTypes,
    loading,
    error,
    executing,
    fetchAgents,
    fetchAgentTypes,
    executeAgent,
    getAgentStatus,
    getAgentResult,
    cancelAgentExecution,
    deleteAgentExecution,
    pollAgentStatus,
    clearAgents,
    isExecuting,
    getExecutionsByType,
    getRunningExecutions,
    getCompletedExecutions,
    getFailedExecutions
  } = context;

  // Memoized functions
  const executeAgentAsync = useCallback(async (type, data, options = {}) => {
    const execution = await executeAgent(type, data, { ...options, async: true });
    
    // Start polling if execution is async
    if (execution.status === 'pending' || execution.status === 'running') {
      pollAgentStatus(execution.agentId);
    }
    
    return execution;
  }, [executeAgent, pollAgentStatus]);

  const executeAgentSync = useCallback(async (type, data, options = {}) => {
    return await executeAgent(type, data, { ...options, async: false });
  }, [executeAgent]);

  // Execute multiple agents in sequence
  const executeAgentChain = useCallback(async (chain) => {
    const results = [];
    let previousResult = null;

    for (const step of chain) {
      const { type, data, options = {} } = step;
      
      // Use previous result as input if specified
      const inputData = options.usePreviousResult && previousResult 
        ? { ...data, previousResult } 
        : data;

      try {
        const result = await executeAgent(type, inputData, options);
        results.push({ type, result, success: true });
        previousResult = result;
      } catch (error) {
        results.push({ type, error, success: false });
        
        // Stop chain execution on error unless continue on error is specified
        if (!options.continueOnError) {
          break;
        }
      }
    }

    return results;
  }, [executeAgent]);

  // Execute multiple agents in parallel
  const executeAgentsParallel = useCallback(async (agentConfigs) => {
    const promises = agentConfigs.map(async (config) => {
      try {
        const result = await executeAgent(config.type, config.data, config.options);
        return { ...config, result, success: true };
      } catch (error) {
        return { ...config, error, success: false };
      }
    });

    return await Promise.allSettled(promises);
  }, [executeAgent]);

  // Get agent execution by ID
  const getExecutionById = useCallback((agentId) => {
    return executions.find(exec => exec.agentId === agentId);
  }, [executions]);

  // Get agent type info
  const getAgentTypeInfo = useCallback((type) => {
    return agentTypes.find(agentType => agentType.type === type);
  }, [agentTypes]);

  // Check if agent type is available
  const isAgentTypeAvailable = useCallback((type) => {
    return agentTypes.some(agentType => agentType.type === type);
  }, [agentTypes]);

  // Get execution statistics
  const getExecutionStats = useCallback(() => {
    const total = executions.length;
    const running = getRunningExecutions().length;
    const completed = getCompletedExecutions().length;
    const failed = getFailedExecutions().length;
    const cancelled = executions.filter(exec => exec.status === 'cancelled').length;

    return {
      total,
      running,
      completed,
      failed,
      cancelled,
      successRate: total > 0 ? (completed / total) * 100 : 0,
      failureRate: total > 0 ? (failed / total) * 100 : 0
    };
  }, [executions, getRunningExecutions, getCompletedExecutions, getFailedExecutions]);

  // Refresh all running executions
  const refreshRunningExecutions = useCallback(async () => {
    const running = getRunningExecutions();
    const statusPromises = running.map(exec => getAgentStatus(exec.agentId));
    
    try {
      await Promise.allSettled(statusPromises);
    } catch (error) {
      console.error('Failed to refresh running executions:', error);
    }
  }, [getRunningExecutions, getAgentStatus]);

  // Auto-refresh running executions
  useEffect(() => {
    const runningExecutions = getRunningExecutions();
    
    if (runningExecutions.length > 0) {
      const interval = setInterval(refreshRunningExecutions, 5000);
      return () => clearInterval(interval);
    }
  }, [getRunningExecutions, refreshRunningExecutions]);

  return {
    // State
    agents: executions,
    agentTypes,
    loading,
    error,
    executing,
    
    // Basic operations
    fetchAgents,
    fetchAgentTypes,
    executeAgent,
    executeAgentAsync,
    executeAgentSync,
    getAgentStatus,
    getAgentResult,
    cancelAgentExecution,
    deleteAgentExecution,
    clearAgents,
    
    // Advanced operations
    executeAgentChain,
    executeAgentsParallel,
    pollAgentStatus,
    refreshRunningExecutions,
    
    // Queries
    isExecuting,
    getExecutionsByType,
    getRunningExecutions,
    getCompletedExecutions,
    getFailedExecutions,
    getExecutionById,
    getAgentTypeInfo,
    isAgentTypeAvailable,
    getExecutionStats,
    
    // Computed properties
    hasRunningExecutions: getRunningExecutions().length > 0,
    totalExecutions: executions.length,
    availableAgentTypes: agentTypes.map(type => type.type)
  };
};

export default useAgents;
