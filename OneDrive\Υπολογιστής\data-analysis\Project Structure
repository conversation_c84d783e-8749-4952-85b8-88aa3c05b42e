ai-data-analysis-platform/
├── backend/
│   ├── src/
│   │   ├── agents/
│   │   │   ├── AgentUniverse.js
│   │   │   ├── PlannerAgent.js
│   │   │   ├── ExecutorAgent.js
│   │   │   ├── ExpresserAgent.js
│   │   │   ├── ReviewerAgent.js
│   │   │   ├── DataFiningAgent.js
│   │   │   └── StorytellerAgent.js
│   │   ├── services/
│   │   │   ├── DeepSeekAPI.js
│   │   │   ├── FileProcessor.js
│   │   │   └── BusinessAnalyzer.js
│   │   ├── routes/
│   │   │   ├── upload.js
│   │   │   ├── analysis.js
│   │   │   └── agents.js
│   │   ├── middleware/
│   │   │   ├── auth.js
│   │   │   ├── validation.js
│   │   │   └── errorHandler.js
│   │   ├── utils/
│   │   │   ├── dataParser.js
│   │   │   ├── kpiExtractor.js
│   │   │   └── reportGenerator.js
│   │   ├── config/
│   │   │   ├── database.js
│   │   │   └── deepseek.js
│   │   └── app.js
│   ├── package.json
│   └── server.js
├── frontend/
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.ico
│   ├── src/
│   │   ├── components/
│   │   │   ├── FileUpload/
│   │   │   │   ├── FileUpload.jsx
│   │   │   │   └── FileUpload.css
│   │   │   ├── AgentStatus/
│   │   │   │   ├── AgentStatus.jsx
│   │   │   │   ├── AgentCard.jsx
│   │   │   │   └── AgentStatus.css
│   │   │   ├── Dashboard/
│   │   │   │   ├── Dashboard.jsx
│   │   │   │   ├── KPICards.jsx
│   │   │   │   ├── Charts.jsx
│   │   │   │   └── Dashboard.css
│   │   │   ├── Reports/
│   │   │   │   ├── BusinessReport.jsx
│   │   │   │   ├── Recommendations.jsx
│   │   │   │   └── Reports.css
│   │   │   └── Common/
│   │   │       ├── Header.jsx
│   │   │       ├── Loading.jsx
│   │   │       └── Button.jsx
│   │   ├── services/
│   │   │   ├── api.js
│   │   │   ├── websocket.js
│   │   │   └── chartData.js
│   │   ├── hooks/
│   │   │   ├── useFileUpload.js
│   │   │   ├── useAgentStatus.js
│   │   │   └── useAnalysis.js
│   │   ├── contexts/
│   │   │   ├── AnalysisContext.js
│   │   │   └── AgentContext.js
│   │   ├── utils/
│   │   │   ├── formatters.js
│   │   │   └── validators.js
│   │   ├── styles/
│   │   │   ├── globals.css
│   │   │   └── theme.css
│   │   ├── App.jsx
│   │   └── index.js
│   ├── package.json
│   └── webpack.config.js
├── shared/
│   ├── types/
│   │   ├── Agent.js
│   │   ├── Analysis.js
│   │   └── BusinessData.js
│   └── constants/
│       ├── agentTypes.js
│       └── analysisStates.js
├── docs/
│   ├── API.md
│   ├── AGENTS.md
│   └── DEPLOYMENT.md
├── tests/
│   ├── backend/
│   │   ├── agents.test.js
│   │   └── deepseek.test.js
│   └── frontend/
│       ├── components.test.js
│       └── services.test.js
├── docker-compose.yml
├── Dockerfile
├── .env.example
├── .gitignore
├── README.md
└── package.json