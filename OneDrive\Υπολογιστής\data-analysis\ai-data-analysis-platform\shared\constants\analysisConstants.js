// Analysis constants for AI Data Analysis Platform

export const ANALYSIS_TYPES = {
  FINANCIAL: 'financial',
  OPERATIONAL: 'operational',
  MARKETING: 'marketing',
  SALES: 'sales',
  CUSTOMER: 'customer',
  INVENTORY: 'inventory',
  COMPREHENSIVE: 'comprehensive',
  CUSTOM: 'custom'
};

export const ANALYSIS_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

export const AGENT_TYPES = {
  EXECUTOR: 'executor',
  EXPRESSER: 'expresser', 
  REVIEWER: 'reviewer',
  DATA_FINING: 'data_fining',
  STORYTELLER: 'storyteller'
};

export const AGENT_STATUS = {
  IDLE: 'idle',
  WORKING: 'working',
  COMPLETED: 'completed',
  ERROR: 'error'
};

export const FILE_TYPES = {
  CSV: 'csv',
  XLSX: 'xlsx',
  XLS: 'xls',
  JSON: 'json'
};

export const CHART_TYPES = {
  LINE: 'line',
  BAR: 'bar',
  PIE: 'pie',
  SCATTER: 'scatter',
  AREA: 'area',
  DOUGHNUT: 'doughnut',
  RADAR: 'radar',
  BUBBLE: 'bubble'
};

export const REPORT_FORMATS = {
  PDF: 'pdf',
  EXCEL: 'excel',
  JSON: 'json',
  HTML: 'html'
};

export const KPI_CATEGORIES = {
  FINANCIAL: 'financial',
  OPERATIONAL: 'operational',
  CUSTOMER: 'customer',
  GROWTH: 'growth',
  EFFICIENCY: 'efficiency'
};

export const INSIGHT_TYPES = {
  TREND: 'trend',
  ANOMALY: 'anomaly',
  CORRELATION: 'correlation',
  FORECAST: 'forecast',
  RECOMMENDATION: 'recommendation'
};

export const PRIORITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

export const TIME_PERIODS = {
  DAILY: 'daily',
  WEEKLY: 'weekly',
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
  YEARLY: 'yearly'
};

export const AGGREGATION_METHODS = {
  SUM: 'sum',
  AVERAGE: 'average',
  COUNT: 'count',
  MIN: 'min',
  MAX: 'max',
  MEDIAN: 'median',
  PERCENTAGE: 'percentage'
};

export const ANALYSIS_PHASES = {
  INITIALIZATION: 'initialization',
  DATA_PROCESSING: 'data_processing',
  PEER_ANALYSIS: 'peer_analysis',
  DOE_ANALYSIS: 'doe_analysis',
  SYNTHESIS: 'synthesis',
  REPORTING: 'reporting'
};

export const ERROR_TYPES = {
  VALIDATION_ERROR: 'validation_error',
  PROCESSING_ERROR: 'processing_error',
  NETWORK_ERROR: 'network_error',
  AUTHENTICATION_ERROR: 'authentication_error',
  AUTHORIZATION_ERROR: 'authorization_error',
  NOT_FOUND_ERROR: 'not_found_error',
  SERVER_ERROR: 'server_error'
};

export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh'
  },
  UPLOAD: {
    FILE: '/api/upload',
    STATUS: '/api/upload/status',
    DELETE: '/api/upload'
  },
  ANALYSIS: {
    CREATE: '/api/analysis',
    GET: '/api/analysis',
    STATUS: '/api/analysis/status',
    RESULTS: '/api/analysis/results',
    DELETE: '/api/analysis'
  },
  AGENTS: {
    LIST: '/api/agents',
    STATUS: '/api/agents/status',
    INVOKE: '/api/agents/invoke'
  },
  REPORTS: {
    GENERATE: '/api/reports/generate',
    DOWNLOAD: '/api/reports/download'
  }
};

export const WEBSOCKET_EVENTS = {
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  ANALYSIS_START: 'analysis_start',
  ANALYSIS_PROGRESS: 'analysis_progress',
  ANALYSIS_COMPLETE: 'analysis_complete',
  ANALYSIS_ERROR: 'analysis_error',
  AGENT_UPDATE: 'agent_update'
};

export const VALIDATION_RULES = {
  FILE_MAX_SIZE: 50 * 1024 * 1024, // 50MB
  ALLOWED_EXTENSIONS: ['.csv', '.xlsx', '.xls', '.json'],
  MIN_PASSWORD_LENGTH: 8,
  MAX_FILE_NAME_LENGTH: 255,
  MAX_ANALYSIS_NAME_LENGTH: 100
};

export const UI_CONSTANTS = {
  SIDEBAR_WIDTH: 280,
  HEADER_HEIGHT: 64,
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
  TOAST_DURATION: 4000
};

export const COLORS = {
  PRIMARY: '#3b82f6',
  SECONDARY: '#64748b',
  SUCCESS: '#22c55e',
  WARNING: '#f59e0b',
  ERROR: '#ef4444',
  INFO: '#06b6d4'
};

export const DEFAULT_PAGINATION = {
  PAGE: 1,
  LIMIT: 20,
  MAX_LIMIT: 100
};

export const CACHE_KEYS = {
  USER_PROFILE: 'user_profile',
  ANALYSIS_LIST: 'analysis_list',
  AGENT_STATUS: 'agent_status',
  FILE_LIST: 'file_list'
};

export const LOCAL_STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_PREFERENCES: 'user_preferences',
  THEME: 'theme',
  LANGUAGE: 'language'
};

export default {
  ANALYSIS_TYPES,
  ANALYSIS_STATUS,
  AGENT_TYPES,
  AGENT_STATUS,
  FILE_TYPES,
  CHART_TYPES,
  REPORT_FORMATS,
  KPI_CATEGORIES,
  INSIGHT_TYPES,
  PRIORITY_LEVELS,
  TIME_PERIODS,
  AGGREGATION_METHODS,
  ANALYSIS_PHASES,
  ERROR_TYPES,
  API_ENDPOINTS,
  WEBSOCKET_EVENTS,
  VALIDATION_RULES,
  UI_CONSTANTS,
  COLORS,
  DEFAULT_PAGINATION,
  CACHE_KEYS,
  LOCAL_STORAGE_KEYS
};
