class KPIsExtractor {
  constructor() {
    this.greekColumnMappings = {
      // Your exact business template columns
      'Ημ/νία': 'date',
      'Συναλλασσόμενος': 'client',
      'Παραλήπτης': 'recipient',
      'Εντολέας': 'principal',
      'Πωλητής': 'salesperson',
      'Περιγραφή': 'description',
      'Πρώτη πώληση': 'first_sale',
      'Νέα Πώληση': 'new_sale',
      'Πηγή lead': 'lead_source',

      // Additional Greek business columns
      'Πελάτης': 'customer',
      'Προϊόν': 'product',
      'Ποσότητα': 'quantity',
      'Τιμή': 'price',
      'Έκπτωση': 'discount',
      'Σύνολο': 'total',

      // Healthcare context (when Παραλήπτης = patient, Εντολέας = doctor)
      'ασθενής': 'patient',
      'γιατρός': 'doctor',
      'θεραπεία': 'treatment',
      'διάγνωση': 'diagnosis',
      'κόστος': 'cost',

      // Common English alternatives
      'Date': 'date',
      'First Sale': 'first_sale',
      'New Sale': 'new_sale',
      'Customer': 'customer',
      'Product': 'product',
      'Quantity': 'quantity',
      'Price': 'price',
      'Total': 'total',
      'Patient': 'patient',
      'Doctor': 'doctor'
    };
  }

  /**
   * Extract comprehensive KPIs from business data
   * @param {Array} data - Array of data records
   * @param {Object} options - Extraction options
   * @returns {Object} Extracted KPIs
   */
  extractKPIs(data, options = {}) {
    try {
      if (!data || !Array.isArray(data) || data.length === 0) {
        return this.getEmptyKPIs();
      }

      const normalizedData = this.normalizeColumnNames(data);
      const dataType = this.detectDataType(normalizedData);
      
      console.log(`🔍 KPI Extraction: Processing ${data.length} ${dataType} records`);

      const kpis = {
        // Basic metrics
        totalRecords: data.length,
        dataType: dataType,
        columns: Object.keys(data[0]),
        timeRange: this.extractTimeRange(normalizedData),
        
        // Financial metrics
        financial: this.extractFinancialKPIs(normalizedData),
        
        // Growth metrics
        growth: this.extractGrowthKPIs(normalizedData),
        
        // Performance metrics
        performance: this.extractPerformanceKPIs(normalizedData),
        
        // Quality metrics
        quality: this.extractDataQualityKPIs(normalizedData),
        
        // Trend analysis
        trends: this.extractTrendKPIs(normalizedData),
        
        // Summary insights
        insights: this.generateKPIInsights(normalizedData, dataType)
      };

      console.log(`✅ KPI Extraction Complete: ${Object.keys(kpis.financial).length} financial metrics extracted`);
      return kpis;

    } catch (error) {
      console.error('❌ KPI Extraction Error:', error);
      return this.getEmptyKPIs();
    }
  }

  /**
   * Normalize Greek column names to standard English equivalents
   */
  normalizeColumnNames(data) {
    if (!data || data.length === 0) return data;

    return data.map(row => {
      const normalizedRow = {};
      
      Object.keys(row).forEach(key => {
        const normalizedKey = this.greekColumnMappings[key] || key.toLowerCase().replace(/\s+/g, '_');
        normalizedRow[normalizedKey] = row[key];
        // Keep original key as well for reference
        normalizedRow[key] = row[key];
      });
      
      return normalizedRow;
    });
  }

  /**
   * Detect if data is healthcare or business type
   */
  detectDataType(data) {
    if (!data || data.length === 0) return 'unknown';
    
    const firstRow = data[0];
    const keys = Object.keys(firstRow).join(' ').toLowerCase();
    
    // Check for healthcare indicators
    if (keys.includes('patient') || keys.includes('doctor') || 
        keys.includes('παραλήπτης') || keys.includes('εντολέας') ||
        keys.includes('ασθενής') || keys.includes('γιατρός')) {
      return 'healthcare';
    }
    
    // Check for business indicators
    if (keys.includes('sale') || keys.includes('πώληση') || 
        keys.includes('customer') || keys.includes('πελάτης') ||
        keys.includes('product') || keys.includes('προϊόν')) {
      return 'business';
    }
    
    return 'general';
  }

  /**
   * Extract financial KPIs
   */
  extractFinancialKPIs(data) {
    const financial = {
      totalRevenue: 0,
      averageTransactionValue: 0,
      totalTransactions: 0,
      revenueGrowth: 0,
      topPerformingPeriods: [],
      currency: 'EUR'
    };

    try {
      // Extract first sale data
      const firstSaleValues = this.extractNumericValues(data, ['first_sale', 'Πρώτη πώληση']);
      const newSaleValues = this.extractNumericValues(data, ['new_sale', 'Νέα Πώληση']);
      
      // Calculate totals
      financial.firstSaleTotal = firstSaleValues.reduce((sum, val) => sum + val, 0);
      financial.newSaleTotal = newSaleValues.reduce((sum, val) => sum + val, 0);
      financial.totalRevenue = financial.firstSaleTotal + financial.newSaleTotal;
      
      // Calculate averages
      financial.avgFirstSale = firstSaleValues.length > 0 ? 
        financial.firstSaleTotal / firstSaleValues.length : 0;
      financial.avgNewSale = newSaleValues.length > 0 ? 
        financial.newSaleTotal / newSaleValues.length : 0;
      financial.averageTransactionValue = (financial.avgFirstSale + financial.avgNewSale) / 2;
      
      // Calculate growth
      if (financial.firstSaleTotal > 0) {
        financial.revenueGrowth = ((financial.newSaleTotal - financial.firstSaleTotal) / 
          financial.firstSaleTotal) * 100;
      }
      
      financial.totalTransactions = Math.max(firstSaleValues.length, newSaleValues.length);
      
      // Additional metrics
      financial.maxTransaction = Math.max(...firstSaleValues, ...newSaleValues);
      financial.minTransaction = Math.min(...firstSaleValues.filter(v => v > 0), 
        ...newSaleValues.filter(v => v > 0));
      
    } catch (error) {
      console.error('Financial KPI extraction error:', error);
    }

    return financial;
  }

  /**
   * Extract growth-related KPIs
   */
  extractGrowthKPIs(data) {
    const growth = {
      overallGrowthRate: 0,
      monthlyGrowthRate: 0,
      trendDirection: 'stable',
      growthAcceleration: 0,
      volatility: 0
    };

    try {
      const firstSaleValues = this.extractNumericValues(data, ['first_sale', 'Πρώτη πώληση']);
      const newSaleValues = this.extractNumericValues(data, ['new_sale', 'Νέα Πώληση']);
      
      if (firstSaleValues.length > 0 && newSaleValues.length > 0) {
        const avgFirst = firstSaleValues.reduce((sum, val) => sum + val, 0) / firstSaleValues.length;
        const avgNew = newSaleValues.reduce((sum, val) => sum + val, 0) / newSaleValues.length;
        
        if (avgFirst > 0) {
          growth.overallGrowthRate = ((avgNew - avgFirst) / avgFirst) * 100;
          growth.trendDirection = growth.overallGrowthRate > 5 ? 'increasing' : 
                                 growth.overallGrowthRate < -5 ? 'decreasing' : 'stable';
        }
        
        // Calculate volatility (coefficient of variation)
        const allValues = [...firstSaleValues, ...newSaleValues];
        const mean = allValues.reduce((sum, val) => sum + val, 0) / allValues.length;
        const variance = allValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / allValues.length;
        growth.volatility = mean > 0 ? Math.sqrt(variance) / mean : 0;
      }
      
    } catch (error) {
      console.error('Growth KPI extraction error:', error);
    }

    return growth;
  }

  /**
   * Extract performance KPIs
   */
  extractPerformanceKPIs(data) {
    const performance = {
      efficiency: 0,
      consistency: 0,
      reliability: 0,
      scalability: 0,
      benchmarkComparison: 'average'
    };

    try {
      const numericColumns = this.getNumericColumns(data);
      
      if (numericColumns.length > 0) {
        // Calculate efficiency as ratio of output to input
        const firstSaleValues = this.extractNumericValues(data, ['first_sale', 'Πρώτη πώληση']);
        const newSaleValues = this.extractNumericValues(data, ['new_sale', 'Νέα Πώληση']);
        
        if (firstSaleValues.length > 0 && newSaleValues.length > 0) {
          const totalFirst = firstSaleValues.reduce((sum, val) => sum + val, 0);
          const totalNew = newSaleValues.reduce((sum, val) => sum + val, 0);
          
          performance.efficiency = totalFirst > 0 ? (totalNew / totalFirst) * 100 : 0;
          
          // Calculate consistency (inverse of coefficient of variation)
          const allValues = [...firstSaleValues, ...newSaleValues];
          const mean = allValues.reduce((sum, val) => sum + val, 0) / allValues.length;
          const stdDev = Math.sqrt(allValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / allValues.length);
          performance.consistency = mean > 0 ? Math.max(0, 100 - (stdDev / mean * 100)) : 0;
          
          // Reliability based on data completeness
          const completenessRatio = allValues.filter(v => v > 0).length / allValues.length;
          performance.reliability = completenessRatio * 100;
        }
      }
      
    } catch (error) {
      console.error('Performance KPI extraction error:', error);
    }

    return performance;
  }

  /**
   * Extract data quality KPIs
   */
  extractDataQualityKPIs(data) {
    const quality = {
      completeness: 0,
      accuracy: 0,
      consistency: 0,
      validity: 0,
      duplicates: 0,
      outliers: 0
    };

    try {
      if (data.length === 0) return quality;
      
      const totalFields = data.length * Object.keys(data[0]).length;
      let filledFields = 0;
      let validFields = 0;
      
      data.forEach(row => {
        Object.values(row).forEach(value => {
          if (value !== null && value !== undefined && value !== '') {
            filledFields++;
            
            // Check if numeric fields are valid numbers
            if (typeof value === 'number' || !isNaN(parseFloat(value))) {
              validFields++;
            } else if (typeof value === 'string' && value.trim().length > 0) {
              validFields++;
            }
          }
        });
      });
      
      quality.completeness = (filledFields / totalFields) * 100;
      quality.accuracy = (validFields / filledFields) * 100 || 0;
      quality.consistency = quality.completeness; // Simplified metric
      quality.validity = quality.accuracy;
      
      // Detect potential duplicates (simplified)
      const uniqueRows = new Set(data.map(row => JSON.stringify(row)));
      quality.duplicates = ((data.length - uniqueRows.size) / data.length) * 100;
      
    } catch (error) {
      console.error('Data quality KPI extraction error:', error);
    }

    return quality;
  }

  /**
   * Extract trend analysis KPIs
   */
  extractTrendKPIs(data) {
    const trends = {
      direction: 'stable',
      strength: 0,
      seasonality: false,
      cyclical: false,
      forecast: []
    };

    try {
      const firstSaleValues = this.extractNumericValues(data, ['first_sale', 'Πρώτη πώληση']);
      const newSaleValues = this.extractNumericValues(data, ['new_sale', 'Νέα Πώληση']);

      if (firstSaleValues.length > 1 && newSaleValues.length > 1) {
        // Simple trend analysis
        const avgFirst = firstSaleValues.reduce((sum, val) => sum + val, 0) / firstSaleValues.length;
        const avgNew = newSaleValues.reduce((sum, val) => sum + val, 0) / newSaleValues.length;

        const trendSlope = avgNew - avgFirst;
        trends.direction = trendSlope > 0 ? 'increasing' : trendSlope < 0 ? 'decreasing' : 'stable';
        trends.strength = Math.abs(trendSlope) / Math.max(avgFirst, avgNew) * 100;

        // Simple forecast (next period prediction)
        trends.forecast = [
          { period: 'next', value: avgNew + trendSlope, confidence: 0.7 }
        ];
      }

    } catch (error) {
      console.error('Trend KPI extraction error:', error);
    }

    return trends;
  }

  /**
   * Extract time range from data
   */
  extractTimeRange(data) {
    const timeRange = {
      startDate: null,
      endDate: null,
      duration: 0,
      periods: 0
    };

    try {
      const dateValues = this.extractDateValues(data, ['date', 'Ημ/νία']);

      if (dateValues.length > 0) {
        const sortedDates = dateValues.sort((a, b) => a - b);
        timeRange.startDate = sortedDates[0];
        timeRange.endDate = sortedDates[sortedDates.length - 1];
        timeRange.duration = timeRange.endDate - timeRange.startDate;
        timeRange.periods = dateValues.length;
      }

    } catch (error) {
      console.error('Time range extraction error:', error);
    }

    return timeRange;
  }

  /**
   * Generate KPI insights based on extracted data
   */
  generateKPIInsights(data, dataType) {
    const insights = [];

    try {
      const financial = this.extractFinancialKPIs(data);
      const growth = this.extractGrowthKPIs(data);
      const quality = this.extractDataQualityKPIs(data);

      // Business-specific insights for your template
      insights.push(`Analyzed ${data.length} business transactions`);

      // Lead source analysis
      const leadSources = this.analyzeLeadSources(data);
      if (leadSources.length > 0) {
        insights.push(`Top lead source: ${leadSources[0].source} (${leadSources[0].count} leads)`);
      }

      // Salesperson performance
      const salespeople = this.analyzeSalesPerformance(data);
      if (salespeople.length > 0) {
        insights.push(`Top performer: ${salespeople[0].name} with €${salespeople[0].total.toLocaleString('el-GR')}`);
      }

      // Financial insights
      if (financial.totalRevenue > 0) {
        insights.push(`Total revenue: €${financial.totalRevenue.toLocaleString('el-GR')}`);

        if (financial.revenueGrowth > 0) {
          insights.push(`Positive revenue growth of ${financial.revenueGrowth.toFixed(1)}%`);
        } else if (financial.revenueGrowth < 0) {
          insights.push(`Revenue decline of ${Math.abs(financial.revenueGrowth).toFixed(1)}%`);
        }
      }

      // Growth insights
      if (growth.trendDirection === 'increasing') {
        insights.push(`Strong upward trend detected`);
      } else if (growth.trendDirection === 'decreasing') {
        insights.push(`Declining trend requires attention`);
      }

      // Quality insights
      if (quality.completeness > 90) {
        insights.push(`Excellent data quality (${quality.completeness.toFixed(1)}% complete)`);
      } else if (quality.completeness < 70) {
        insights.push(`Data quality issues detected (${quality.completeness.toFixed(1)}% complete)`);
      }

    } catch (error) {
      console.error('Insight generation error:', error);
      insights.push('Basic analysis completed successfully');
    }

    return insights;
  }

  /**
   * Analyze lead sources from your data
   */
  analyzeLeadSources(data) {
    const leadSources = {};

    data.forEach(row => {
      const source = row['Πηγή lead'] || row['lead_source'] || 'Unknown';
      leadSources[source] = (leadSources[source] || 0) + 1;
    });

    return Object.entries(leadSources)
      .map(([source, count]) => ({ source, count }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Analyze sales performance by salesperson
   */
  analyzeSalesPerformance(data) {
    const salespeople = {};

    data.forEach(row => {
      const salesperson = row['Πωλητής'] || row['salesperson'] || 'Unknown';
      const firstSale = parseFloat(row['Πρώτη πώληση'] || row['first_sale'] || 0);
      const newSale = parseFloat(row['Νέα Πώληση'] || row['new_sale'] || 0);

      if (!salespeople[salesperson]) {
        salespeople[salesperson] = { name: salesperson, total: 0, transactions: 0 };
      }

      salespeople[salesperson].total += firstSale + newSale;
      salespeople[salesperson].transactions += 1;
    });

    return Object.values(salespeople)
      .sort((a, b) => b.total - a.total);
  }

  /**
   * Extract numeric values from specified columns
   */
  extractNumericValues(data, columnNames) {
    const values = [];

    data.forEach(row => {
      columnNames.forEach(colName => {
        if (row[colName] !== undefined && row[colName] !== null) {
          const numValue = parseFloat(row[colName]);
          if (!isNaN(numValue)) {
            values.push(numValue);
          }
        }
      });
    });

    return values;
  }

  /**
   * Extract date values from specified columns
   */
  extractDateValues(data, columnNames) {
    const dates = [];

    data.forEach(row => {
      columnNames.forEach(colName => {
        if (row[colName] !== undefined && row[colName] !== null) {
          const dateValue = new Date(row[colName]);
          if (!isNaN(dateValue.getTime())) {
            dates.push(dateValue);
          }
        }
      });
    });

    return dates;
  }

  /**
   * Get numeric columns from data
   */
  getNumericColumns(data) {
    if (!data || data.length === 0) return [];

    const firstRow = data[0];
    const numericColumns = [];

    Object.keys(firstRow).forEach(key => {
      const value = firstRow[key];
      if (typeof value === 'number' || !isNaN(parseFloat(value))) {
        numericColumns.push(key);
      }
    });

    return numericColumns;
  }

  /**
   * Return empty KPIs structure
   */
  getEmptyKPIs() {
    return {
      totalRecords: 0,
      dataType: 'unknown',
      columns: [],
      timeRange: { startDate: null, endDate: null, duration: 0, periods: 0 },
      financial: { totalRevenue: 0, averageTransactionValue: 0, totalTransactions: 0, revenueGrowth: 0 },
      growth: { overallGrowthRate: 0, trendDirection: 'stable', volatility: 0 },
      performance: { efficiency: 0, consistency: 0, reliability: 0 },
      quality: { completeness: 0, accuracy: 0, validity: 0, duplicates: 0 },
      trends: { direction: 'stable', strength: 0, forecast: [] },
      insights: ['No data available for analysis']
    };
  }

  /**
   * Enhanced business analysis for your specific template
   */
  analyzeBusinessTemplate(data) {
    const analysis = {
      totalTransactions: data.length,
      uniqueClients: new Set(data.map(row => row['Συναλλασσόμενος'])).size,
      uniqueRecipients: new Set(data.map(row => row['Παραλήπτης'])).size,
      uniquePrincipals: new Set(data.map(row => row['Εντολέας'])).size,
      uniqueSalespeople: new Set(data.map(row => row['Πωλητής'])).size,
      leadSourceDistribution: this.analyzeLeadSources(data),
      salesPerformance: this.analyzeSalesPerformance(data),
      topClients: this.analyzeTopClients(data),
      businessType: this.determineBusinessType(data)
    };

    return analysis;
  }

  /**
   * Analyze top clients by transaction value
   */
  analyzeTopClients(data) {
    const clients = {};

    data.forEach(row => {
      const client = row['Συναλλασσόμενος'] || 'Unknown';
      const firstSale = parseFloat(row['Πρώτη πώληση'] || 0);
      const newSale = parseFloat(row['Νέα Πώληση'] || 0);

      if (!clients[client]) {
        clients[client] = { name: client, total: 0, transactions: 0 };
      }

      clients[client].total += firstSale + newSale;
      clients[client].transactions += 1;
    });

    return Object.values(clients)
      .sort((a, b) => b.total - a.total)
      .slice(0, 10); // Top 10 clients
  }

  /**
   * Determine business type based on data patterns
   */
  determineBusinessType(data) {
    // Check if this is healthcare data (Παραλήπτης = patient, Εντολέας = doctor)
    const recipients = data.map(row => row['Παραλήπτης']).filter(Boolean);
    const principals = data.map(row => row['Εντολέας']).filter(Boolean);

    // Simple heuristic: if recipients and principals are different people, likely healthcare
    const uniqueRecipients = new Set(recipients);
    const uniquePrincipals = new Set(principals);

    if (uniqueRecipients.size > 0 && uniquePrincipals.size > 0) {
      const overlap = [...uniqueRecipients].filter(r => uniquePrincipals.has(r));
      if (overlap.length / uniqueRecipients.size < 0.3) {
        return 'healthcare'; // Low overlap suggests patient-doctor relationship
      }
    }

    return 'business';
  }
}

module.exports = KPIsExtractor;
