class KPIsExtractor {
  constructor() {
    this.greekColumnMappings = {
      // Greek business columns
      'Ημ/νία': 'date',
      'Πρώτη πώληση': 'first_sale',
      'Νέα Πώληση': 'new_sale',
      'Πελάτης': 'customer',
      'Προϊόν': 'product',
      'Ποσότητα': 'quantity',
      'Τιμή': 'price',
      'Έκπτωση': 'discount',
      'Σύνολο': 'total',
      
      // Greek healthcare columns
      'παραλήπτης': 'patient',
      'εντολέας': 'doctor',
      'ασθενής': 'patient',
      'γιατρός': 'doctor',
      'θεραπεία': 'treatment',
      'διάγνωση': 'diagnosis',
      'κόστος': 'cost',
      
      // Common English alternatives
      'Date': 'date',
      'First Sale': 'first_sale',
      'New Sale': 'new_sale',
      'Customer': 'customer',
      'Product': 'product',
      'Quantity': 'quantity',
      'Price': 'price',
      'Total': 'total',
      'Patient': 'patient',
      'Doctor': 'doctor'
    };
  }

  /**
   * Extract comprehensive KPIs from business data
   * @param {Array} data - Array of data records
   * @param {Object} options - Extraction options
   * @returns {Object} Extracted KPIs
   */
  extractKPIs(data, options = {}) {
    try {
      if (!data || !Array.isArray(data) || data.length === 0) {
        return this.getEmptyKPIs();
      }

      const normalizedData = this.normalizeColumnNames(data);
      const dataType = this.detectDataType(normalizedData);
      
      console.log(`🔍 KPI Extraction: Processing ${data.length} ${dataType} records`);

      const kpis = {
        // Basic metrics
        totalRecords: data.length,
        dataType: dataType,
        columns: Object.keys(data[0]),
        timeRange: this.extractTimeRange(normalizedData),
        
        // Financial metrics
        financial: this.extractFinancialKPIs(normalizedData),
        
        // Growth metrics
        growth: this.extractGrowthKPIs(normalizedData),
        
        // Performance metrics
        performance: this.extractPerformanceKPIs(normalizedData),
        
        // Quality metrics
        quality: this.extractDataQualityKPIs(normalizedData),
        
        // Trend analysis
        trends: this.extractTrendKPIs(normalizedData),
        
        // Summary insights
        insights: this.generateKPIInsights(normalizedData, dataType)
      };

      console.log(`✅ KPI Extraction Complete: ${Object.keys(kpis.financial).length} financial metrics extracted`);
      return kpis;

    } catch (error) {
      console.error('❌ KPI Extraction Error:', error);
      return this.getEmptyKPIs();
    }
  }

  /**
   * Normalize Greek column names to standard English equivalents
   */
  normalizeColumnNames(data) {
    if (!data || data.length === 0) return data;

    return data.map(row => {
      const normalizedRow = {};
      
      Object.keys(row).forEach(key => {
        const normalizedKey = this.greekColumnMappings[key] || key.toLowerCase().replace(/\s+/g, '_');
        normalizedRow[normalizedKey] = row[key];
        // Keep original key as well for reference
        normalizedRow[key] = row[key];
      });
      
      return normalizedRow;
    });
  }

  /**
   * Detect if data is healthcare or business type
   */
  detectDataType(data) {
    if (!data || data.length === 0) return 'unknown';
    
    const firstRow = data[0];
    const keys = Object.keys(firstRow).join(' ').toLowerCase();
    
    // Check for healthcare indicators
    if (keys.includes('patient') || keys.includes('doctor') || 
        keys.includes('παραλήπτης') || keys.includes('εντολέας') ||
        keys.includes('ασθενής') || keys.includes('γιατρός')) {
      return 'healthcare';
    }
    
    // Check for business indicators
    if (keys.includes('sale') || keys.includes('πώληση') || 
        keys.includes('customer') || keys.includes('πελάτης') ||
        keys.includes('product') || keys.includes('προϊόν')) {
      return 'business';
    }
    
    return 'general';
  }

  /**
   * Extract financial KPIs
   */
  extractFinancialKPIs(data) {
    const financial = {
      totalRevenue: 0,
      averageTransactionValue: 0,
      totalTransactions: 0,
      revenueGrowth: 0,
      topPerformingPeriods: [],
      currency: 'EUR'
    };

    try {
      // Extract first sale data
      const firstSaleValues = this.extractNumericValues(data, ['first_sale', 'Πρώτη πώληση']);
      const newSaleValues = this.extractNumericValues(data, ['new_sale', 'Νέα Πώληση']);
      
      // Calculate totals
      financial.firstSaleTotal = firstSaleValues.reduce((sum, val) => sum + val, 0);
      financial.newSaleTotal = newSaleValues.reduce((sum, val) => sum + val, 0);
      financial.totalRevenue = financial.firstSaleTotal + financial.newSaleTotal;
      
      // Calculate averages
      financial.avgFirstSale = firstSaleValues.length > 0 ? 
        financial.firstSaleTotal / firstSaleValues.length : 0;
      financial.avgNewSale = newSaleValues.length > 0 ? 
        financial.newSaleTotal / newSaleValues.length : 0;
      financial.averageTransactionValue = (financial.avgFirstSale + financial.avgNewSale) / 2;
      
      // Calculate growth
      if (financial.firstSaleTotal > 0) {
        financial.revenueGrowth = ((financial.newSaleTotal - financial.firstSaleTotal) / 
          financial.firstSaleTotal) * 100;
      }
      
      financial.totalTransactions = Math.max(firstSaleValues.length, newSaleValues.length);
      
      // Additional metrics
      financial.maxTransaction = Math.max(...firstSaleValues, ...newSaleValues);
      financial.minTransaction = Math.min(...firstSaleValues.filter(v => v > 0), 
        ...newSaleValues.filter(v => v > 0));
      
    } catch (error) {
      console.error('Financial KPI extraction error:', error);
    }

    return financial;
  }

  /**
   * Extract growth-related KPIs
   */
  extractGrowthKPIs(data) {
    const growth = {
      overallGrowthRate: 0,
      monthlyGrowthRate: 0,
      trendDirection: 'stable',
      growthAcceleration: 0,
      volatility: 0
    };

    try {
      const firstSaleValues = this.extractNumericValues(data, ['first_sale', 'Πρώτη πώληση']);
      const newSaleValues = this.extractNumericValues(data, ['new_sale', 'Νέα Πώληση']);
      
      if (firstSaleValues.length > 0 && newSaleValues.length > 0) {
        const avgFirst = firstSaleValues.reduce((sum, val) => sum + val, 0) / firstSaleValues.length;
        const avgNew = newSaleValues.reduce((sum, val) => sum + val, 0) / newSaleValues.length;
        
        if (avgFirst > 0) {
          growth.overallGrowthRate = ((avgNew - avgFirst) / avgFirst) * 100;
          growth.trendDirection = growth.overallGrowthRate > 5 ? 'increasing' : 
                                 growth.overallGrowthRate < -5 ? 'decreasing' : 'stable';
        }
        
        // Calculate volatility (coefficient of variation)
        const allValues = [...firstSaleValues, ...newSaleValues];
        const mean = allValues.reduce((sum, val) => sum + val, 0) / allValues.length;
        const variance = allValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / allValues.length;
        growth.volatility = mean > 0 ? Math.sqrt(variance) / mean : 0;
      }
      
    } catch (error) {
      console.error('Growth KPI extraction error:', error);
    }

    return growth;
  }

  /**
   * Extract performance KPIs
   */
  extractPerformanceKPIs(data) {
    const performance = {
      efficiency: 0,
      consistency: 0,
      reliability: 0,
      scalability: 0,
      benchmarkComparison: 'average'
    };

    try {
      const numericColumns = this.getNumericColumns(data);
      
      if (numericColumns.length > 0) {
        // Calculate efficiency as ratio of output to input
        const firstSaleValues = this.extractNumericValues(data, ['first_sale', 'Πρώτη πώληση']);
        const newSaleValues = this.extractNumericValues(data, ['new_sale', 'Νέα Πώληση']);
        
        if (firstSaleValues.length > 0 && newSaleValues.length > 0) {
          const totalFirst = firstSaleValues.reduce((sum, val) => sum + val, 0);
          const totalNew = newSaleValues.reduce((sum, val) => sum + val, 0);
          
          performance.efficiency = totalFirst > 0 ? (totalNew / totalFirst) * 100 : 0;
          
          // Calculate consistency (inverse of coefficient of variation)
          const allValues = [...firstSaleValues, ...newSaleValues];
          const mean = allValues.reduce((sum, val) => sum + val, 0) / allValues.length;
          const stdDev = Math.sqrt(allValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / allValues.length);
          performance.consistency = mean > 0 ? Math.max(0, 100 - (stdDev / mean * 100)) : 0;
          
          // Reliability based on data completeness
          const completenessRatio = allValues.filter(v => v > 0).length / allValues.length;
          performance.reliability = completenessRatio * 100;
        }
      }
      
    } catch (error) {
      console.error('Performance KPI extraction error:', error);
    }

    return performance;
  }

  /**
   * Extract data quality KPIs
   */
  extractDataQualityKPIs(data) {
    const quality = {
      completeness: 0,
      accuracy: 0,
      consistency: 0,
      validity: 0,
      duplicates: 0,
      outliers: 0
    };

    try {
      if (data.length === 0) return quality;
      
      const totalFields = data.length * Object.keys(data[0]).length;
      let filledFields = 0;
      let validFields = 0;
      
      data.forEach(row => {
        Object.values(row).forEach(value => {
          if (value !== null && value !== undefined && value !== '') {
            filledFields++;
            
            // Check if numeric fields are valid numbers
            if (typeof value === 'number' || !isNaN(parseFloat(value))) {
              validFields++;
            } else if (typeof value === 'string' && value.trim().length > 0) {
              validFields++;
            }
          }
        });
      });
      
      quality.completeness = (filledFields / totalFields) * 100;
      quality.accuracy = (validFields / filledFields) * 100 || 0;
      quality.consistency = quality.completeness; // Simplified metric
      quality.validity = quality.accuracy;
      
      // Detect potential duplicates (simplified)
      const uniqueRows = new Set(data.map(row => JSON.stringify(row)));
      quality.duplicates = ((data.length - uniqueRows.size) / data.length) * 100;
      
    } catch (error) {
      console.error('Data quality KPI extraction error:', error);
    }

    return quality;
  }
