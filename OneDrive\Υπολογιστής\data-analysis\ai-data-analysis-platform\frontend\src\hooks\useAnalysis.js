import { useAnalysisContext } from '../contexts/AnalysisContext';
import { useCallback, useEffect, useState } from 'react';

/**
 * Custom hook for managing data analysis
 */
export const useAnalysis = () => {
  const context = useAnalysisContext();
  
  if (!context) {
    throw new Error('useAnalysis must be used within an AnalysisProvider');
  }

  const {
    analyses,
    currentAnalysis,
    loading,
    error,
    starting,
    pagination,
    fetchAnalyses,
    startAnalysis,
    getAnalysisStatus,
    getAnalysisResult,
    getAnalysisInsights,
    getAnalysisKPIs,
    getAnalysisRecommendations,
    generateAnalysisReport,
    rerunAnalysis,
    deleteAnalysis,
    setCurrentAnalysis,
    pollAnalysisStatus,
    clearAnalyses,
    getAnalysesByStatus,
    getRunningAnalyses,
    getCompletedAnalyses,
    getFailedAnalyses,
    hasRunningAnalyses
  } = context;

  const [autoRefresh, setAutoRefresh] = useState(true);

  // Start analysis with file IDs
  const startAnalysisWithFiles = useCallback(async (fileIds, analysisType = 'comprehensive', options = {}) => {
    const config = {
      fileIds,
      analysisType,
      settings: {
        includeVisualization: true,
        includeInsights: true,
        includeRecommendations: true,
        ...options.settings
      },
      agents: options.agents || ['executor', 'expresser', 'reviewer'],
      priority: options.priority || 'normal'
    };

    const analysis = await startAnalysis(config);
    
    // Start polling if analysis is running
    if (analysis.status === 'pending' || analysis.status === 'running') {
      pollAnalysisStatus(analysis.analysisId);
    }
    
    return analysis;
  }, [startAnalysis, pollAnalysisStatus]);

  // Get full analysis details (status + result + insights + KPIs + recommendations)
  const getFullAnalysisDetails = useCallback(async (analysisId) => {
    try {
      const [status, result, insights, kpis, recommendations] = await Promise.allSettled([
        getAnalysisStatus(analysisId),
        getAnalysisResult(analysisId),
        getAnalysisInsights(analysisId),
        getAnalysisKPIs(analysisId),
        getAnalysisRecommendations(analysisId)
      ]);

      return {
        status: status.status === 'fulfilled' ? status.value : null,
        result: result.status === 'fulfilled' ? result.value : null,
        insights: insights.status === 'fulfilled' ? insights.value : null,
        kpis: kpis.status === 'fulfilled' ? kpis.value : null,
        recommendations: recommendations.status === 'fulfilled' ? recommendations.value : null,
        errors: {
          status: status.status === 'rejected' ? status.reason : null,
          result: result.status === 'rejected' ? result.reason : null,
          insights: insights.status === 'rejected' ? insights.reason : null,
          kpis: kpis.status === 'rejected' ? kpis.reason : null,
          recommendations: recommendations.status === 'rejected' ? recommendations.reason : null
        }
      };
    } catch (error) {
      console.error('Failed to get full analysis details:', error);
      throw error;
    }
  }, [getAnalysisStatus, getAnalysisResult, getAnalysisInsights, getAnalysisKPIs, getAnalysisRecommendations]);

  // Get analysis by ID
  const getAnalysisById = useCallback((analysisId) => {
    return analyses.find(analysis => analysis.analysisId === analysisId);
  }, [analyses]);

  // Get analyses by file ID
  const getAnalysesByFileId = useCallback((fileId) => {
    return analyses.filter(analysis => 
      analysis.fileIds && analysis.fileIds.includes(fileId)
    );
  }, [analyses]);

  // Get analysis statistics
  const getAnalysisStats = useCallback(() => {
    const total = analyses.length;
    const running = getRunningAnalyses().length;
    const completed = getCompletedAnalyses().length;
    const failed = getFailedAnalyses().length;
    const cancelled = analyses.filter(analysis => analysis.status === 'cancelled').length;

    return {
      total,
      running,
      completed,
      failed,
      cancelled,
      successRate: total > 0 ? (completed / total) * 100 : 0,
      failureRate: total > 0 ? (failed / total) * 100 : 0,
      averageDuration: completed > 0 
        ? getCompletedAnalyses().reduce((sum, analysis) => sum + (analysis.duration || 0), 0) / completed
        : 0
    };
  }, [analyses, getRunningAnalyses, getCompletedAnalyses, getFailedAnalyses]);

  // Generate comprehensive report
  const generateComprehensiveReport = useCallback(async (analysisId, format = 'pdf') => {
    try {
      const details = await getFullAnalysisDetails(analysisId);
      
      const reportOptions = {
        includeCharts: true,
        includeInsights: true,
        includeKPIs: true,
        includeRecommendations: true,
        includeRawData: format === 'json',
        template: 'comprehensive'
      };

      return await generateAnalysisReport(analysisId, format, reportOptions);
    } catch (error) {
      console.error('Failed to generate comprehensive report:', error);
      throw error;
    }
  }, [getFullAnalysisDetails, generateAnalysisReport]);

  // Export analysis data
  const exportAnalysisData = useCallback(async (analysisId, format = 'json') => {
    try {
      const details = await getFullAnalysisDetails(analysisId);
      
      const exportData = {
        analysis: getAnalysisById(analysisId),
        details,
        exportedAt: new Date().toISOString(),
        format
      };

      if (format === 'json') {
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
          type: 'application/json' 
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `analysis-${analysisId}-${Date.now()}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        return true;
      } else {
        return await generateAnalysisReport(analysisId, format);
      }
    } catch (error) {
      console.error('Failed to export analysis data:', error);
      throw error;
    }
  }, [getFullAnalysisDetails, getAnalysisById, generateAnalysisReport]);

  // Batch operations
  const deleteMultipleAnalyses = useCallback(async (analysisIds) => {
    const results = [];
    
    for (const analysisId of analysisIds) {
      try {
        await deleteAnalysis(analysisId);
        results.push({ analysisId, success: true });
      } catch (error) {
        results.push({ analysisId, success: false, error });
      }
    }
    
    return results;
  }, [deleteAnalysis]);

  const rerunMultipleAnalyses = useCallback(async (analysisIds, options = {}) => {
    const results = [];
    
    for (const analysisId of analysisIds) {
      try {
        const result = await rerunAnalysis(analysisId, options);
        results.push({ analysisId, success: true, result });
        
        // Start polling for rerun analysis
        if (result.status === 'pending' || result.status === 'running') {
          pollAnalysisStatus(analysisId);
        }
      } catch (error) {
        results.push({ analysisId, success: false, error });
      }
    }
    
    return results;
  }, [rerunAnalysis, pollAnalysisStatus]);

  // Refresh all running analyses
  const refreshRunningAnalyses = useCallback(async () => {
    const running = getRunningAnalyses();
    const statusPromises = running.map(analysis => getAnalysisStatus(analysis.analysisId));
    
    try {
      await Promise.allSettled(statusPromises);
    } catch (error) {
      console.error('Failed to refresh running analyses:', error);
    }
  }, [getRunningAnalyses, getAnalysisStatus]);

  // Auto-refresh running analyses
  useEffect(() => {
    if (!autoRefresh) return;
    
    const runningAnalyses = getRunningAnalyses();
    
    if (runningAnalyses.length > 0) {
      const interval = setInterval(refreshRunningAnalyses, 5000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, getRunningAnalyses, refreshRunningAnalyses]);

  // Filter and search
  const filterAnalyses = useCallback((filters) => {
    let filtered = [...analyses];

    if (filters.status) {
      filtered = filtered.filter(analysis => analysis.status === filters.status);
    }

    if (filters.type) {
      filtered = filtered.filter(analysis => analysis.analysisType === filters.type);
    }

    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      filtered = filtered.filter(analysis => new Date(analysis.createdAt) >= fromDate);
    }

    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo);
      filtered = filtered.filter(analysis => new Date(analysis.createdAt) <= toDate);
    }

    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(analysis => 
        analysis.analysisId.toLowerCase().includes(searchTerm) ||
        (analysis.metadata?.filename || '').toLowerCase().includes(searchTerm)
      );
    }

    return filtered;
  }, [analyses]);

  return {
    // State
    analyses,
    currentAnalysis,
    loading,
    error,
    starting,
    pagination,
    autoRefresh,
    
    // Basic operations
    fetchAnalyses,
    startAnalysis,
    startAnalysisWithFiles,
    getAnalysisStatus,
    getAnalysisResult,
    getAnalysisInsights,
    getAnalysisKPIs,
    getAnalysisRecommendations,
    generateAnalysisReport,
    generateComprehensiveReport,
    rerunAnalysis,
    deleteAnalysis,
    setCurrentAnalysis,
    clearAnalyses,
    
    // Advanced operations
    getFullAnalysisDetails,
    exportAnalysisData,
    deleteMultipleAnalyses,
    rerunMultipleAnalyses,
    pollAnalysisStatus,
    refreshRunningAnalyses,
    
    // Queries and filters
    getAnalysesByStatus,
    getRunningAnalyses,
    getCompletedAnalyses,
    getFailedAnalyses,
    getAnalysisById,
    getAnalysesByFileId,
    getAnalysisStats,
    filterAnalyses,
    
    // Settings
    setAutoRefresh,
    
    // Computed properties
    hasRunningAnalyses,
    totalAnalyses: analyses.length,
    hasAnalyses: analyses.length > 0,
    canStartNewAnalysis: !starting && !hasRunningAnalyses()
  };
};

export default useAnalysis;
