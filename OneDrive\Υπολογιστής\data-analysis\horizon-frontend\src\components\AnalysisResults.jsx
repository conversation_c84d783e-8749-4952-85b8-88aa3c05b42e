import React from 'react';
import {
  Box,
  SimpleGrid,
  Text,
  Heading,
  VStack,
  HStack,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useColorModeValue,
  Alert,
  AlertIcon,
  Icon,
  Flex,
  Badge,
  Progress,
  Divider,
} from '@chakra-ui/react';

// Horizon UI Components
import Card from 'components/card/Card.js';
import MiniStatistics from 'components/card/MiniStatistics.js';
import LineChart from 'components/charts/LineChart.js';
import BarChart from 'components/charts/BarChart.js';
import PieChart from 'components/charts/PieChart.js';
import LineAreaChart from 'components/charts/LineAreaChart.js';
import IconBox from 'components/icons/IconBox.js';

// Icons
import {
  MdAttachMoney,
  MdBarChart,
  MdPeople,
  MdTrendingUp,
  MdInsights,
  MdAnalytics,
  MdShowChart,
  MdAccountBalance,
} from 'react-icons/md';

const AnalysisResults = ({ agentResults, data }) => {
  const bgColor = useColorModeValue('white', 'navy.800');
  const textColor = useColorModeValue('secondaryGray.900', 'white');
  
  // Mock data for demonstration - this would come from agent results
  const mockSalesData = [
    { month: 'Jan', sales: 4000, profit: 2400, expenses: 1600 },
    { month: 'Feb', sales: 3000, profit: 1398, expenses: 1602 },
    { month: 'Mar', sales: 2000, profit: 800, expenses: 1200 },
    { month: 'Apr', sales: 2780, profit: 1908, expenses: 872 },
    { month: 'May', sales: 1890, profit: 800, expenses: 1090 },
    { month: 'Jun', sales: 2390, profit: 1800, expenses: 590 },
  ];

  const mockCategoryData = [
    { name: 'Product A', value: 400, color: '#0088FE' },
    { name: 'Product B', value: 300, color: '#00C49F' },
    { name: 'Product C', value: 300, color: '#FFBB28' },
    { name: 'Product D', value: 200, color: '#FF8042' },
  ];

  const mockKPIs = [
    {
      label: 'Total Revenue',
      value: '$124,563',
      change: '+12.5%',
      trend: 'increase',
    },
    {
      label: 'Growth Rate',
      value: '23.8%',
      change: '+2.1%',
      trend: 'increase',
    },
    {
      label: 'Customer Acquisition',
      value: '1,247',
      change: '-3.2%',
      trend: 'decrease',
    },
    {
      label: 'Profit Margin',
      value: '34.2%',
      change: '+5.4%',
      trend: 'increase',
    },
  ];

  const hasCompletedAgents = Object.values(agentResults).some(
    state => state.status === 'completed'
  );

  if (!hasCompletedAgents) {
    return (
      <Alert status="info">
        <AlertIcon />
        Analysis results will appear here once AI agents complete their processing.
      </Alert>
    );
  }

  // Chart data for Horizon UI components
  const lineChartDataRevenue = [
    {
      name: "Revenue",
      data: [4000, 3000, 2000, 2780, 1890, 2390],
    },
    {
      name: "Profit",
      data: [2400, 1398, 800, 1908, 800, 1800],
    },
  ];

  const lineChartOptionsRevenue = {
    chart: {
      toolbar: {
        show: false,
      },
      dropShadow: {
        enabled: true,
        top: 13,
        left: 0,
        blur: 10,
        opacity: 0.1,
        color: "#4318FF",
      },
    },
    colors: ["#4318FF", "#39B8FF"],
    markers: {
      size: 0,
      colors: "white",
      strokeColors: "#7551FF",
      strokeWidth: 3,
      strokeOpacity: 0.9,
      strokeDashArray: 0,
      fillOpacity: 1,
      discrete: [],
      shape: "circle",
      radius: 2,
      offsetX: 0,
      offsetY: 0,
      showNullDataPoints: true,
    },
    tooltip: {
      theme: "dark",
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: "smooth",
      type: "line",
    },
    xaxis: {
      type: "numeric",
      categories: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
      labels: {
        style: {
          colors: "#A3AED0",
          fontSize: "12px",
          fontWeight: "500",
        },
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      show: false,
    },
    legend: {
      show: false,
    },
    grid: {
      show: false,
      column: {
        color: ["#7551FF", "#39B8FF"],
        opacity: 0.5,
      },
    },
    color: ["#7551FF", "#39B8FF"],
  };

  const barChartDataSales = [
    {
      name: "Sales",
      data: [4000, 3000, 2000, 2780, 1890, 2390],
    },
    {
      name: "Expenses",
      data: [1600, 1602, 1200, 872, 1090, 590],
    },
  ];

  const barChartOptionsSales = {
    chart: {
      toolbar: {
        show: false,
      },
    },
    tooltip: {
      style: {
        fontSize: "12px",
        fontFamily: undefined,
      },
      onDatasetHover: {
        style: {
          fontSize: "12px",
          fontFamily: undefined,
        },
      },
      theme: "dark",
    },
    xaxis: {
      categories: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
      show: false,
      labels: {
        show: true,
        style: {
          colors: "#A3AED0",
          fontSize: "14px",
          fontWeight: "500",
        },
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      show: false,
      color: "black",
      labels: {
        show: true,
        style: {
          colors: "#CBD5E0",
          fontSize: "14px",
        },
      },
    },
    grid: {
      show: false,
      strokeDashArray: 5,
      yaxis: {
        lines: {
          show: true,
        },
      },
      xaxis: {
        lines: {
          show: false,
        },
      },
    },
    fill: {
      type: "gradient",
      gradient: {
        type: "vertical",
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.9,
        colorStops: [
          [
            {
              offset: 0,
              color: "#4318FF",
              opacity: 1,
            },
            {
              offset: 100,
              color: "rgba(67, 24, 255, 1)",
              opacity: 0.28,
            },
          ],
        ],
      },
    },
    dataLabels: {
      enabled: false,
    },
    plotOptions: {
      bar: {
        borderRadius: 10,
        columnWidth: "40px",
      },
    },
  };

  const pieChartOptions = {
    labels: ["Product A", "Product B", "Product C", "Product D"],
    colors: ["#4318FF", "#6AD2FF", "#EFF4FB", "#E9EDF7"],
    chart: {
      width: "50px",
    },
    states: {
      hover: {
        filter: {
          type: "none",
        },
      },
    },
    legend: {
      show: false,
    },
    dataLabels: {
      enabled: false,
    },
    hover: { mode: null },
    plotOptions: {
      donut: {
        expandOnClick: false,
        donut: {
          labels: {
            show: false,
          },
        },
      },
    },
    fill: {
      colors: ["#4318FF", "#6AD2FF", "#EFF4FB", "#E9EDF7"],
    },
    tooltip: {
      enabled: true,
      theme: "dark",
    },
  };

  const pieChartData = [400, 300, 300, 200];

  return (
    <VStack spacing="6" align="stretch">
      {/* Enhanced KPI Cards using Horizon UI MiniStatistics */}
      <Box>
        <Heading size="md" mb="4" color={textColor}>
          Key Performance Indicators
        </Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing="4">
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #4318FF 0%, #9F7AEA 100%)'
                icon={<Icon w='32px' h='32px' as={MdAttachMoney} color='white' />}
              />
            }
            name='Total Revenue'
            value='$124,563'
            growth='+12.5%'
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #FF6B6B 0%, #4ECDC4 100%)'
                icon={<Icon w='32px' h='32px' as={MdTrendingUp} color='white' />}
              />
            }
            name='Growth Rate'
            value='23.8%'
            growth='+2.1%'
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #FFE066 0%, #FF6B6B 100%)'
                icon={<Icon w='32px' h='32px' as={MdPeople} color='white' />}
              />
            }
            name='Customer Acquisition'
            value='1,247'
            growth='-3.2%'
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #4ECDC4 0%, #44A08D 100%)'
                icon={<Icon w='32px' h='32px' as={MdAccountBalance} color='white' />}
              />
            }
            name='Profit Margin'
            value='34.2%'
            growth='+5.4%'
          />
        </SimpleGrid>
      </Box>

      {/* Enhanced Charts Grid using Horizon UI Components */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing="6">
        {/* Revenue Trend Chart */}
        <Card p="6">
          <Flex direction="column">
            <Flex
              justify="space-between"
              align="start"
              px="10px"
              pt="5px"
              mb="20px"
            >
              <Flex direction="column" align="start" me="20px">
                <Flex w="100%">
                  <Text
                    me="auto"
                    color="secondaryGray.600"
                    fontSize="sm"
                    fontWeight="500"
                  >
                    Revenue & Profit Trend
                  </Text>
                </Flex>
                <Flex align="end">
                  <Text
                    color={textColor}
                    fontSize="34px"
                    fontWeight="700"
                    lineHeight="100%"
                  >
                    $124,563
                  </Text>
                  <Text
                    ms="6px"
                    color="secondaryGray.600"
                    fontSize="sm"
                    fontWeight="500"
                  >
                    Total Revenue
                  </Text>
                </Flex>
              </Flex>
              <Flex align="center">
                <Icon as={MdBarChart} color="brand.500" w="24px" h="24px" me="7px" />
                <Text color="green.500" fontSize="sm" fontWeight="700">
                  +12.5%
                </Text>
              </Flex>
            </Flex>
            <Box h="240px" mt="auto">
              <LineChart
                chartData={lineChartDataRevenue}
                chartOptions={lineChartOptionsRevenue}
              />
            </Box>
          </Flex>
        </Card>

        {/* Category Distribution */}
        <Card p="6">
          <Flex direction="column" w="100%">
            <Flex
              direction={{ base: "column", lg: "row" }}
              justify="space-between"
              align="start"
              px="10px"
              pt="5px"
              mb="20px"
            >
              <Flex direction="column" align="start" me="20px">
                <Text
                  me="auto"
                  color="secondaryGray.600"
                  fontSize="sm"
                  fontWeight="500"
                >
                  Category Distribution
                </Text>
                <Text
                  color={textColor}
                  fontSize="34px"
                  fontWeight="700"
                  lineHeight="100%"
                >
                  4 Products
                </Text>
              </Flex>
              <Flex align="center">
                <Icon as={MdAnalytics} color="brand.500" w="24px" h="24px" me="7px" />
                <Text color="green.500" fontSize="sm" fontWeight="700">
                  Active
                </Text>
              </Flex>
            </Flex>
            <Box h="240px" mt="auto">
              <PieChart
                chartData={pieChartData}
                chartOptions={pieChartOptions}
              />
            </Box>
            <Flex mt="20px" justify="space-between">
              <Flex align="center">
                <Box w="8px" h="8px" bg="#4318FF" borderRadius="50%" me="7px" />
                <Text fontSize="xs" color="secondaryGray.600" fontWeight="700">
                  Product A: 33%
                </Text>
              </Flex>
              <Flex align="center">
                <Box w="8px" h="8px" bg="#6AD2FF" borderRadius="50%" me="7px" />
                <Text fontSize="xs" color="secondaryGray.600" fontWeight="700">
                  Product B: 25%
                </Text>
              </Flex>
              <Flex align="center">
                <Box w="8px" h="8px" bg="#EFF4FB" borderRadius="50%" me="7px" />
                <Text fontSize="xs" color="secondaryGray.600" fontWeight="700">
                  Product C: 25%
                </Text>
              </Flex>
              <Flex align="center">
                <Box w="8px" h="8px" bg="#E9EDF7" borderRadius="50%" me="7px" />
                <Text fontSize="xs" color="secondaryGray.600" fontWeight="700">
                  Product D: 17%
                </Text>
              </Flex>
            </Flex>
          </Flex>
        </Card>

        {/* Sales vs Expenses */}
        <Card p="6">
          <Flex direction="column">
            <Flex
              justify="space-between"
              align="start"
              px="10px"
              pt="5px"
              mb="20px"
            >
              <Flex direction="column" align="start" me="20px">
                <Text
                  me="auto"
                  color="secondaryGray.600"
                  fontSize="sm"
                  fontWeight="500"
                >
                  Sales vs Expenses
                </Text>
                <Text
                  color={textColor}
                  fontSize="34px"
                  fontWeight="700"
                  lineHeight="100%"
                >
                  $89,340
                </Text>
              </Flex>
              <Flex align="center">
                <Icon as={MdShowChart} color="brand.500" w="24px" h="24px" me="7px" />
                <Text color="green.500" fontSize="sm" fontWeight="700">
                  +8.2%
                </Text>
              </Flex>
            </Flex>
            <Box h="240px" mt="auto">
              <BarChart
                chartData={barChartDataSales}
                chartOptions={barChartOptionsSales}
              />
            </Box>
          </Flex>
        </Card>

        {/* Cumulative Growth */}
        <Card p="6">
          <Flex direction="column">
            <Flex
              justify="space-between"
              align="start"
              px="10px"
              pt="5px"
              mb="20px"
            >
              <Flex direction="column" align="start" me="20px">
                <Text
                  me="auto"
                  color="secondaryGray.600"
                  fontSize="sm"
                  fontWeight="500"
                >
                  Cumulative Growth
                </Text>
                <Text
                  color={textColor}
                  fontSize="34px"
                  fontWeight="700"
                  lineHeight="100%"
                >
                  +23.8%
                </Text>
              </Flex>
              <Flex align="center">
                <Icon as={MdInsights} color="brand.500" w="24px" h="24px" me="7px" />
                <Text color="green.500" fontSize="sm" fontWeight="700">
                  Growing
                </Text>
              </Flex>
            </Flex>
            <Box h="240px" mt="auto">
              <LineAreaChart
                chartData={lineChartDataRevenue}
                chartOptions={lineChartOptionsRevenue}
              />
            </Box>
          </Flex>
        </Card>
      </SimpleGrid>

      {/* Enhanced AI Insights Section */}
      <Card p="6">
        <Flex direction="column">
          <Flex align="center" mb="20px">
            <IconBox
              w='56px'
              h='56px'
              bg='linear-gradient(90deg, #4318FF 0%, #9F7AEA 100%)'
              icon={<Icon w='32px' h='32px' as={MdInsights} color='white' />}
              me="20px"
            />
            <Box>
              <Text
                color={textColor}
                fontSize="xl"
                fontWeight="700"
                lineHeight="100%"
              >
                AI-Generated Insights
              </Text>
              <Text
                color="secondaryGray.600"
                fontSize="sm"
                fontWeight="500"
              >
                Powered by DeepSeek AI Analysis
              </Text>
            </Box>
          </Flex>

          <VStack spacing="4" align="stretch">
            <Card bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" p="4">
              <Text color="white" fontSize="sm" fontWeight="600">
                📈 Revenue Analysis: The data shows strong performance in Q1 with increasing profit margins.
                Product A dominates market share with 33% of total sales.
              </Text>
            </Card>

            <Card bg="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" p="4">
              <Text color="white" fontSize="sm" fontWeight="600">
                🎯 Growth Opportunity: Customer acquisition costs increased by 8% while volume decreased.
                Consider optimizing marketing strategies for better ROI.
              </Text>
            </Card>

            <Card bg="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" p="4">
              <Text color="white" fontSize="sm" fontWeight="600">
                💡 Strategic Insight: Seasonal patterns detected with peaks in January and June.
                Plan inventory and marketing campaigns accordingly.
              </Text>
            </Card>
          </VStack>
        </Flex>
      </Card>

      {/* Agent Contributions Section */}
      <Card p="6">
        <Heading size="md" mb="6" color={textColor}>
          AI Agent Contributions
        </Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing="4">
          <Card bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" p="4">
            <VStack align="start" spacing="3">
              <Flex align="center">
                <Icon as={MdBarChart} color="white" w="20px" h="20px" me="10px" />
                <Text color="white" fontSize="sm" fontWeight="700">
                  Executor Agent - Statistical Analysis
                </Text>
              </Flex>
              <Text color="white" fontSize="xs">
                Performed comprehensive statistical analysis revealing 12.5% revenue growth
                and identified key performance metrics.
              </Text>
            </VStack>
          </Card>

          <Card bg="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" p="4">
            <VStack align="start" spacing="3">
              <Flex align="center">
                <Icon as={MdAnalytics} color="white" w="20px" h="20px" me="10px" />
                <Text color="white" fontSize="sm" fontWeight="700">
                  Data Mining Agent - Pattern Discovery
                </Text>
              </Flex>
              <Text color="white" fontSize="xs">
                Discovered seasonal patterns and product performance correlations
                that inform strategic recommendations.
              </Text>
            </VStack>
          </Card>

          <Card bg="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" p="4">
            <VStack align="start" spacing="3">
              <Flex align="center">
                <Icon as={MdInsights} color="white" w="20px" h="20px" me="10px" />
                <Text color="white" fontSize="sm" fontWeight="700">
                  Storyteller Agent - Business Narrative
                </Text>
              </Flex>
              <Text color="white" fontSize="xs">
                Generated actionable business insights and recommendations
                based on comprehensive data analysis.
              </Text>
            </VStack>
          </Card>
        </SimpleGrid>
      </Card>
    </VStack>
  );
};

export default AnalysisResults;
