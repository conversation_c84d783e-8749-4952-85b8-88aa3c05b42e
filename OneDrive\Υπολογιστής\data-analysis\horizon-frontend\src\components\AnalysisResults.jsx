import React from 'react';
import {
  Box,
  SimpleGrid,
  Text,
  Heading,
  VStack,
  HStack,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useColorModeValue,
  Alert,
  AlertIcon,
  Icon,
  Flex,
  Badge,
  Progress,
  Divider,
} from '@chakra-ui/react';

// Horizon UI Components
import Card from 'components/card/Card.js';
import MiniStatistics from 'components/card/MiniStatistics.js';
import LineChart from 'components/charts/LineChart.js';
import BarChart from 'components/charts/BarChart.js';
import PieChart from 'components/charts/PieChart.js';
import LineAreaChart from 'components/charts/LineAreaChart.js';
import IconBox from 'components/icons/IconBox.js';

// Icons
import {
  MdAttachMoney,
  MdBarChart,
  MdPeople,
  MdTrendingUp,
  MdInsights,
  MdAnalytics,
  MdShowChart,
  MdAccountBalance,
} from 'react-icons/md';

const AnalysisResults = ({ agentResults, data }) => {
  const bgColor = useColorModeValue('white', 'navy.800');
  const textColor = useColorModeValue('secondaryGray.900', 'white');
  
  // Extract real data from agent results and uploaded file
  const getKPIData = () => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return {
        totalRecords: 0,
        columns: [],
        firstSaleTotal: 0,
        newSaleTotal: 0,
        avgFirstSale: 0,
        avgNewSale: 0,
        growth: 0,
        currency: 'EUR'
      };
    }

    const totalRecords = data.length;
    const columns = Object.keys(data[0]);

    // Extract Greek column data - handle different possible column names
    const firstSaleData = data.map(row => {
      const value = row['Πρώτη πώληση'] || row['First Sale'] || row['first_sale'] || 0;
      return parseFloat(value) || 0;
    }).filter(val => val > 0);

    const newSaleData = data.map(row => {
      const value = row['Νέα Πώληση'] || row['New Sale'] || row['new_sale'] || 0;
      return parseFloat(value) || 0;
    }).filter(val => val > 0);

    const firstSaleTotal = firstSaleData.reduce((sum, val) => sum + val, 0);
    const newSaleTotal = newSaleData.reduce((sum, val) => sum + val, 0);
    const avgFirstSale = firstSaleData.length > 0 ? firstSaleTotal / firstSaleData.length : 0;
    const avgNewSale = newSaleData.length > 0 ? newSaleTotal / newSaleData.length : 0;

    const growth = avgFirstSale > 0 ? ((avgNewSale - avgFirstSale) / avgFirstSale * 100) : 0;

    return {
      totalRecords,
      columns,
      firstSaleTotal,
      newSaleTotal,
      avgFirstSale,
      avgNewSale,
      growth,
      firstSaleCount: firstSaleData.length,
      newSaleCount: newSaleData.length,
      currency: 'EUR'
    };
  };

  const kpiData = getKPIData();

  // Format numbers for display
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('el-GR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatNumber = (value) => {
    return new Intl.NumberFormat('el-GR').format(Math.round(value));
  };

  const formatPercentage = (value) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const hasCompletedAgents = Object.values(agentResults).some(
    state => state.status === 'completed'
  );

  if (!hasCompletedAgents) {
    return (
      <Alert status="info">
        <AlertIcon />
        Analysis results will appear here once AI agents complete their processing.
      </Alert>
    );
  }

  // Chart data for Horizon UI components
  const lineChartDataRevenue = [
    {
      name: "Revenue",
      data: [4000, 3000, 2000, 2780, 1890, 2390],
    },
    {
      name: "Profit",
      data: [2400, 1398, 800, 1908, 800, 1800],
    },
  ];

  const lineChartOptionsRevenue = {
    chart: {
      toolbar: {
        show: false,
      },
      dropShadow: {
        enabled: true,
        top: 13,
        left: 0,
        blur: 10,
        opacity: 0.1,
        color: "#4318FF",
      },
    },
    colors: ["#4318FF", "#39B8FF"],
    markers: {
      size: 0,
      colors: "white",
      strokeColors: "#7551FF",
      strokeWidth: 3,
      strokeOpacity: 0.9,
      strokeDashArray: 0,
      fillOpacity: 1,
      discrete: [],
      shape: "circle",
      radius: 2,
      offsetX: 0,
      offsetY: 0,
      showNullDataPoints: true,
    },
    tooltip: {
      theme: "dark",
    },
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: "smooth",
      type: "line",
    },
    xaxis: {
      type: "numeric",
      categories: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
      labels: {
        style: {
          colors: "#A3AED0",
          fontSize: "12px",
          fontWeight: "500",
        },
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      show: false,
    },
    legend: {
      show: false,
    },
    grid: {
      show: false,
      column: {
        color: ["#7551FF", "#39B8FF"],
        opacity: 0.5,
      },
    },
    color: ["#7551FF", "#39B8FF"],
  };

  const barChartDataSales = [
    {
      name: "Sales",
      data: [4000, 3000, 2000, 2780, 1890, 2390],
    },
    {
      name: "Expenses",
      data: [1600, 1602, 1200, 872, 1090, 590],
    },
  ];

  const barChartOptionsSales = {
    chart: {
      toolbar: {
        show: false,
      },
    },
    tooltip: {
      style: {
        fontSize: "12px",
        fontFamily: undefined,
      },
      onDatasetHover: {
        style: {
          fontSize: "12px",
          fontFamily: undefined,
        },
      },
      theme: "dark",
    },
    xaxis: {
      categories: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
      show: false,
      labels: {
        show: true,
        style: {
          colors: "#A3AED0",
          fontSize: "14px",
          fontWeight: "500",
        },
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      show: false,
      color: "black",
      labels: {
        show: true,
        style: {
          colors: "#CBD5E0",
          fontSize: "14px",
        },
      },
    },
    grid: {
      show: false,
      strokeDashArray: 5,
      yaxis: {
        lines: {
          show: true,
        },
      },
      xaxis: {
        lines: {
          show: false,
        },
      },
    },
    fill: {
      type: "gradient",
      gradient: {
        type: "vertical",
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.9,
        colorStops: [
          [
            {
              offset: 0,
              color: "#4318FF",
              opacity: 1,
            },
            {
              offset: 100,
              color: "rgba(67, 24, 255, 1)",
              opacity: 0.28,
            },
          ],
        ],
      },
    },
    dataLabels: {
      enabled: false,
    },
    plotOptions: {
      bar: {
        borderRadius: 10,
        columnWidth: "40px",
      },
    },
  };

  const pieChartOptions = {
    labels: ["Product A", "Product B", "Product C", "Product D"],
    colors: ["#4318FF", "#6AD2FF", "#EFF4FB", "#E9EDF7"],
    chart: {
      width: "50px",
    },
    states: {
      hover: {
        filter: {
          type: "none",
        },
      },
    },
    legend: {
      show: false,
    },
    dataLabels: {
      enabled: false,
    },
    hover: { mode: null },
    plotOptions: {
      donut: {
        expandOnClick: false,
        donut: {
          labels: {
            show: false,
          },
        },
      },
    },
    fill: {
      colors: ["#4318FF", "#6AD2FF", "#EFF4FB", "#E9EDF7"],
    },
    tooltip: {
      enabled: true,
      theme: "dark",
    },
  };

  const pieChartData = [400, 300, 300, 200];

  return (
    <VStack spacing="6" align="stretch">
      {/* Enhanced KPI Cards using Horizon UI MiniStatistics */}
      <Box>
        <Heading size="md" mb="4" color={textColor}>
          Key Performance Indicators
        </Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing="4">
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #4318FF 0%, #9F7AEA 100%)'
                icon={<Icon w='32px' h='32px' as={MdAttachMoney} color='white' />}
              />
            }
            name='Πρώτη Πώληση (Total)'
            value={formatCurrency(kpiData.firstSaleTotal)}
            growth={`${kpiData.firstSaleCount} records`}
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #FF6B6B 0%, #4ECDC4 100%)'
                icon={<Icon w='32px' h='32px' as={MdTrendingUp} color='white' />}
              />
            }
            name='Νέα Πώληση (Total)'
            value={formatCurrency(kpiData.newSaleTotal)}
            growth={`${kpiData.newSaleCount} records`}
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #FFE066 0%, #FF6B6B 100%)'
                icon={<Icon w='32px' h='32px' as={MdPeople} color='white' />}
              />
            }
            name='Growth Rate'
            value={formatPercentage(kpiData.growth)}
            growth={kpiData.growth >= 0 ? 'Positive' : 'Negative'}
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #4ECDC4 0%, #44A08D 100%)'
                icon={<Icon w='32px' h='32px' as={MdAccountBalance} color='white' />}
              />
            }
            name='Total Records'
            value={formatNumber(kpiData.totalRecords)}
            growth={`${kpiData.columns.length} columns`}
          />
        </SimpleGrid>
      </Box>

      {/* Enhanced Charts Grid using Horizon UI Components */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing="6">
        {/* Revenue Trend Chart */}
        <Card p="6">
          <Flex direction="column">
            <Flex
              justify="space-between"
              align="start"
              px="10px"
              pt="5px"
              mb="20px"
            >
              <Flex direction="column" align="start" me="20px">
                <Flex w="100%">
                  <Text
                    me="auto"
                    color="secondaryGray.600"
                    fontSize="sm"
                    fontWeight="500"
                  >
                    Πρώτη vs Νέα Πώληση
                  </Text>
                </Flex>
                <Flex align="end">
                  <Text
                    color={textColor}
                    fontSize="34px"
                    fontWeight="700"
                    lineHeight="100%"
                  >
                    {formatCurrency(kpiData.firstSaleTotal)}
                  </Text>
                  <Text
                    ms="6px"
                    color="secondaryGray.600"
                    fontSize="sm"
                    fontWeight="500"
                  >
                    Πρώτη Πώληση
                  </Text>
                </Flex>
              </Flex>
              <Flex align="center">
                <Icon as={MdBarChart} color="brand.500" w="24px" h="24px" me="7px" />
                <Text color={kpiData.growth >= 0 ? "green.500" : "red.500"} fontSize="sm" fontWeight="700">
                  {formatPercentage(kpiData.growth)}
                </Text>
              </Flex>
            </Flex>
            <Box h="240px" mt="auto">
              <LineChart
                chartData={lineChartDataRevenue}
                chartOptions={lineChartOptionsRevenue}
              />
            </Box>
          </Flex>
        </Card>

        {/* Category Distribution */}
        <Card p="6">
          <Flex direction="column" w="100%">
            <Flex
              direction={{ base: "column", lg: "row" }}
              justify="space-between"
              align="start"
              px="10px"
              pt="5px"
              mb="20px"
            >
              <Flex direction="column" align="start" me="20px">
                <Text
                  me="auto"
                  color="secondaryGray.600"
                  fontSize="sm"
                  fontWeight="500"
                >
                  Data Distribution
                </Text>
                <Text
                  color={textColor}
                  fontSize="34px"
                  fontWeight="700"
                  lineHeight="100%"
                >
                  {formatNumber(kpiData.totalRecords)} Records
                </Text>
              </Flex>
              <Flex align="center">
                <Icon as={MdAnalytics} color="brand.500" w="24px" h="24px" me="7px" />
                <Text color="green.500" fontSize="sm" fontWeight="700">
                  {kpiData.columns.length} Columns
                </Text>
              </Flex>
            </Flex>
            <Box h="240px" mt="auto">
              <PieChart
                chartData={pieChartData}
                chartOptions={pieChartOptions}
              />
            </Box>
            <Flex mt="20px" justify="space-between" wrap="wrap">
              {kpiData.columns.slice(0, 4).map((column, index) => {
                const colors = ["#4318FF", "#6AD2FF", "#EFF4FB", "#E9EDF7"];
                const percentage = Math.round((1 / kpiData.columns.length) * 100);
                return (
                  <Flex key={index} align="center" mb="2">
                    <Box w="8px" h="8px" bg={colors[index]} borderRadius="50%" me="7px" />
                    <Text fontSize="xs" color="secondaryGray.600" fontWeight="700">
                      {column}: {percentage}%
                    </Text>
                  </Flex>
                );
              })}
              {kpiData.columns.length > 4 && (
                <Flex align="center" mb="2">
                  <Box w="8px" h="8px" bg="#A0AEC0" borderRadius="50%" me="7px" />
                  <Text fontSize="xs" color="secondaryGray.600" fontWeight="700">
                    +{kpiData.columns.length - 4} more columns
                  </Text>
                </Flex>
              )}
            </Flex>
          </Flex>
        </Card>

        {/* Sales vs Expenses */}
        <Card p="6">
          <Flex direction="column">
            <Flex
              justify="space-between"
              align="start"
              px="10px"
              pt="5px"
              mb="20px"
            >
              <Flex direction="column" align="start" me="20px">
                <Text
                  me="auto"
                  color="secondaryGray.600"
                  fontSize="sm"
                  fontWeight="500"
                >
                  Average Sales Comparison
                </Text>
                <Text
                  color={textColor}
                  fontSize="34px"
                  fontWeight="700"
                  lineHeight="100%"
                >
                  {formatCurrency(kpiData.avgNewSale)}
                </Text>
              </Flex>
              <Flex align="center">
                <Icon as={MdShowChart} color="brand.500" w="24px" h="24px" me="7px" />
                <Text color={kpiData.growth >= 0 ? "green.500" : "red.500"} fontSize="sm" fontWeight="700">
                  Avg New Sale
                </Text>
              </Flex>
            </Flex>
            <Box h="240px" mt="auto">
              <BarChart
                chartData={barChartDataSales}
                chartOptions={barChartOptionsSales}
              />
            </Box>
          </Flex>
        </Card>

        {/* Cumulative Growth */}
        <Card p="6">
          <Flex direction="column">
            <Flex
              justify="space-between"
              align="start"
              px="10px"
              pt="5px"
              mb="20px"
            >
              <Flex direction="column" align="start" me="20px">
                <Text
                  me="auto"
                  color="secondaryGray.600"
                  fontSize="sm"
                  fontWeight="500"
                >
                  Sales Growth Analysis
                </Text>
                <Text
                  color={textColor}
                  fontSize="34px"
                  fontWeight="700"
                  lineHeight="100%"
                >
                  {formatPercentage(kpiData.growth)}
                </Text>
              </Flex>
              <Flex align="center">
                <Icon as={MdInsights} color="brand.500" w="24px" h="24px" me="7px" />
                <Text color={kpiData.growth >= 0 ? "green.500" : "red.500"} fontSize="sm" fontWeight="700">
                  {kpiData.growth >= 0 ? 'Growing' : 'Declining'}
                </Text>
              </Flex>
            </Flex>
            <Box h="240px" mt="auto">
              <LineAreaChart
                chartData={lineChartDataRevenue}
                chartOptions={lineChartOptionsRevenue}
              />
            </Box>
          </Flex>
        </Card>
      </SimpleGrid>

      {/* Enhanced AI Insights Section */}
      <Card p="6">
        <Flex direction="column">
          <Flex align="center" mb="20px">
            <IconBox
              w='56px'
              h='56px'
              bg='linear-gradient(90deg, #4318FF 0%, #9F7AEA 100%)'
              icon={<Icon w='32px' h='32px' as={MdInsights} color='white' />}
              me="20px"
            />
            <Box>
              <Text
                color={textColor}
                fontSize="xl"
                fontWeight="700"
                lineHeight="100%"
              >
                AI-Generated Insights
              </Text>
              <Text
                color="secondaryGray.600"
                fontSize="sm"
                fontWeight="500"
              >
                Powered by DeepSeek AI Analysis
              </Text>
            </Box>
          </Flex>

          <VStack spacing="4" align="stretch">
            <Card bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" p="4">
              <Text color="white" fontSize="sm" fontWeight="600">
                📊 Data Analysis: Analyzed {formatNumber(kpiData.totalRecords)} records from your Greek business data with {kpiData.columns.length} columns including Πρώτη πώληση and Νέα Πώληση.
              </Text>
            </Card>

            <Card bg="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" p="4">
              <Text color="white" fontSize="sm" fontWeight="600">
                {kpiData.growth >= 0 ? '📈' : '📉'} Growth Analysis: {kpiData.growth >= 0 ? 'Positive' : 'Negative'} growth of {formatPercentage(kpiData.growth)} detected between first and new sales.
                {kpiData.growth >= 0 ? 'Strong performance indicates business expansion.' : 'Consider reviewing sales strategies.'}
              </Text>
            </Card>

            <Card bg="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" p="4">
              <Text color="white" fontSize="sm" fontWeight="600">
                💰 Financial Insight: Total first sales: {formatCurrency(kpiData.firstSaleTotal)} across {kpiData.firstSaleCount} records.
                New sales total: {formatCurrency(kpiData.newSaleTotal)} across {kpiData.newSaleCount} records.
              </Text>
            </Card>
          </VStack>
        </Flex>
      </Card>

      {/* Agent Contributions Section */}
      <Card p="6">
        <Heading size="md" mb="6" color={textColor}>
          AI Agent Contributions
        </Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing="4">
          <Card bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)" p="4">
            <VStack align="start" spacing="3">
              <Flex align="center">
                <Icon as={MdBarChart} color="white" w="20px" h="20px" me="10px" />
                <Text color="white" fontSize="sm" fontWeight="700">
                  Executor Agent - Statistical Analysis
                </Text>
              </Flex>
              <Text color="white" fontSize="xs">
                {agentResults.executor?.status === 'completed'
                  ? `Analyzed ${formatNumber(kpiData.totalRecords)} records with ${formatPercentage(kpiData.growth)} growth rate detected.`
                  : agentResults.executor?.status === 'failed'
                  ? 'Analysis failed - statistical processing encountered errors.'
                  : 'Processing statistical analysis of your business data...'
                }
              </Text>
            </VStack>
          </Card>

          <Card bg="linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" p="4">
            <VStack align="start" spacing="3">
              <Flex align="center">
                <Icon as={MdAnalytics} color="white" w="20px" h="20px" me="10px" />
                <Text color="white" fontSize="sm" fontWeight="700">
                  Data Mining Agent - Pattern Discovery
                </Text>
              </Flex>
              <Text color="white" fontSize="xs">
                {agentResults.datafining?.status === 'completed'
                  ? `Discovered patterns in ${kpiData.columns.length} data columns including Greek business metrics.`
                  : agentResults.datafining?.status === 'failed'
                  ? 'Pattern discovery failed - data mining encountered errors.'
                  : 'Mining patterns and correlations in your data...'
                }
              </Text>
            </VStack>
          </Card>

          <Card bg="linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" p="4">
            <VStack align="start" spacing="3">
              <Flex align="center">
                <Icon as={MdInsights} color="white" w="20px" h="20px" me="10px" />
                <Text color="white" fontSize="sm" fontWeight="700">
                  Storyteller Agent - Business Narrative
                </Text>
              </Flex>
              <Text color="white" fontSize="xs">
                {agentResults.storyteller?.status === 'completed'
                  ? `Generated business insights from ${formatNumber(kpiData.totalRecords)} Greek sales records.`
                  : agentResults.storyteller?.status === 'failed'
                  ? 'Narrative generation failed - storytelling encountered errors.'
                  : 'Creating business narrative and strategic recommendations...'
                }
              </Text>
            </VStack>
          </Card>
        </SimpleGrid>
      </Card>
    </VStack>
  );
};

export default AnalysisResults;
