import React from 'react';
import {
  Box,
  SimpleGrid,
  <PERSON>ing,
  VStack,
  useColorModeValue,
  Icon,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';

// Horizon UI Components
import MiniStatistics from 'components/card/MiniStatistics.js';
import IconBox from 'components/icons/IconBox.js';

// New components for redesigned layout
import InsightReport from './InsightReport';
import DynamicCharts from './DynamicCharts';

// Icons
import {
  MdAttachMoney,
  MdTrendingUp,
  MdPeople,
  MdAccountBalance,
} from 'react-icons/md';

const AnalysisResults = ({ agentResults, data }) => {
  const textColor = useColorModeValue('secondaryGray.900', 'white');

  // Extract real data metrics from your actual business template
  const getKPIData = () => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return {
        totalRecords: 0,
        columns: [],
        uniqueClients: 0,
        uniqueRecipients: 0,
        uniquePrincipals: 0,
        uniqueSalespeople: 0,
        leadSources: [],
        dataCompleteness: 0
      };
    }

    const totalRecords = data.length;
    const columns = Object.keys(data[0]);

    // Analyze your actual business template columns
    const clients = new Set(data.map(row => row['Συναλλασσόμενος']).filter(Boolean));
    const recipients = new Set(data.map(row => row['Παραλήπτης']).filter(Boolean));
    const principals = new Set(data.map(row => row['Εντολέας']).filter(Boolean));
    const salespeople = new Set(data.map(row => row['Πωλητής']).filter(Boolean));
    const leadSources = new Set(data.map(row => row['Πηγή lead']).filter(Boolean));

    // Calculate data completeness
    let filledFields = 0;
    let totalFields = 0;

    data.forEach(row => {
      Object.values(row).forEach(value => {
        totalFields++;
        if (value !== null && value !== undefined && value !== '') {
          filledFields++;
        }
      });
    });

    const dataCompleteness = totalFields > 0 ? (filledFields / totalFields) * 100 : 0;

    return {
      totalRecords,
      columns,
      uniqueClients: clients.size,
      uniqueRecipients: recipients.size,
      uniquePrincipals: principals.size,
      uniqueSalespeople: salespeople.size,
      uniqueLeadSources: leadSources.size,
      dataCompleteness,
      leadSources: Array.from(leadSources),
      topClient: data.reduce((acc, row) => {
        const client = row['Συναλλασσόμενος'];
        if (client) {
          acc[client] = (acc[client] || 0) + 1;
        }
        return acc;
      }, {}),
      businessType: recipients.size > 0 && principals.size > 0 ? 'healthcare' : 'business'
    };
  };

  const kpiData = getKPIData();

  // Format numbers for display
  const formatCurrency = (value) => {
    const safeValue = isNaN(value) || value === null || value === undefined ? 0 : Number(value);
    return new Intl.NumberFormat('el-GR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(safeValue);
  };

  const formatNumber = (value) => {
    const safeValue = isNaN(value) || value === null || value === undefined ? 0 : Number(value);
    return new Intl.NumberFormat('el-GR').format(Math.round(safeValue));
  };

  const formatPercentage = (value) => {
    const safeValue = isNaN(value) || value === null || value === undefined ? 0 : Number(value);
    return `${safeValue >= 0 ? '+' : ''}${safeValue.toFixed(1)}%`;
  };

  const hasCompletedAgents = Object.values(agentResults).some(
    state => state.status === 'completed'
  );

  if (!hasCompletedAgents) {
    return (
      <Alert status="info">
        <AlertIcon />
        Analysis results will appear here once AI agents complete their processing.
      </Alert>
    );
  }



  return (
    <VStack spacing="8" align="stretch">
      {/* KPI Dashboard - Keep this in grid format as requested */}
      <Box>
        <Heading size="lg" mb="6" color={textColor}>
          📊 Performance Dashboard
        </Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing="6">
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #4318FF 0%, #9F7AEA 100%)'
                icon={<Icon w='32px' h='32px' as={MdPeople} color='white' />}
              />
            }
            name='Unique Clients'
            value={formatNumber(kpiData.uniqueClients)}
            growth={`${kpiData.businessType === 'healthcare' ? 'Patients' : 'Business clients'}`}
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #FF6B6B 0%, #4ECDC4 100%)'
                icon={<Icon w='32px' h='32px' as={MdTrendingUp} color='white' />}
              />
            }
            name='Active Salespeople'
            value={formatNumber(kpiData.uniqueSalespeople)}
            growth={`${kpiData.uniqueLeadSources} lead sources`}
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #FFE066 0%, #FF6B6B 100%)'
                icon={<Icon w='32px' h='32px' as={MdAccountBalance} color='white' />}
              />
            }
            name='Data Quality'
            value={`${kpiData.dataCompleteness.toFixed(1)}%`}
            growth={kpiData.dataCompleteness > 80 ? 'Excellent' : kpiData.dataCompleteness > 60 ? 'Good' : 'Needs improvement'}
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #4ECDC4 0%, #44A08D 100%)'
                icon={<Icon w='32px' h='32px' as={MdAttachMoney} color='white' />}
              />
            }
            name='Total Records'
            value={formatNumber(kpiData.totalRecords)}
            growth={`${kpiData.columns.length} data columns`}
          />
        </SimpleGrid>
      </Box>

      {/* Charts Section - Keep in grid format */}
      <Box>
        <Heading size="lg" mb="6" color={textColor}>
          📈 Data Visualizations
        </Heading>
        <DynamicCharts data={data} agentResults={agentResults} />
      </Box>

      {/* Analysis Report - New container-based layout */}
      <Box>
        <Heading size="lg" mb="6" color={textColor}>
          🔍 Comprehensive Analysis Report
        </Heading>
        <InsightReport
          agentResults={agentResults}
          data={data}
          analysisType={data && data.length > 0 ? 'business' : 'unknown'}
        />
      </Box>




    </VStack>
  );
};

export default AnalysisResults;
