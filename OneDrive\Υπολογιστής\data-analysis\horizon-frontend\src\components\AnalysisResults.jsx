import React from 'react';
import {
  Box,
  SimpleGrid,
  Heading,
  VStack,
  useColorModeValue,
  Icon,
} from '@chakra-ui/react';

// Horizon UI Components
import MiniStatistics from 'components/card/MiniStatistics.js';
import IconBox from 'components/icons/IconBox.js';

// New components for redesigned layout
import InsightReport from './InsightReport';
import DynamicCharts from './DynamicCharts';

// Icons
import {
  Md<PERSON><PERSON>ch<PERSON>oney,
  MdTrendingUp,
  MdPeople,
  MdAccountBalance,
} from 'react-icons/md';

const AnalysisResults = ({ agentResults, data }) => {
  const textColor = useColorModeValue('secondaryGray.900', 'white');

  // Extract real data from agent results and uploaded file
  const getKPIData = () => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return {
        totalRecords: 0,
        columns: [],
        firstSaleTotal: 0,
        newSaleTotal: 0,
        avgFirstSale: 0,
        avgNewSale: 0,
        growth: 0,
        currency: 'EUR'
      };
    }

    const totalRecords = data.length;
    const columns = Object.keys(data[0]);

    // Extract Greek column data - handle different possible column names
    const firstSaleData = data.map(row => {
      const value = row['Πρώτη πώληση'] || row['First Sale'] || row['first_sale'] || 0;
      return parseFloat(value) || 0;
    }).filter(val => val > 0);

    const newSaleData = data.map(row => {
      const value = row['Νέα Πώληση'] || row['New Sale'] || row['new_sale'] || 0;
      return parseFloat(value) || 0;
    }).filter(val => val > 0);

    const firstSaleTotal = firstSaleData.reduce((sum, val) => sum + val, 0);
    const newSaleTotal = newSaleData.reduce((sum, val) => sum + val, 0);
    const avgFirstSale = firstSaleData.length > 0 ? firstSaleTotal / firstSaleData.length : 0;
    const avgNewSale = newSaleData.length > 0 ? newSaleTotal / newSaleData.length : 0;

    const growth = avgFirstSale > 0 ? ((avgNewSale - avgFirstSale) / avgFirstSale * 100) : 0;

    return {
      totalRecords,
      columns,
      firstSaleTotal,
      newSaleTotal,
      avgFirstSale,
      avgNewSale,
      growth,
      firstSaleCount: firstSaleData.length,
      newSaleCount: newSaleData.length,
      currency: 'EUR'
    };
  };

  const kpiData = getKPIData();

  // Format numbers for display
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('el-GR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const formatNumber = (value) => {
    return new Intl.NumberFormat('el-GR').format(Math.round(value));
  };

  const formatPercentage = (value) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  const hasCompletedAgents = Object.values(agentResults).some(
    state => state.status === 'completed'
  );

  if (!hasCompletedAgents) {
    return (
      <Alert status="info">
        <AlertIcon />
        Analysis results will appear here once AI agents complete their processing.
      </Alert>
    );
  }



  return (
    <VStack spacing="8" align="stretch">
      {/* KPI Dashboard - Keep this in grid format as requested */}
      <Box>
        <Heading size="lg" mb="6" color={textColor}>
          📊 Performance Dashboard
        </Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing="6">
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #4318FF 0%, #9F7AEA 100%)'
                icon={<Icon w='32px' h='32px' as={MdAttachMoney} color='white' />}
              />
            }
            name='Πρώτη Πώληση (Total)'
            value={formatCurrency(kpiData.firstSaleTotal)}
            growth={`${kpiData.firstSaleCount} records`}
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #FF6B6B 0%, #4ECDC4 100%)'
                icon={<Icon w='32px' h='32px' as={MdTrendingUp} color='white' />}
              />
            }
            name='Νέα Πώληση (Total)'
            value={formatCurrency(kpiData.newSaleTotal)}
            growth={`${kpiData.newSaleCount} records`}
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #FFE066 0%, #FF6B6B 100%)'
                icon={<Icon w='32px' h='32px' as={MdPeople} color='white' />}
              />
            }
            name='Growth Rate'
            value={formatPercentage(kpiData.growth)}
            growth={kpiData.growth >= 0 ? 'Positive' : 'Negative'}
          />
          <MiniStatistics
            startContent={
              <IconBox
                w='56px'
                h='56px'
                bg='linear-gradient(90deg, #4ECDC4 0%, #44A08D 100%)'
                icon={<Icon w='32px' h='32px' as={MdAccountBalance} color='white' />}
              />
            }
            name='Total Records'
            value={formatNumber(kpiData.totalRecords)}
            growth={`${kpiData.columns.length} columns`}
          />
        </SimpleGrid>
      </Box>

      {/* Charts Section - Keep in grid format */}
      <Box>
        <Heading size="lg" mb="6" color={textColor}>
          📈 Data Visualizations
        </Heading>
        <DynamicCharts data={data} agentResults={agentResults} />
      </Box>

      {/* Analysis Report - New container-based layout */}
      <Box>
        <Heading size="lg" mb="6" color={textColor}>
          🔍 Comprehensive Analysis Report
        </Heading>
        <InsightReport
          agentResults={agentResults}
          data={data}
          analysisType={data && data.length > 0 ? 'business' : 'unknown'}
        />
      </Box>




    </VStack>
  );
};

export default AnalysisResults;
