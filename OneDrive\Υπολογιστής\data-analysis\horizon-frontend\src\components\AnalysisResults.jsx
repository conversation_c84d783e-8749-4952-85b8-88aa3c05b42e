import React from 'react';
import {
  Box,
  SimpleGrid,
  Text,
  Heading,
  VStack,
  HStack,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  useColorModeValue,
  Alert,
  AlertIcon,
} from '@chakra-ui/react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

const AnalysisResults = ({ agentResults, data }) => {
  const bgColor = useColorModeValue('white', 'navy.800');
  const textColor = useColorModeValue('secondaryGray.900', 'white');
  
  // Mock data for demonstration - this would come from agent results
  const mockSalesData = [
    { month: 'Jan', sales: 4000, profit: 2400, expenses: 1600 },
    { month: 'Feb', sales: 3000, profit: 1398, expenses: 1602 },
    { month: 'Mar', sales: 2000, profit: 800, expenses: 1200 },
    { month: 'Apr', sales: 2780, profit: 1908, expenses: 872 },
    { month: 'May', sales: 1890, profit: 800, expenses: 1090 },
    { month: 'Jun', sales: 2390, profit: 1800, expenses: 590 },
  ];

  const mockCategoryData = [
    { name: 'Product A', value: 400, color: '#0088FE' },
    { name: 'Product B', value: 300, color: '#00C49F' },
    { name: 'Product C', value: 300, color: '#FFBB28' },
    { name: 'Product D', value: 200, color: '#FF8042' },
  ];

  const mockKPIs = [
    {
      label: 'Total Revenue',
      value: '$124,563',
      change: '+12.5%',
      trend: 'increase',
    },
    {
      label: 'Growth Rate',
      value: '23.8%',
      change: '+2.1%',
      trend: 'increase',
    },
    {
      label: 'Customer Acquisition',
      value: '1,247',
      change: '-3.2%',
      trend: 'decrease',
    },
    {
      label: 'Profit Margin',
      value: '34.2%',
      change: '+5.4%',
      trend: 'increase',
    },
  ];

  const hasCompletedAgents = Object.values(agentResults).some(
    state => state.status === 'completed'
  );

  if (!hasCompletedAgents) {
    return (
      <Alert status="info">
        <AlertIcon />
        Analysis results will appear here once AI agents complete their processing.
      </Alert>
    );
  }

  return (
    <VStack spacing="6" align="stretch">
      {/* KPI Cards */}
      <Box>
        <Heading size="md" mb="4" color={textColor}>
          Key Performance Indicators
        </Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 4 }} spacing="4">
          {mockKPIs.map((kpi, index) => (
            <Box
              key={index}
              bg={bgColor}
              p="4"
              borderRadius="12px"
              border="1px solid"
              borderColor="gray.200"
              boxShadow="sm"
            >
              <Stat>
                <StatLabel fontSize="sm" color="gray.500">
                  {kpi.label}
                </StatLabel>
                <StatNumber fontSize="2xl" color={textColor}>
                  {kpi.value}
                </StatNumber>
                <StatHelpText>
                  <StatArrow type={kpi.trend} />
                  {kpi.change}
                </StatHelpText>
              </Stat>
            </Box>
          ))}
        </SimpleGrid>
      </Box>

      {/* Charts Grid */}
      <SimpleGrid columns={{ base: 1, lg: 2 }} spacing="6">
        {/* Revenue Trend Chart */}
        <Box
          bg={bgColor}
          p="6"
          borderRadius="12px"
          border="1px solid"
          borderColor="gray.200"
        >
          <Heading size="sm" mb="4" color={textColor}>
            Revenue & Profit Trend
          </Heading>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={mockSalesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="sales"
                stroke="#0088FE"
                strokeWidth={3}
                name="Sales"
              />
              <Line
                type="monotone"
                dataKey="profit"
                stroke="#00C49F"
                strokeWidth={3}
                name="Profit"
              />
            </LineChart>
          </ResponsiveContainer>
        </Box>

        {/* Category Distribution */}
        <Box
          bg={bgColor}
          p="6"
          borderRadius="12px"
          border="1px solid"
          borderColor="gray.200"
        >
          <Heading size="sm" mb="4" color={textColor}>
            Category Distribution
          </Heading>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={mockCategoryData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {mockCategoryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </Box>

        {/* Sales vs Expenses */}
        <Box
          bg={bgColor}
          p="6"
          borderRadius="12px"
          border="1px solid"
          borderColor="gray.200"
        >
          <Heading size="sm" mb="4" color={textColor}>
            Sales vs Expenses
          </Heading>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={mockSalesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="sales" fill="#0088FE" name="Sales" />
              <Bar dataKey="expenses" fill="#FF8042" name="Expenses" />
            </BarChart>
          </ResponsiveContainer>
        </Box>

        {/* Area Chart for Cumulative Growth */}
        <Box
          bg={bgColor}
          p="6"
          borderRadius="12px"
          border="1px solid"
          borderColor="gray.200"
        >
          <Heading size="sm" mb="4" color={textColor}>
            Cumulative Growth
          </Heading>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={mockSalesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Area
                type="monotone"
                dataKey="profit"
                stackId="1"
                stroke="#00C49F"
                fill="#00C49F"
                fillOpacity={0.6}
              />
            </AreaChart>
          </ResponsiveContainer>
        </Box>
      </SimpleGrid>

      {/* Agent Insights */}
      {agentResults.expresser?.result && (
        <Box
          bg="blue.50"
          p="4"
          borderRadius="12px"
          border="1px solid"
          borderColor="blue.200"
        >
          <Heading size="sm" mb="2" color="blue.800">
            AI-Generated Insights
          </Heading>
          <Text fontSize="sm" color="blue.700">
            {agentResults.expresser.result.insights || 
             "The data shows strong performance in Q1 with increasing profit margins. Product A dominates market share with 33% of total sales."}
          </Text>
        </Box>
      )}
    </VStack>
  );
};

export default AnalysisResults;
