const express = require('express');
const { v4: uuidv4 } = require('uuid');
const BusinessAnalyzer = require('../services/BusinessAnalyzer');
const DeepSeekAPI = require('../services/DeepSeekAPI');
const KPIExtractor = require('../utils/kpiExtractor');
const ReportGenerator = require('../utils/reportGenerator');
const { validateAnalysisRequest, validateUUIDParam, validateReportRequest } = require('../middleware/validation');
const { authenticate } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');
const winston = require('winston');

const router = express.Router();

// Initialize services
const businessAnalyzer = new BusinessAnalyzer();
const deepSeekAPI = new DeepSeekAPI();
const kpiExtractor = new KPIExtractor();
const reportGenerator = new ReportGenerator();

// Logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/analysis-routes.log' })
  ]
});

// Start new analysis
router.post('/',
  authenticate(),
  validateAnalysisRequest(),
  asyncHandler(async (req, res) => {
    try {
      const { fileId, analysisType, options } = req.body;
      const analysisId = uuidv4();

      logger.info('Analysis started:', {
        analysisId,
        fileId,
        analysisType,
        userId: req.user?.id
      });

      // In a real implementation, this would fetch data from the database using fileId
      // For now, we'll use mock data
      const mockData = [
        { date: '2024-01-01', revenue: 100000, costs: 60000, customers: 1500 },
        { date: '2024-02-01', revenue: 110000, costs: 62000, customers: 1600 },
        { date: '2024-03-01', revenue: 105000, costs: 58000, customers: 1550 },
        { date: '2024-04-01', revenue: 120000, costs: 65000, customers: 1700 },
        { date: '2024-05-01', revenue: 130000, costs: 67000, customers: 1800 }
      ];

      // Start analysis (this would typically be asynchronous)
      const analysisResult = await businessAnalyzer.analyzeBusinessData(mockData, {
        analysisType,
        ...options
      });

      // Store analysis in memory (in production, this would be in database)
      req.app.locals.analyses = req.app.locals.analyses || {};
      req.app.locals.analyses[analysisId] = {
        id: analysisId,
        fileId,
        status: 'completed',
        analysisType,
        data: mockData,
        result: analysisResult,
        createdAt: new Date().toISOString(),
        completedAt: new Date().toISOString(),
        userId: req.user?.id
      };

      logger.info('Analysis completed:', {
        analysisId,
        processingTime: analysisResult.processingTime
      });

      res.status(201).json({
        success: true,
        message: 'Analysis completed successfully',
        data: {
          analysisId,
          status: 'completed',
          analysisType,
          fileId,
          createdAt: new Date().toISOString(),
          summary: {
            totalRecords: analysisResult.dataMetadata.totalRecords,
            keyInsights: analysisResult.insights.automaticInsights?.slice(0, 3) || [],
            topMetrics: Object.keys(analysisResult.businessMetrics).slice(0, 5),
            processingTime: analysisResult.processingTime
          }
        }
      });
    } catch (error) {
      logger.error('Analysis failed:', error);
      throw error;
    }
  })
);

// Get analysis results
router.get('/:analysisId',
  authenticate(),
  validateUUIDParam('analysisId'),
  asyncHandler(async (req, res) => {
    try {
      const { analysisId } = req.params;
      const analyses = req.app.locals.analyses || {};

      if (!analyses[analysisId]) {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Analysis not found',
            type: 'NOT_FOUND'
          }
        });
      }

      const analysis = analyses[analysisId];

      logger.info('Analysis results requested:', {
        analysisId,
        userId: req.user?.id
      });

      res.json({
        success: true,
        data: {
          analysisId: analysis.id,
          status: analysis.status,
          analysisType: analysis.analysisType,
          fileId: analysis.fileId,
          createdAt: analysis.createdAt,
          completedAt: analysis.completedAt,
          results: analysis.result
        }
      });
    } catch (error) {
      logger.error('Failed to get analysis results:', error);
      throw error;
    }
  })
);

// List user's analyses
router.get('/',
  authenticate(),
  asyncHandler(async (req, res) => {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        analysisType,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const analyses = req.app.locals.analyses || {};
      const userId = req.user?.id;

      // Filter analyses for the current user
      const userAnalyses = Object.values(analyses).filter(analysis => 
        analysis.userId === userId
      );

      // Apply filters
      let filteredAnalyses = userAnalyses;
      
      if (status) {
        filteredAnalyses = filteredAnalyses.filter(analysis => analysis.status === status);
      }
      
      if (analysisType) {
        filteredAnalyses = filteredAnalyses.filter(analysis => analysis.analysisType === analysisType);
      }

      // Sort
      filteredAnalyses.sort((a, b) => {
        const aVal = a[sortBy];
        const bVal = b[sortBy];
        return sortOrder === 'desc' ? 
          (bVal > aVal ? 1 : -1) : 
          (aVal > bVal ? 1 : -1);
      });

      // Paginate
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + parseInt(limit);
      const paginatedAnalyses = filteredAnalyses.slice(startIndex, endIndex);

      logger.info('Analyses list requested:', {
        userId,
        page,
        limit,
        total: filteredAnalyses.length
      });

      res.json({
        success: true,
        data: {
          analyses: paginatedAnalyses.map(analysis => ({
            analysisId: analysis.id,
            fileId: analysis.fileId,
            status: analysis.status,
            analysisType: analysis.analysisType,
            createdAt: analysis.createdAt,
            completedAt: analysis.completedAt,
            summary: {
              recordCount: analysis.result?.dataMetadata?.totalRecords || 0,
              insightCount: analysis.result?.insights?.automaticInsights?.length || 0
            }
          })),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: filteredAnalyses.length,
            pages: Math.ceil(filteredAnalyses.length / limit)
          }
        }
      });
    } catch (error) {
      logger.error('Failed to list analyses:', error);
      throw error;
    }
  })
);

// Get analysis status
router.get('/status/:sessionId',
  authenticate(),
  validateUUIDParam('sessionId'),
  asyncHandler(async (req, res) => {
    try {
      const { sessionId } = req.params;
      const sessions = req.app.locals.sessions || {};
      
      if (!sessions[sessionId]) {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Session not found',
            type: 'NOT_FOUND'
          }
        });
      }
      
      const session = sessions[sessionId];
      const analysis = session.analysis;
      
      if (!analysis) {
        return res.json({
          success: true,
          data: {
            sessionId,
            status: 'not_started',
            message: 'Analysis has not been started yet'
          }
        });
      }      
      const response = {
        sessionId,
        analysisId: analysis.analysisId,
        status: analysis.status,
        startTime: analysis.startTime,
        endTime: analysis.endTime || null
      };
      
      if (analysis.status === 'failed') {
        response.error = analysis.error;
      }
      
      if (analysis.status === 'completed') {
        response.summary = {
          totalAgents: analysis.results?.phases?.peer ? Object.keys(analysis.results.phases.peer).length : 0,
          qualityScore: analysis.results?.phases?.synthesis?.executionSummary?.qualityScore,
          confidence: analysis.results?.phases?.synthesis?.executionSummary?.confidence
        };
      }
      
      res.json({
        success: true,
        data: response
      });
      
    } catch (error) {
      logger.error('Analysis status error:', error);
      throw error;
    }
  })
);

// Get analysis results by session
router.get('/results/:sessionId',
  authenticate(),
  validateUUIDParam('sessionId'),
  asyncHandler(async (req, res) => {
    try {
      const { sessionId } = req.params;
      const sessions = req.app.locals.sessions || {};
      
      if (!sessions[sessionId]) {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Session not found',
            type: 'NOT_FOUND'
          }
        });
      }
      
      const session = sessions[sessionId];
      const analysis = session.analysis;
      
      if (!analysis || analysis.status !== 'completed') {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Analysis not completed',
            type: 'INVALID_STATE',
            status: analysis?.status || 'not_started'
          }
        });
      }

      res.json({
        success: true,
        data: {
          sessionId,
          analysisId: analysis.analysisId,
          results: analysis.results,
          metadata: {
            fileName: session.fileName,
            uploadTime: session.uploadTime,
            analysisTime: analysis.endTime,
            duration: new Date(analysis.endTime) - new Date(analysis.startTime)
          }
        }
      });
      
    } catch (error) {
      logger.error('Analysis results error:', error);
      throw error;
    }
  })
);

// Get specific analysis component
router.get('/results/:sessionId/:component',
  authenticate(),
  validateUUIDParam('sessionId'),
  asyncHandler(async (req, res) => {
    try {
      const { sessionId, component } = req.params;
      const sessions = req.app.locals.sessions || {};
      
      if (!sessions[sessionId]) {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Session not found',
            type: 'NOT_FOUND'
          }
        });
      }
      
      const session = sessions[sessionId];
      const analysis = session.analysis;
      
      if (!analysis || analysis.status !== 'completed') {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Analysis not completed',
            type: 'INVALID_STATE',
            status: analysis?.status || 'not_started'
          }
        });
      }
      
      const results = analysis.results;
      let componentData;
      
      switch (component) {
        case 'business-context':
          componentData = results.phases?.peer?.plan?.businessContext;
          break;
        case 'kpis':
          componentData = results.phases?.peer?.plan?.kpis;
          break;
        case 'forecasts':
          componentData = results.phases?.peer?.execute?.forecasts;
          break;
        case 'visualizations':
          componentData = results.phases?.peer?.express?.charts;
          break;
        case 'recommendations':
          componentData = results.phases?.doe?.storytelling?.recommendations;
          break;
        case 'insights':
          componentData = results.phases?.synthesis?.keyInsights;
          break;
        default:
          return res.status(400).json({
            success: false,
            error: {
              message: 'Invalid component requested',
              type: 'INVALID_PARAMETER'
            }
          });
      }
      
      if (!componentData) {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Component data not found',
            type: 'NOT_FOUND'
          }
        });
      }
      
      res.json({
        success: true,
        data: {
          sessionId,
          component,
          data: componentData,
          timestamp: new Date().toISOString()
        }
      });
      
    } catch (error) {
      logger.error('Component data error:', error);
      throw error;
    }
  })
);

// Generate custom report
// Generate custom report
router.post('/:analysisId/report',
  authenticate(),
  validateUUIDParam('analysisId'),
  validateReportRequest(),
  asyncHandler(async (req, res) => {
    try {
      const { analysisId } = req.params;
      const { reportType, format, sections, outputPath } = req.body;
      
      const analyses = req.app.locals.analyses || {};
      
      if (!analyses[analysisId]) {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Analysis not found',
            type: 'NOT_FOUND'
          }
        });
      }

      const analysis = analyses[analysisId];
      
      if (analysis.status !== 'completed') {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Analysis not completed',
            type: 'INVALID_STATE'
          }
        });
      }

      logger.info('Report generation requested:', {
        analysisId,
        reportType,
        format,
        userId: req.user?.id
      });

      const report = await reportGenerator.generateReport(analysis.result, {
        reportType,
        format,
        customSections: sections,
        outputPath
      });

      res.json({
        success: true,
        message: 'Report generated successfully',
        data: {
          analysisId,
          reportType,
          format,
          generatedAt: new Date().toISOString(),
          ...(outputPath ? { filePath: report.filePath } : { content: report.content })
        }
      });
    } catch (error) {
      logger.error('Report generation failed:', error);
      throw error;
    }
  })
);

// Delete analysis
router.delete('/:analysisId',
  authenticate(),
  validateUUIDParam('analysisId'),
  asyncHandler(async (req, res) => {
    try {
      const { analysisId } = req.params;
      const analyses = req.app.locals.analyses || {};

      if (!analyses[analysisId]) {
        return res.status(404).json({
          success: false,
          error: {
            message: 'Analysis not found',
            type: 'NOT_FOUND'
          }
        });
      }

      const analysis = analyses[analysisId];
      
      // Check if user owns this analysis
      if (analysis.userId !== req.user?.id) {
        return res.status(403).json({
          success: false,
          error: {
            message: 'Access denied',
            type: 'FORBIDDEN'
          }
        });
      }

      delete analyses[analysisId];

      logger.info('Analysis deleted:', {
        analysisId,
        userId: req.user?.id
      });

      res.json({
        success: true,
        message: 'Analysis deleted successfully',
        data: { analysisId }
      });
    } catch (error) {
      logger.error('Failed to delete analysis:', error);
      throw error;
    }
  })
);

module.exports = router;