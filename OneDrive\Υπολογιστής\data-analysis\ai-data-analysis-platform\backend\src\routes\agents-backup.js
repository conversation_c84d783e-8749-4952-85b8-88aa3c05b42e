const express = require('express');
const router = express.Router();
// const { authenticate, requireRole } = require('../middleware/auth');
// const { validateRequest } = require('../middleware/validation');
const ExecutorAgent = require('../agents/ExecutorAgent');
const ExpresserAgent = require('../agents/ExpresserAgent');
const ReviewerAgent = require('../agents/ReviewerAgent');
const DataFiningAgent = require('../agents/DataFiningAgent');
const StorytellerAgent = require('../agents/StorytellerAgent');
const { AppError } = require('../middleware/errorHandler');
const { body, param, query } = require('express-validator');
// const Analysis = require('../config/database').Analysis;

// Validation schemas
const agentExecutionValidation = [
  body('type').isIn(['executor', 'expresser', 'reviewer', 'datafining', 'storyteller'])
    .withMessage('Invalid agent type'),
  body('data').notEmpty().withMessage('Data is required'),
  body('context').optional().isObject().withMessage('Context must be an object'),
  body('options').optional().isObject().withMessage('Options must be an object')
];

const agentStatusValidation = [
  param('agentId').isMongoId().withMessage('Invalid agent ID')
];

const agentListValidation = [
  query('type').optional().isIn(['executor', 'expresser', 'reviewer', 'datafining', 'storyteller'])
    .withMessage('Invalid agent type filter'),
  query('status').optional().isIn(['pending', 'running', 'completed', 'failed'])
    .withMessage('Invalid status filter'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
];

// Agent factory
const createAgent = (type, analysisId) => {
  switch (type) {
    case 'executor':
      return new ExecutorAgent(analysisId);
    case 'expresser':
      return new ExpresserAgent(analysisId);
    case 'reviewer':
      return new ReviewerAgent(analysisId);
    case 'datafining':
      return new DataFiningAgent(analysisId);
    case 'storyteller':
      return new StorytellerAgent(analysisId);
    default:
      throw new AppError('Invalid agent type', 400);
  }
};

/**
 * @route POST /api/agents/execute
 * @desc Execute an agent task
 * @access Public (for testing)
 */
router.post('/execute', 
  async (req, res, next) => {
    try {
      const { type, data, context = {}, options = {} } = req.body;
      const userId = 'test-user'; // Mock user for testing/**
 * @route POST /api/agents/execute
 * @desc Execute an agent task
 * @access Public (for testing)
 */
router.post('/execute', 
  async (req, res, next) => {
    try {
      const { type, data, context = {}, options = {} } = req.body;
      const userId = 'test-user'; // Mock user for testing

      // For testing - simple validation
      if (!type || !data) {
        return res.status(400).json({
          success: false,
          error: 'Type and data are required'
        });
      }

      // Create and execute agent
      const agent = createAgent(type, null); // No analysis ID for testing
      
      // Set context and options
      agent.setContext(context);
      agent.setOptions(options);

      // Execute agent
      const result = await agent.execute(data);

      res.json({
        success: true,
        data: {
          type,
          status: 'completed',
          result,
          executedAt: new Date()
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route POST /api/agents/execute/async
 * @desc Execute an agent task asynchronously
 * @access Private
 */
router.post('/execute/async', 
  // authenticate(), 
  // requireRole(['admin', 'analyst']),
  agentExecutionValidation,
  validateRequest,
  async (req, res, next) => {
    try {
      const { type, data, context = {}, options = {} } = req.body;
      const userId = req.user.id;

      // Create analysis record
      const analysis = new Analysis({
        user: userId,
        type: 'agent_execution',
        status: 'pending',
        metadata: {
          agentType: type,
          context,
          options,
          createdAt: new Date()
        }
      });
      await analysis.save();

      // Execute agent asynchronously (don't wait)
      setImmediate(async () => {
        try {
          // Update status to running
          analysis.status = 'running';
          analysis.metadata.startedAt = new Date();
          await analysis.save();

          const agent = createAgent(type, analysis._id);
          agent.setContext(context);
          agent.setOptions(options);

          const result = await agent.execute(data);

          // Update with success
          analysis.status = 'completed';
          analysis.result = result;
          analysis.metadata.completedAt = new Date();
          analysis.metadata.duration = Date.now() - analysis.metadata.startedAt.getTime();
          await analysis.save();

        } catch (error) {
          // Update with error
          analysis.status = 'failed';
          analysis.error = {
            message: error.message,
            stack: error.stack,
            code: error.code || 'AGENT_EXECUTION_ERROR'
          };
          analysis.metadata.failedAt = new Date();
          await analysis.save();
        }
      });

      res.json({
        success: true,
        data: {
          agentId: analysis._id,
          type,
          status: 'pending',
          message: 'Agent execution started asynchronously'
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route GET /api/agents/:agentId/status
 * @desc Get agent execution status
 * @access Private
 */
router.get('/:agentId/status',
  // authenticate(),
  agentStatusValidation,
  validateRequest,
  async (req, res, next) => {
    try {
      const { agentId } = req.params;
      const userId = req.user.id;

      const analysis = await Analysis.findOne({
        _id: agentId,
        user: userId
      });

      if (!analysis) {
        return res.status(404).json({
          success: false,
          message: 'Agent execution not found'
        });
      }

      res.json({
        success: true,
        data: {
          agentId: analysis._id,
          type: analysis.metadata.agentType,
          status: analysis.status,
          progress: analysis.progress || 0,
          result: analysis.result,
          error: analysis.error,
          metadata: analysis.metadata
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route GET /api/agents/:agentId/result
 * @desc Get agent execution result
 * @access Private
 */
router.get('/:agentId/result',
  // authenticate(),
  agentStatusValidation,
  validateRequest,
  async (req, res, next) => {
    try {
      const { agentId } = req.params;
      const userId = req.user.id;

      const analysis = await Analysis.findOne({
        _id: agentId,
        user: userId,
        status: 'completed'
      });

      if (!analysis) {
        return res.status(404).json({
          success: false,
          message: 'Completed agent execution not found'
        });
      }

      res.json({
        success: true,
        data: {
          agentId: analysis._id,
          type: analysis.metadata.agentType,
          result: analysis.result,
          metadata: analysis.metadata
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route GET /api/agents
 * @desc List agent executions
 * @access Private
 */
router.get('/',
  // authenticate(),
  agentListValidation,
  validateRequest,
  async (req, res, next) => {
    try {
      const { 
        type, 
        status, 
        page = 1, 
        limit = 20 
      } = req.query;
      const userId = req.user.id;

      // Build filter
      const filter = {
        user: userId,
        type: 'agent_execution'
      };

      if (type) {
        filter['metadata.agentType'] = type;
      }

      if (status) {
        filter.status = status;
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Execute query
      const [executions, total] = await Promise.all([
        Analysis.find(filter)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(parseInt(limit))
          .select('_id status progress error metadata createdAt updatedAt'),
        Analysis.countDocuments(filter)
      ]);

      const totalPages = Math.ceil(total / limit);

      res.json({
        success: true,
        data: {
          executions: executions.map(exec => ({
            agentId: exec._id,
            type: exec.metadata.agentType,
            status: exec.status,
            progress: exec.progress || 0,
            hasError: !!exec.error,
            createdAt: exec.createdAt,
            updatedAt: exec.updatedAt,
            duration: exec.metadata.duration
          })),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1
          }
        }
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route DELETE /api/agents/:agentId
 * @desc Delete agent execution
 * @access Private
 */
router.delete('/:agentId',
  // authenticate(),
  agentStatusValidation,
  validateRequest,
  async (req, res, next) => {
    try {
      const { agentId } = req.params;
      const userId = req.user.id;

      const analysis = await Analysis.findOneAndDelete({
        _id: agentId,
        user: userId
      });

      if (!analysis) {
        return res.status(404).json({
          success: false,
          message: 'Agent execution not found'
        });
      }

      res.json({
        success: true,
        message: 'Agent execution deleted successfully'
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route POST /api/agents/:agentId/cancel
 * @desc Cancel running agent execution
 * @access Private
 */
router.post('/:agentId/cancel',
  // authenticate(),
  agentStatusValidation,
  validateRequest,
  async (req, res, next) => {
    try {
      const { agentId } = req.params;
      const userId = req.user.id;

      const analysis = await Analysis.findOne({
        _id: agentId,
        user: userId,
        status: { $in: ['pending', 'running'] }
      });

      if (!analysis) {
        return res.status(404).json({
          success: false,
          message: 'Running agent execution not found'
        });
      }

      // Update status to cancelled
      analysis.status = 'cancelled';
      analysis.metadata.cancelledAt = new Date();
      await analysis.save();

      res.json({
        success: true,
        message: 'Agent execution cancelled successfully'
      });

    } catch (error) {
      next(error);
    }
  }
);

/**
 * @route GET /api/agents/types
 * @desc Get available agent types and their descriptions
 * @access Private
 */
router.get('/types',
  // authenticate(),
  async (req, res, next) => {
    try {
      const agentTypes = [
        {
          type: 'executor',
          name: 'Executor Agent',
          description: 'Executes data analysis tasks and generates initial insights',
          capabilities: ['data_processing', 'statistical_analysis', 'pattern_detection']
        },
        {
          type: 'expresser',
          name: 'Expresser Agent',
          description: 'Formats and presents analysis results in various formats',
          capabilities: ['report_generation', 'visualization', 'formatting']
        },
        {
          type: 'reviewer',
          name: 'Reviewer Agent',
          description: 'Reviews and validates analysis results for quality assurance',
          capabilities: ['quality_check', 'validation', 'accuracy_assessment']
        },
        {
          type: 'datafining',
          name: 'Data Fining Agent',
          description: 'Refines and enhances data quality and structure',
          capabilities: ['data_cleaning', 'preprocessing', 'enhancement']
        },
        {
          type: 'storyteller',
          name: 'Storyteller Agent',
          description: 'Creates narrative explanations and business stories from data',
          capabilities: ['narrative_generation', 'business_storytelling', 'insight_explanation']
        }
      ];

      res.json({
        success: true,
        data: { agentTypes }
      });

    } catch (error) {
      next(error);
    }
  }
);

module.exports = router;
