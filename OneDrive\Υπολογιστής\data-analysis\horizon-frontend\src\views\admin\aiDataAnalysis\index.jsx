/*!
  _   _  ___  ____  ___ ________  _   _   _   _ ___   
 | | | |/ _ \|  _ \|_ _|__  / _ \| \ | | | | | |_ _| 
 | |_| | | | | |_) || |  / / | | |  \| | | | | || | 
 |  _  | |_| |  _ < | | / /| |_| | |\  | | |_| || |
 |_| |_|\___/|_| \_\___/____\___/|_| \_|  \___/|___|
                                                                                                                                                                                                                                                                                                                                       
=========================================================
* Horizon UI - AI Data Analysis Platform
=========================================================
* Product Page: https://www.horizon-ui.com/
* Copyright 2023 Horizon UI (https://www.horizon-ui.com/)
* Licensed under MIT (https://github.com/horizon-ui/horizon-ui-chakra/blob/master/LICENSE.md)
* Coded by Simmmple
=========================================================
* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

// Chakra imports
import { Box } from '@chakra-ui/react';

// Custom components
import DataAnalysisDashboard from 'components/DataAnalysisDashboard';

export default function AIDataAnalysis() {
  return (
    <Box>
      <DataAnalysisDashboard />
    </Box>
  );
}
