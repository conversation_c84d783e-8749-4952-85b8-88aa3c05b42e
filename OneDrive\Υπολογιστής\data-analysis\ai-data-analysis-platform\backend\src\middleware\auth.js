const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const winston = require('winston');
const { unauthorizedError, forbiddenError } = require('./errorHandler');

class AuthMiddleware {
  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'fallback-secret-key';
    this.jwtExpiry = process.env.JWT_EXPIRY || '24h';
    this.refreshTokenExpiry = process.env.REFRESH_TOKEN_EXPIRY || '7d';
    
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/auth.log' })
      ]
    });

    if (this.jwtSecret === 'fallback-secret-key') {
      this.logger.warn('Using fallback JWT secret. Set JWT_SECRET environment variable for production.');
    }
  }

  // Generate JWT token
  generateToken(payload, expiresIn = this.jwtExpiry) {
    try {
      return jwt.sign(payload, this.jwtSecret, { 
        expiresIn,
        issuer: 'ai-data-analysis-platform',
        audience: 'client'
      });
    } catch (error) {
      this.logger.error('Token generation failed:', error);
      throw new Error('Failed to generate authentication token');
    }
  }

  // Generate refresh token
  generateRefreshToken(payload) {
    return this.generateToken(payload, this.refreshTokenExpiry);
  }

  // Verify JWT token
  verifyToken(token) {
    try {
      return jwt.verify(token, this.jwtSecret);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw unauthorizedError('Token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw unauthorizedError('Invalid token');
      } else {
        throw unauthorizedError('Token verification failed');
      }
    }
  }

  // Extract token from request
  extractToken(req) {
    // Check Authorization header
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Check query parameter (for WebSocket connections)
    if (req.query && req.query.token) {
      return req.query.token;
    }

    // Check cookies
    if (req.cookies && req.cookies.accessToken) {
      return req.cookies.accessToken;
    }

    return null;
  }

  // Main authentication middleware
  authenticate(options = {}) {
    const { required = true, roles = [] } = options;

    return (req, res, next) => {
      try {
        const token = this.extractToken(req);

        if (!token) {
          if (!required) {
            return next();
          }
          throw unauthorizedError('Access token is required');
        }

        const decoded = this.verifyToken(token);
        req.user = decoded;

        // Role-based access control
        if (roles.length > 0 && !this.hasRequiredRole(decoded, roles)) {
          throw forbiddenError('Insufficient permissions');
        }

        this.logger.info('User authenticated:', {
          userId: decoded.id,
          email: decoded.email,
          roles: decoded.roles,
          ip: req.ip
        });

        next();
      } catch (error) {
        next(error);
      }
    };
  }

  // Check if user has required role
  hasRequiredRole(user, requiredRoles) {
    if (!user.roles || !Array.isArray(user.roles)) {
      return false;
    }

    return requiredRoles.some(role => user.roles.includes(role));
  }

  // Admin only middleware
  requireAdmin() {
    return this.authenticate({ required: true, roles: ['admin'] });
  }

  // Analyst role middleware
  requireAnalyst() {
    return this.authenticate({ required: true, roles: ['admin', 'analyst'] });
  }

  // User role middleware (authenticated users)
  requireUser() {
    return this.authenticate({ required: true, roles: ['admin', 'analyst', 'user'] });
  }

  // Optional authentication (for public endpoints with optional user context)
  optionalAuth() {
    return this.authenticate({ required: false });
  }

  // Rate limiting by user
  userRateLimit(windowMs = 15 * 60 * 1000, maxRequests = 100) {
    const userRequests = new Map();

    return (req, res, next) => {
      const userId = req.user?.id || req.ip;
      const now = Date.now();
      const windowStart = now - windowMs;

      // Clean old entries
      for (const [key, requests] of userRequests.entries()) {
        userRequests.set(key, requests.filter(time => time > windowStart));
        if (userRequests.get(key).length === 0) {
          userRequests.delete(key);
        }
      }

      // Check current user's requests
      const userRequestsList = userRequests.get(userId) || [];
      const recentRequests = userRequestsList.filter(time => time > windowStart);

      if (recentRequests.length >= maxRequests) {
        return res.status(429).json({
          success: false,
          error: {
            message: 'Too many requests. Please try again later.',
            type: 'RATE_LIMIT_EXCEEDED',
            retryAfter: Math.ceil(windowMs / 1000)
          }
        });
      }

      // Add current request
      recentRequests.push(now);
      userRequests.set(userId, recentRequests);

      next();
    };
  }

  // Hash password
  async hashPassword(password) {
    try {
      const saltRounds = 12;
      return await bcrypt.hash(password, saltRounds);
    } catch (error) {
      this.logger.error('Password hashing failed:', error);
      throw new Error('Password hashing failed');
    }
  }

  // Verify password
  async verifyPassword(password, hashedPassword) {
    try {
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      this.logger.error('Password verification failed:', error);
      return false;
    }
  }

  // Generate API key
  generateApiKey(userId, purpose = 'general') {
    const payload = {
      userId,
      purpose,
      type: 'api_key',
      createdAt: Date.now()
    };

    return this.generateToken(payload, '365d'); // API keys valid for 1 year
  }

  // Verify API key
  verifyApiKey() {
    return (req, res, next) => {
      try {
        const apiKey = req.headers['x-api-key'] || req.query.apiKey;

        if (!apiKey) {
          throw unauthorizedError('API key is required');
        }

        const decoded = this.verifyToken(apiKey);

        if (decoded.type !== 'api_key') {
          throw unauthorizedError('Invalid API key');
        }

        req.apiKey = decoded;
        req.user = { id: decoded.userId, type: 'api' };

        this.logger.info('API key authenticated:', {
          userId: decoded.userId,
          purpose: decoded.purpose,
          ip: req.ip
        });

        next();
      } catch (error) {
        next(error);
      }
    };
  }

  // Revoke token (blacklist)
  revokeToken(token) {
    // In a production environment, you would store revoked tokens in a database or cache
    // For now, we'll just log the revocation
    this.logger.info('Token revoked:', { token: token.substring(0, 20) + '...' });
  }

  // Validate session
  validateSession() {
    return (req, res, next) => {
      try {
        if (!req.user) {
          throw unauthorizedError('No active session');
        }

        // Check if token is close to expiry and refresh if needed
        const tokenExp = req.user.exp * 1000; // Convert to milliseconds
        const now = Date.now();
        const timeUntilExpiry = tokenExp - now;
        const refreshThreshold = 30 * 60 * 1000; // 30 minutes

        if (timeUntilExpiry < refreshThreshold) {
          // Token is close to expiry, set a flag for the client
          res.set('X-Token-Refresh-Required', 'true');
        }

        next();
      } catch (error) {
        next(error);
      }
    };
  }

  // Login attempt tracking
  trackLoginAttempts() {
    const attempts = new Map();
    const maxAttempts = 5;
    const lockoutTime = 15 * 60 * 1000; // 15 minutes

    return (req, res, next) => {
      const identifier = req.body.email || req.ip;
      const now = Date.now();

      const userAttempts = attempts.get(identifier) || { count: 0, lastAttempt: 0, lockedUntil: 0 };

      // Check if account is locked
      if (userAttempts.lockedUntil > now) {
        const remainingTime = Math.ceil((userAttempts.lockedUntil - now) / 1000);
        return res.status(429).json({
          success: false,
          error: {
            message: `Account locked due to too many failed attempts. Try again in ${remainingTime} seconds.`,
            type: 'ACCOUNT_LOCKED',
            retryAfter: remainingTime
          }
        });
      }

      // Reset attempts if lockout period has passed
      if (userAttempts.lockedUntil > 0 && userAttempts.lockedUntil <= now) {
        attempts.delete(identifier);
      }

      req.loginAttempts = {
        recordFailure: () => {
          const currentAttempts = attempts.get(identifier) || { count: 0, lastAttempt: 0, lockedUntil: 0 };
          currentAttempts.count += 1;
          currentAttempts.lastAttempt = now;

          if (currentAttempts.count >= maxAttempts) {
            currentAttempts.lockedUntil = now + lockoutTime;
            this.logger.warn('Account locked due to failed login attempts:', { identifier });
          }

          attempts.set(identifier, currentAttempts);
        },
        recordSuccess: () => {
          attempts.delete(identifier);
        }
      };

      next();
    };
  }

  // Security headers
  securityHeaders() {
    return (req, res, next) => {
      res.set({
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
      });
      next();
    };
  }

  // CORS configuration for authenticated requests
  corsConfig() {
    return (req, res, next) => {
      const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];
      const origin = req.headers.origin;

      if (allowedOrigins.includes(origin)) {
        res.set('Access-Control-Allow-Origin', origin);
      }

      res.set({
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Max-Age': '86400' // 24 hours
      });

      if (req.method === 'OPTIONS') {
        return res.status(200).end();
      }

      next();
    };
  }

  // Generic role requirement middleware
  requireRole(allowedRoles) {
    return this.authenticate({ required: true, roles: allowedRoles });
  }
}

// Export both class and instance
const authMiddleware = new AuthMiddleware();

module.exports = {
  AuthMiddleware,
  authMiddleware,
  
  // Convenience exports
  authenticate: authMiddleware.authenticate.bind(authMiddleware),
  requireRole: authMiddleware.requireRole.bind(authMiddleware),
  requireAdmin: authMiddleware.requireAdmin.bind(authMiddleware),
  requireAnalyst: authMiddleware.requireAnalyst.bind(authMiddleware),
  requireUser: authMiddleware.requireUser.bind(authMiddleware),
  optionalAuth: authMiddleware.optionalAuth.bind(authMiddleware),
  verifyApiKey: authMiddleware.verifyApiKey.bind(authMiddleware),
  userRateLimit: authMiddleware.userRateLimit.bind(authMiddleware),
  trackLoginAttempts: authMiddleware.trackLoginAttempts.bind(authMiddleware),
  securityHeaders: authMiddleware.securityHeaders.bind(authMiddleware),
  corsConfig: authMiddleware.corsConfig.bind(authMiddleware),
  generateToken: authMiddleware.generateToken.bind(authMiddleware),
  generateRefreshToken: authMiddleware.generateRefreshToken.bind(authMiddleware),
  verifyToken: authMiddleware.verifyToken.bind(authMiddleware),
  hashPassword: authMiddleware.hashPassword.bind(authMiddleware),
  verifyPassword: authMiddleware.verifyPassword.bind(authMiddleware),
  generateApiKey: authMiddleware.generateApiKey.bind(authMiddleware)
};
