class StorytellerAgent {
  constructor(deepseekAPI) {
    this.deepseekAPI = deepseekAPI;
    this.agentId = 'storyteller';
    this.pattern = 'DOE';
    this.status = 'idle';
    this.capabilities = [
      'narrative_generation',
      'business_storytelling',
      'executive_reporting',
      'insight_synthesis',
      'recommendation_crafting'
    ];
  }

  async execute(data, analysisResults = {}) {
    this.status = 'processing';
    
    try {
      // Step 1: Create executive summary
      const executiveSummary = await this.createExecutiveSummary(analysisResults);
      
      // Step 2: Generate business narrative
      const businessNarrative = await this.generateBusinessNarrative(data, analysisResults);
      
      // Step 3: Craft actionable recommendations
      const recommendations = await this.craftRecommendations(analysisResults);
      
      // Step 4: Create compelling insights story
      const insightStory = await this.createInsightStory(analysisResults);
      
      // Step 5: Generate presentation materials
      const presentationMaterials = await this.generatePresentationMaterials(
        executiveSummary, businessNarrative, recommendations
      );
      
      // Step 6: Create industry context using DeepSeek
      const industryContext = await this.generateIndustryContext(data, analysisResults);
      
      this.status = 'completed';
      
      return {
        executiveSummary,
        businessNarrative,
        recommendations,
        insightStory,
        presentationMaterials,
        industryContext,
        deliverables: {
          executive_report: this.generateExecutiveReport(executiveSummary, recommendations),
          detailed_analysis: this.generateDetailedAnalysis(businessNarrative, insightStory),
          action_plan: this.generateActionPlan(recommendations),
          presentation_deck: this.generatePresentationDeck(presentationMaterials)
        },
        metadata: {
          agentId: this.agentId,
          pattern: this.pattern,
          timestamp: new Date().toISOString(),
          narrativeComplexity: this.assessNarrativeComplexity(businessNarrative),
          recommendationCount: recommendations.length,
          confidence: this.calculateNarrativeConfidence(analysisResults)
        }
      };
      
    } catch (error) {
      this.status = 'error';
      console.error('Storyteller Agent Error:', error);
      throw error;
    }
  }

  async createExecutiveSummary(analysisResults) {
    const summary = {
      key_findings: [],
      business_impact: '',
      strategic_implications: [],
      critical_actions: [],
      success_metrics: [],
      executive_overview: ''
    };

    // Extract key findings from different analysis phases
    if (analysisResults.execute?.businessInsights) {
      summary.key_findings.push(...analysisResults.execute.businessInsights.slice(0, 3));
    }

    if (analysisResults.dataFining?.patterns) {
      const significantPatterns = analysisResults.dataFining.patterns
        .filter(p => p.strength > 0.7 || p.significance > 0.8)
        .slice(0, 2);
      
      significantPatterns.forEach(pattern => {
        summary.key_findings.push(`${pattern.description} - ${pattern.business_implication}`);
      });
    }

    // Assess business impact
    summary.business_impact = this.assessBusinessImpact(analysisResults);

    // Generate strategic implications
    summary.strategic_implications = this.generateStrategicImplications(analysisResults);

    // Identify critical actions
    summary.critical_actions = this.identifyCriticalActions(analysisResults);

    // Define success metrics
    summary.success_metrics = this.defineSuccessMetrics(analysisResults);

    // Create executive overview
    summary.executive_overview = this.createExecutiveOverview(summary);

    return summary;
  }

  async generateBusinessNarrative(data, analysisResults) {
    const narrative = {
      business_context: '',
      data_story: '',
      performance_journey: '',
      challenges_identified: [],
      opportunities_discovered: [],
      transformation_potential: '',
      narrative_flow: []
    };

    // Business context
    if (analysisResults.plan?.businessContext) {
      narrative.business_context = this.narrateBusinessContext(analysisResults.plan.businessContext);
    }

    // Data story
    narrative.data_story = this.createDataStory(data, analysisResults);

    // Performance journey
    narrative.performance_journey = this.narratePerformanceJourney(analysisResults);

    // Challenges and opportunities
    narrative.challenges_identified = this.identifyBusinessChallenges(analysisResults);
    narrative.opportunities_discovered = this.discoverBusinessOpportunities(analysisResults);

    // Transformation potential
    narrative.transformation_potential = this.assessTransformationPotential(analysisResults);

    // Create narrative flow
    narrative.narrative_flow = this.createNarrativeFlow(narrative);

    return narrative;
  }

  async craftRecommendations(analysisResults) {
    const recommendations = [];

    // Strategic recommendations
    const strategicRecs = this.generateStrategicRecommendations(analysisResults);
    recommendations.push(...strategicRecs);

    // Operational recommendations
    const operationalRecs = this.generateOperationalRecommendations(analysisResults);
    recommendations.push(...operationalRecs);

    // Technology recommendations
    const techRecs = this.generateTechnologyRecommendations(analysisResults);
    recommendations.push(...techRecs);

    // Performance optimization recommendations
    const performanceRecs = this.generatePerformanceRecommendations(analysisResults);
    recommendations.push(...performanceRecs);

    // Risk mitigation recommendations
    const riskRecs = this.generateRiskMitigationRecommendations(analysisResults);
    recommendations.push(...riskRecs);

    // Prioritize and rank recommendations
    return this.prioritizeRecommendations(recommendations);
  }

  async createInsightStory(analysisResults) {
    const story = {
      opening: '',
      discovery_moments: [],
      turning_points: [],
      revelation_insights: [],
      future_vision: '',
      compelling_narrative: ''
    };

    // Opening hook
    story.opening = this.createOpeningHook(analysisResults);

    // Discovery moments
    story.discovery_moments = this.identifyDiscoveryMoments(analysisResults);

    // Turning points
    story.turning_points = this.identifyTurningPoints(analysisResults);

    // Revelation insights
    story.revelation_insights = this.extractRevelationInsights(analysisResults);

    // Future vision
    story.future_vision = this.paintFutureVision(analysisResults);

    // Weave compelling narrative
    story.compelling_narrative = this.weaveCompellingNarrative(story);

    return story;
  }

  async generatePresentationMaterials(executiveSummary, businessNarrative, recommendations) {
    return {
      executive_slides: this.createExecutiveSlides(executiveSummary),
      detailed_slides: this.createDetailedSlides(businessNarrative),
      recommendation_slides: this.createRecommendationSlides(recommendations),
      appendix_slides: this.createAppendixSlides(),
      speaker_notes: this.generateSpeakerNotes(),
      handout_materials: this.createHandoutMaterials()
    };
  }

  async generateIndustryContext(data, analysisResults) {
    const businessContext = analysisResults.plan?.businessContext || {};
    
    const prompt = `
    Generate industry context and competitive insights for this business analysis:
    
    Business Type: ${businessContext.businessType || 'General Business'}
    Industry: ${businessContext.industry || 'General'}
    Key Metrics: ${JSON.stringify(analysisResults.execute?.kpiCalculations ? Object.keys(analysisResults.execute.kpiCalculations) : [], null, 2)}
    Performance Trends: ${JSON.stringify(analysisResults.execute?.trendAnalysis || {}, null, 2)}
    
    Provide industry context in JSON format:
    {
      "industry_overview": "Current state and trends in the industry",
      "competitive_landscape": "Key competitive factors and positioning",
      "industry_benchmarks": {
        "performance_standards": "Expected performance levels",
        "best_practices": ["industry best practices"],
        "success_factors": ["critical success factors"]
      },
      "market_trends": [
        {
          "trend": "trend description",
          "impact": "High/Medium/Low",
          "opportunity": "specific opportunity",
          "timeframe": "timeline for impact"
        }
      ],
      "regulatory_environment": "Key regulations and compliance requirements",
      "technology_disruption": "Emerging technologies affecting the industry",
      "strategic_positioning": "Recommendations for competitive positioning"
    }
    `;

    try {
      const response = await this.deepseekAPI.client.post('/chat/completions', {
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: 'You are an industry expert and business strategist with deep knowledge across multiple sectors.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.3,
        max_tokens: 2000
      });

      return this.deepseekAPI.parseJSONResponse(response.data.choices[0].message.content);
    } catch (error) {
      console.error('Industry Context Generation Error:', error);
      return this.generateFallbackIndustryContext(businessContext);
    }
  }

  // Executive Summary Methods
  assessBusinessImpact(analysisResults) {
    const impactFactors = [];
    
    // Revenue impact
    if (analysisResults.execute?.forecasts?.forecasts) {
      const revenueForecasts = analysisResults.execute.forecasts.forecasts
        .filter(f => f.metric.toLowerCase().includes('revenue') || f.metric.toLowerCase().includes('sales'));
      
      if (revenueForecasts.length > 0) {
        const avgGrowth = revenueForecasts.reduce((sum, f) => sum + (f.growth_rate || 0), 0) / revenueForecasts.length;
        impactFactors.push(`Revenue growth potential: ${(avgGrowth * 100).toFixed(1)}%`);
      }
    }

    // Efficiency impact
    if (analysisResults.dataFining?.patterns) {
      const efficiencyPatterns = analysisResults.dataFining.patterns
        .filter(p => p.business_implication.toLowerCase().includes('efficiency') || 
                     p.business_implication.toLowerCase().includes('optimization'));
      
      if (efficiencyPatterns.length > 0) {
        impactFactors.push(`${efficiencyPatterns.length} efficiency optimization opportunities identified`);
      }
    }

    // Quality impact
    if (analysisResults.review?.qualityScore) {
      if (analysisResults.review.qualityScore > 0.8) {
        impactFactors.push('High-quality data foundation for reliable decision making');
      }
    }

    return impactFactors.length > 0 
      ? impactFactors.join('. ') 
      : 'Moderate business impact potential identified through data analysis.';
  }

  generateStrategicImplications(analysisResults) {
    const implications = [];

    // Growth implications
    if (analysisResults.execute?.forecasts?.forecasts) {
      const growingMetrics = analysisResults.execute.forecasts.forecasts
        .filter(f => f.growth_rate > 0.1);
      
      if (growingMetrics.length > 0) {
        implications.push('Scale operations to support projected growth');
      }
    }

    // Pattern implications
    if (analysisResults.dataFining?.patterns) {
      const cyclicalPatterns = analysisResults.dataFining.patterns
        .filter(p => p.type === 'cyclical');
      
      if (cyclicalPatterns.length > 0) {
        implications.push('Implement seasonal planning and resource allocation strategies');
      }
    }

    // Ensure minimum implications
    if (implications.length === 0) {
      implications.push(
        'Focus on data-driven decision making',
        'Implement continuous monitoring and optimization',
        'Invest in analytics capabilities for competitive advantage'
      );
    }

    return implications;
  }

  identifyCriticalActions(analysisResults) {
    const actions = [];

    // Data quality actions
    if (analysisResults.review?.qualityScore < 0.7) {
      actions.push('Immediate: Improve data quality and collection processes');
    }

    // Performance actions
    if (analysisResults.execute?.businessInsights) {
      const negativeInsights = analysisResults.execute.businessInsights
        .filter(insight => insight.toLowerCase().includes('decline') || 
                          insight.toLowerCase().includes('decrease'));
      
      if (negativeInsights.length > 0) {
        actions.push('Urgent: Address declining performance metrics');
      }
    }

    // Opportunity actions
    actions.push('30 days: Implement top-priority recommendations');
    actions.push('90 days: Establish regular analytics review process');

    return actions;
  }

  defineSuccessMetrics(analysisResults) {
    const metrics = [];

    // KPI-based metrics
    if (analysisResults.plan?.kpis?.primary_kpis) {
      analysisResults.plan.kpis.primary_kpis.forEach(kpi => {
        metrics.push(`Improve ${kpi} by 15-25% within 6 months`);
      });
    }

    // Default metrics
    if (metrics.length === 0) {
      metrics.push(
        'Achieve 20% improvement in key performance indicators',
        'Reduce operational inefficiencies by 15%',
        'Increase data-driven decision accuracy to 90%'
      );
    }

    return metrics.slice(0, 4); // Limit to top 4 metrics
  }

  createExecutiveOverview(summary) {
    const overview = `
    Our comprehensive analysis reveals ${summary.key_findings.length} critical insights that present significant opportunities for business improvement. 
    ${summary.business_impact} 
    
    Key strategic implications include ${summary.strategic_implications.slice(0, 2).join(' and ')}.
    
    Immediate action on ${summary.critical_actions.length} critical recommendations could deliver measurable results within the next quarter, 
    with success measured through ${summary.success_metrics.slice(0, 2).join(' and ')}.
    `;

    return overview.trim().replace(/\s+/g, ' ');
  }

  // Business Narrative Methods
  narrateBusinessContext(businessContext) {
    return `
    Operating in the ${businessContext.industry || 'business'} sector, this ${businessContext.businessType || 'organization'} 
    demonstrates ${businessContext.context || 'strong operational characteristics'}. 
    ${businessContext.reasoning || 'The business model shows potential for data-driven optimization.'}
    `;
  }

  createDataStory(data, analysisResults) {
    const dataPoints = data ? data.length : 0;
    const columns = data && data.length > 0 ? Object.keys(data[0]).length : 0;
    
    return `
    The analysis encompasses ${dataPoints} data points across ${columns} key variables, revealing a rich tapestry of business performance. 
    Our data journey uncovered ${analysisResults.execute?.businessInsights?.length || 3} significant insights, 
    with ${analysisResults.dataFining?.patterns?.length || 'multiple'} underlying patterns driving business performance.
    `;
  }

  narratePerformanceJourney(analysisResults) {
    let journey = 'The performance analysis reveals a complex landscape of business operations. ';

    // Add trend insights
    if (analysisResults.execute?.trendAnalysis) {
      const trends = Object.values(analysisResults.execute.trendAnalysis);
      const increasingTrends = trends.filter(t => t.direction === 'increasing').length;
      const decreasingTrends = trends.filter(t => t.direction === 'decreasing').length;
      
      journey += `Of the key metrics analyzed, ${increasingTrends} show positive trends while ${decreasingTrends} require attention. `;
    }

    // Add forecasting insights
    if (analysisResults.execute?.forecasts?.forecasts) {
      const positiveForecasts = analysisResults.execute.forecasts.forecasts
        .filter(f => f.growth_rate > 0).length;
      
      journey += `Future projections indicate ${positiveForecasts} metrics positioned for growth, `;
      journey += 'suggesting strong fundamentals with targeted optimization opportunities.';
    }

    return journey;
  }

  identifyBusinessChallenges(analysisResults) {
    const challenges = [];

    // Quality challenges
    if (analysisResults.review?.qualityScore < 0.8) {
      challenges.push({
        challenge: 'Data Quality and Consistency',
        impact: 'Medium to High',
        description: 'Inconsistent data quality may be limiting analytical insights and decision accuracy'
      });
    }

    // Performance challenges
    if (analysisResults.execute?.trendAnalysis) {
      const decliningMetrics = Object.entries(analysisResults.execute.trendAnalysis)
        .filter(([metric, trend]) => trend.direction === 'decreasing')
        .map(([metric]) => metric);
      
      if (decliningMetrics.length > 0) {
        challenges.push({
          challenge: 'Performance Decline in Key Areas',
          impact: 'High',
          description: `Declining trends identified in: ${decliningMetrics.slice(0, 3).join(', ')}`
        });
      }
    }

    // Default challenges
    if (challenges.length === 0) {
      challenges.push({
        challenge: 'Analytics Maturity',
        impact: 'Medium',
        description: 'Opportunity to enhance data analytics capabilities for deeper insights'
      });
    }

    return challenges;
  }

  discoverBusinessOpportunities(analysisResults) {
    const opportunities = [];

    // Pattern-based opportunities
    if (analysisResults.dataFining?.patterns) {
      analysisResults.dataFining.patterns.forEach(pattern => {
        if (pattern.business_implication.toLowerCase().includes('optimization') || 
            pattern.business_implication.toLowerCase().includes('opportunity')) {
          opportunities.push({
            opportunity: pattern.description,
            potential: 'High',
            timeframe: '3-6 months',
            description: pattern.business_implication
          });
        }
      });
    }

    // Growth opportunities
    if (analysisResults.execute?.forecasts?.forecasts) {
      const strongGrowthMetrics = analysisResults.execute.forecasts.forecasts
        .filter(f => f.growth_rate > 0.2);
      
      if (strongGrowthMetrics.length > 0) {
        opportunities.push({
          opportunity: 'Accelerated Growth in High-Performing Areas',
          potential: 'Very High',
          timeframe: '6-12 months',
          description: 'Scale successful strategies and invest in high-growth metrics'
        });
      }
    }

    // Default opportunities
    if (opportunities.length === 0) {
      opportunities.push({
        opportunity: 'Data-Driven Decision Making',
        potential: 'High',
        timeframe: '1-3 months',
        description: 'Implement systematic analytics for improved business outcomes'
      });
    }

    return opportunities.slice(0, 5); // Limit to top 5
  }

  assessTransformationPotential(analysisResults) {
    let potential = 'moderate';
    let description = 'The analysis indicates solid potential for business transformation through data-driven initiatives. ';

    // Assess based on patterns found
    if (analysisResults.dataFining?.patterns?.length > 5) {
      potential = 'high';
      description = 'Significant transformation potential identified with multiple optimization vectors. ';
    }

    // Assess based on quality score
    if (analysisResults.review?.qualityScore > 0.8) {
      description += 'Strong data foundation supports advanced analytics and AI implementation. ';
    }

    // Assess based on forecasts
    if (analysisResults.execute?.forecasts?.forecasts) {
      const positiveForecasts = analysisResults.execute.forecasts.forecasts
        .filter(f => f.growth_rate > 0.1).length;
      
      if (positiveForecasts > 2) {
        description += 'Multiple growth vectors provide diverse transformation pathways.';
      }
    }

    return `${potential.charAt(0).toUpperCase() + potential.slice(1)} transformation potential. ${description}`;
  }

  createNarrativeFlow(narrative) {
    return [
      {
        section: 'Context Setting',
        content: narrative.business_context,
        purpose: 'Establish business foundation'
      },
      {
        section: 'Data Journey',
        content: narrative.data_story,
        purpose: 'Present analytical scope'
      },
      {
        section: 'Performance Analysis',
        content: narrative.performance_journey,
        purpose: 'Reveal current state insights'
      },
      {
        section: 'Challenge Identification',
        content: narrative.challenges_identified,
        purpose: 'Address improvement areas'
      },
      {
        section: 'Opportunity Discovery',
        content: narrative.opportunities_discovered,
        purpose: 'Highlight growth potential'
      },
      {
        section: 'Transformation Vision',
        content: narrative.transformation_potential,
        purpose: 'Paint future possibilities'
      }
    ];
  }

  // Recommendation Methods
  generateStrategicRecommendations(analysisResults) {
    const recommendations = [];

    // Growth strategy recommendations
    if (analysisResults.execute?.forecasts?.forecasts) {
      const growthMetrics = analysisResults.execute.forecasts.forecasts
        .filter(f => f.growth_rate > 0.1);
      
      if (growthMetrics.length > 0) {
        recommendations.push({
          category: 'Strategic Growth',
          priority: 'High',
          title: 'Scale High-Growth Business Areas',
          description: `Focus investment and resources on ${growthMetrics.length} high-growth metrics showing strong momentum`,
          expected_impact: '20-30% performance improvement',
          implementation_timeline: '6-12 months',
          resources_needed: ['Strategic planning team', 'Budget reallocation', 'Performance monitoring'],
          success_metrics: ['Growth rate acceleration', 'Market share increase'],
          risk_level: 'Medium'
        });
      }
    }

    return recommendations;
  }

  generateOperationalRecommendations(analysisResults) {
    const recommendations = [];

    // Process optimization
    if (analysisResults.dataFining?.patterns) {
      const efficiencyPatterns = analysisResults.dataFining.patterns
        .filter(p => p.type === 'cyclical' || p.business_implication.toLowerCase().includes('efficiency'));
      
      if (efficiencyPatterns.length > 0) {
        recommendations.push({
          category: 'Operational Excellence',
          priority: 'High',
          title: 'Optimize Cyclical Business Processes',
          description: 'Implement pattern-based process optimization to improve operational efficiency',
          expected_impact: '15-25% efficiency improvement',
          implementation_timeline: '3-6 months',
          resources_needed: ['Process improvement team', 'Technology upgrades'],
          success_metrics: ['Process efficiency metrics', 'Cost reduction'],
          risk_level: 'Low'
        });
      }
    }

    return recommendations;
  }

  generateTechnologyRecommendations(analysisResults) {
    const recommendations = [];

    // Analytics platform
    recommendations.push({
      category: 'Technology Enhancement',
      priority: 'Medium',
      title: 'Implement Advanced Analytics Platform',
      description: 'Deploy comprehensive analytics infrastructure for continuous business intelligence',
      expected_impact: 'Enhanced decision-making capabilities',
      implementation_timeline: '4-8 months',
      resources_needed: ['IT team', 'Analytics software', 'Training budget'],
      success_metrics: ['Decision accuracy', 'Insight generation speed'],
      risk_level: 'Medium'
    });

    return recommendations;
  }

  generatePerformanceRecommendations(analysisResults) {
    const recommendations = [];

    // KPI optimization
    if (analysisResults.plan?.kpis?.primary_kpis) {
      recommendations.push({
        category: 'Performance Optimization',
        priority: 'High',
        title: 'Implement KPI-Driven Performance Management',
        description: `Focus on optimizing ${analysisResults.plan.kpis.primary_kpis.length} critical KPIs identified in analysis`,
        expected_impact: '10-20% KPI improvement',
        implementation_timeline: '2-4 months',
        resources_needed: ['Performance management system', 'Training'],
        success_metrics: ['KPI achievement rates', 'Performance consistency'],
        risk_level: 'Low'
      });
    }

    return recommendations;
  }

  generateRiskMitigationRecommendations(analysisResults) {
    const recommendations = [];

    // Data quality risks
    if (analysisResults.review?.qualityScore < 0.7) {
      recommendations.push({
        category: 'Risk Mitigation',
        priority: 'High',
        title: 'Establish Data Quality Management Program',
        description: 'Implement comprehensive data quality controls to ensure reliable analytics',
        expected_impact: 'Reduced decision risk and improved accuracy',
        implementation_timeline: '2-3 months',
        resources_needed: ['Data governance team', 'Quality tools'],
        success_metrics: ['Data quality score', 'Error reduction rate'],
        risk_level: 'Low'
      });
    }

    return recommendations;
  }

  prioritizeRecommendations(recommendations) {
    const priorityOrder = { 'High': 3, 'Medium': 2, 'Low': 1 };
    
    return recommendations.sort((a, b) => {
      // First sort by priority
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      
      // Then by expected impact (simplified scoring)
      const impactScore = (rec) => {
        if (rec.expected_impact.includes('20-30%')) return 3;
        if (rec.expected_impact.includes('15-25%')) return 2;
        return 1;
      };
      
      return impactScore(b) - impactScore(a);
    });
  }

  // Insight Story Methods
  createOpeningHook(analysisResults) {
    const patterns = analysisResults.dataFining?.patterns?.length || 0;
    const insights = analysisResults.execute?.businessInsights?.length || 0;
    
    return `In the depths of your business data lies a story of ${patterns} hidden patterns and ${insights} powerful insights waiting to transform your operations. Our journey through the numbers reveals not just what happened, but why it happened and what it means for your future.`;
  }

  identifyDiscoveryMoments(analysisResults) {
    const moments = [];

    // Pattern discoveries
    if (analysisResults.dataFining?.patterns) {
      analysisResults.dataFining.patterns.slice(0, 3).forEach(pattern => {
        moments.push({
          moment: `Discovery of ${pattern.type} pattern in ${pattern.column || 'business data'}`,
          significance: pattern.strength || pattern.significance || 'High',
          insight: pattern.description,
          implication: pattern.business_implication
        });
      });
    }

    // Forecast discoveries
    if (analysisResults.execute?.forecasts?.forecasts) {
      const significantForecasts = analysisResults.execute.forecasts.forecasts
        .filter(f => Math.abs(f.growth_rate) > 0.15);
      
      significantForecasts.slice(0, 2).forEach(forecast => {
        moments.push({
          moment: `Breakthrough forecast for ${forecast.metric}`,
          significance: 'High',
          insight: `${forecast.trend} trajectory with ${(forecast.growth_rate * 100).toFixed(1)}% growth rate`,
          implication: 'Strategic planning opportunity identified'
        });
      });
    }

    return moments;
  }

  identifyTurningPoints(analysisResults) {
    const turningPoints = [];

    // Regime changes
    if (analysisResults.dataFining?.patterns) {
      const regimeChanges = analysisResults.dataFining.patterns
        .filter(p => p.type === 'regime_change');
      
      regimeChanges.forEach(change => {
        turningPoints.push({
          point: `Business regime shift in ${change.column}`,
          timing: `Position ${change.change_point}`,
          magnitude: change.magnitude,
          significance: 'Critical business transition identified'
        });
      });
    }

    // Threshold effects
    if (analysisResults.dataFining?.patterns) {
      const thresholds = analysisResults.dataFining.patterns
        .filter(p => p.type === 'threshold');
      
      thresholds.forEach(threshold => {
        turningPoints.push({
          point: `Performance threshold at ${threshold.threshold_value}`,
          timing: 'Critical performance level',
          magnitude: threshold.effect_size,
          significance: 'Optimization target identified'
        });
      });
    }

    return turningPoints;
  }

  extractRevelationInsights(analysisResults) {
    const revelations = [];

    // AI-generated insights
    if (analysisResults.dataFining?.aiInsights?.strategic_insights) {
      analysisResults.dataFining.aiInsights.strategic_insights.forEach(insight => {
        revelations.push({
          revelation: insight.insight,
          impact: insight.business_impact,
          actionability: insight.actionability,
          value: insight.opportunity_value
        });
      });
    }

    // Hidden correlations
    if (analysisResults.dataFining?.hiddenCorrelations) {
      analysisResults.dataFining.hiddenCorrelations.slice(0, 2).forEach(correlation => {
        revelations.push({
          revelation: `Hidden relationship discovered: ${correlation.description || 'Complex correlation pattern'}`,
          impact: 'Medium to High',
          actionability: 'Leverage for optimization',
          value: 'Competitive advantage through insight'
        });
      });
    }

    return revelations;
  }

  paintFutureVision(analysisResults) {
    let vision = 'Imagine a future where ';

    // Growth vision
    if (analysisResults.execute?.forecasts?.forecasts) {
      const positiveForecasts = analysisResults.execute.forecasts.forecasts
        .filter(f => f.growth_rate > 0);
      
      if (positiveForecasts.length > 0) {
        vision += `your ${positiveForecasts.length} key performance metrics are growing systematically, `;
        vision += 'each milestone predictable and achievable through data-driven decisions. ';
      }
    }

    // Pattern-optimized future
    if (analysisResults.dataFining?.patterns?.length > 0) {
      vision += 'Your business operates in perfect harmony with its natural rhythms, ';
      vision += 'every cycle optimized, every pattern leveraged for maximum efficiency. ';
    }

    // AI-enhanced future
    vision += 'Advanced analytics and AI work continuously behind the scenes, ';
    vision += 'turning every data point into competitive advantage and every insight into action.';

    return vision;
  }

  weaveCompellingNarrative(story) {
    return `
    ${story.opening}

    Our exploration began with ${story.discovery_moments.length} remarkable discoveries, each revealing hidden truths about your business performance. ${story.discovery_moments[0]?.insight || 'Key patterns emerged from the analysis'}.

    ${story.turning_points.length > 0 ? `Critical turning points emerged at ${story.turning_points[0]?.point}, marking ${story.turning_points[0]?.significance}.` : 'The analysis revealed important inflection points in business performance.'}

    But the most profound revelations came from our deep-dive analysis: ${story.revelation_insights[0]?.revelation || 'Advanced patterns that traditional analysis would miss'}. This insight alone represents ${story.revelation_insights[0]?.value || 'significant business value'}.

    ${story.future_vision}

    This is not just analysis—it's your roadmap to transformation.
    `;
  }

  // Presentation and Report Generation Methods
  createExecutiveSlides(executiveSummary) {
    return [
      {
        slide: 1,
        title: 'Executive Summary',
        content: {
          overview: executiveSummary.executive_overview,
          key_points: executiveSummary.key_findings.slice(0, 4)
        }
      },
      {
        slide: 2,
        title: 'Business Impact',
        content: {
          impact: executiveSummary.business_impact,
          metrics: executiveSummary.success_metrics
        }
      },
      {
        slide: 3,
        title: 'Strategic Implications',
        content: {
          implications: executiveSummary.strategic_implications,
          actions: executiveSummary.critical_actions
        }
      }
    ];
  }

  createDetailedSlides(businessNarrative) {
    return businessNarrative.narrative_flow.map((section, index) => ({
      slide: index + 4,
      title: section.section,
      content: {
        narrative: section.content,
        purpose: section.purpose
      }
    }));
  }

  createRecommendationSlides(recommendations) {
    return recommendations.slice(0, 5).map((rec, index) => ({
      slide: index + 10,
      title: rec.title,
      content: {
        category: rec.category,
        priority: rec.priority,
        description: rec.description,
        impact: rec.expected_impact,
        timeline: rec.implementation_timeline,
        resources: rec.resources_needed,
        metrics: rec.success_metrics
      }
    }));
  }

  createAppendixSlides() {
    return [
      {
        slide: 'A1',
        title: 'Methodology',
        content: 'Analysis methodology and approach details'
      },
      {
        slide: 'A2',
        title: 'Data Sources',
        content: 'Data sources and quality assessment'
      }
    ];
  }

  generateSpeakerNotes() {
    return {
      opening: 'Begin with the compelling business opportunity and transformation potential',
      key_insights: 'Emphasize the uniqueness and actionability of insights discovered',
      recommendations: 'Focus on immediate actions and quick wins',
      closing: 'Reinforce the competitive advantage of data-driven decision making'
    };
  }

  createHandoutMaterials() {
    return {
      executive_summary: 'One-page executive summary for stakeholders',
      detailed_report: 'Comprehensive analysis report with all findings',
      action_checklist: 'Prioritized action items with timelines',
      resource_guide: 'Implementation resources and best practices'
    };
  }

  // Report Generation Methods
  generateExecutiveReport(executiveSummary, recommendations) {
    return {
      title: 'Executive Business Intelligence Report',
      sections: [
        {
          title: 'Executive Overview',
          content: executiveSummary.executive_overview
        },
        {
          title: 'Key Findings',
          content: executiveSummary.key_findings
        },
        {
          title: 'Strategic Recommendations',
          content: recommendations.slice(0, 3).map(r => `${r.title}: ${r.description}`)
        },
        {
          title: 'Success Metrics',
          content: executiveSummary.success_metrics
        }
      ]
    };
  }

  generateDetailedAnalysis(businessNarrative, insightStory) {
    return {
      title: 'Comprehensive Business Analysis',
      sections: [
        {
          title: 'Business Context',
          content: businessNarrative.business_context
        },
        {
          title: 'Data Story',
          content: businessNarrative.data_story
        },
        {
          title: 'Performance Journey',
          content: businessNarrative.performance_journey
        },
        {
          title: 'Key Insights',
          content: insightStory.compelling_narrative
        },
        {
          title: 'Transformation Potential',
          content: businessNarrative.transformation_potential
        }
      ]
    };
  }

  generateActionPlan(recommendations) {
    return {
      title: 'Strategic Action Plan',
      immediate_actions: recommendations.filter(r => r.priority === 'High'),
      medium_term_actions: recommendations.filter(r => r.priority === 'Medium'),
      long_term_actions: recommendations.filter(r => r.priority === 'Low'),
      implementation_roadmap: this.createImplementationRoadmap(recommendations)
    };
  }

  generatePresentationDeck(presentationMaterials) {
    return {
      title: 'Business Intelligence Presentation',
      slide_count: presentationMaterials.executive_slides.length + 
                   presentationMaterials.detailed_slides.length + 
                   presentationMaterials.recommendation_slides.length,
      sections: [
        'Executive Summary',
        'Business Analysis',
        'Strategic Recommendations',
        'Implementation Roadmap'
      ],
      speaker_notes: presentationMaterials.speaker_notes,
      handouts: presentationMaterials.handout_materials
    };
  }

  createImplementationRoadmap(recommendations) {
    const roadmap = {
      phase_1: {
        timeframe: '0-30 days',
        actions: recommendations.filter(r => r.implementation_timeline.includes('2-3') || 
                                          r.implementation_timeline.includes('1-3')),
        focus: 'Quick wins and foundation building'
      },
      phase_2: {
        timeframe: '30-90 days',
        actions: recommendations.filter(r => r.implementation_timeline.includes('3-6') ||
                                          r.implementation_timeline.includes('2-4')),
        focus: 'Core implementations and process optimization'
      },
      phase_3: {
        timeframe: '90+ days',
        actions: recommendations.filter(r => r.implementation_timeline.includes('6-12') ||
                                          r.implementation_timeline.includes('4-8')),
        focus: 'Strategic initiatives and long-term transformation'
      }
    };

    return roadmap;
  }

  // Utility Methods
  generateFallbackIndustryContext(businessContext) {
    return {
      industry_overview: `The ${businessContext.industry || 'business'} sector continues to evolve with digital transformation trends`,
      competitive_landscape: 'Competitive differentiation increasingly depends on data-driven insights and operational efficiency',
      industry_benchmarks: {
        performance_standards: 'Industry-standard performance metrics and best practices',
        best_practices: ['Data-driven decision making', 'Continuous improvement', 'Customer-centric approach'],
        success_factors: ['Operational efficiency', 'Innovation capability', 'Market responsiveness']
      },
      market_trends: [
        {
          trend: 'Digital transformation acceleration',
          impact: 'High',
          opportunity: 'Enhanced analytics and automation',
          timeframe: '1-2 years'
        }
      ],
      regulatory_environment: 'Standard industry regulations and compliance requirements apply',
      technology_disruption: 'AI and advanced analytics are reshaping business operations',
      strategic_positioning: 'Focus on data analytics capabilities for competitive advantage'
    };
  }

  assessNarrativeComplexity(businessNarrative) {
    const complexityFactors = [
      businessNarrative.narrative_flow?.length || 0,
      businessNarrative.challenges_identified?.length || 0,
      businessNarrative.opportunities_discovered?.length || 0
    ];
    
    const totalComplexity = complexityFactors.reduce((sum, factor) => sum + factor, 0);
    return Math.min(1, totalComplexity / 15); // Normalize to 0-1 scale
  }

  calculateNarrativeConfidence(analysisResults) {
    let confidence = 0.7; // Base confidence
    
    // Increase confidence based on data quality
    if (analysisResults.review?.qualityScore) {
      confidence += (analysisResults.review.qualityScore - 0.7) * 0.3;
    }
    
    // Increase confidence based on patterns found
    if (analysisResults.dataFining?.patterns?.length > 3) {
      confidence += 0.1;
    }
    
    return Math.min(1, Math.max(0.5, confidence));
  }

  getStatus() {
    return {
      agentId: this.agentId,
      status: this.status,
      pattern: this.pattern,
      capabilities: this.capabilities,
      last_execution: this.lastExecution || null
    };
  }
}

module.exports = StorytellerAgent;
