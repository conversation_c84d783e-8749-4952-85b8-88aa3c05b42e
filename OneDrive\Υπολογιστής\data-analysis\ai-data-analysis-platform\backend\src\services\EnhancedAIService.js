/**
 * ENHANCED AI SERVICE - REVOLUTIONARY ANALYSIS SYSTEM
 * Integrates DeepSeek Reasoner + Chat + Multiple Embedding Models
 * 
 * This is the core AI orchestration layer that powers world-class analysis
 */

const axios = require('axios');

class EnhancedAIService {
  constructor() {
    this.deepseekApiKey = process.env.DEEPSEEK_API_KEY;
    this.jinaApiKey = process.env.JINA_API_KEY;
    this.cohereApiKey = process.env.COHERE_API_KEY;
    this.huggingfaceApiKey = process.env.HUGGINGFACE_API_KEY;
    
    // API Endpoints
    this.endpoints = {
      deepseekReasoner: 'https://api.deepseek.com/v1/chat/completions',
      deepseekChat: 'https://api.deepseek.com/v1/chat/completions',
      jinaEmbeddings: 'https://api.jina.ai/v1/embeddings',
      cohereEmbeddings: 'https://api.cohere.ai/v1/embed',
      huggingface: 'https://api-inference.huggingface.co/models'
    };
    
    // Model configurations
    this.models = {
      deepseekReasoner: 'deepseek-reasoner',
      deepseekChat: 'deepseek-chat',
      jinaEmbedding: 'jina-embeddings-v2-base-en',
      cohereEmbedding: 'embed-english-v3.0',
      huggingfaceModels: {
        sentiment: 'cardiffnlp/twitter-roberta-base-sentiment-latest',
        classification: 'microsoft/DialoGPT-medium',
        clustering: 'sentence-transformers/all-MiniLM-L6-v2'
      }
    };
    
    console.log('🚀 Enhanced AI Service initialized with multi-model capabilities');
  }

  /**
   * DEEPSEEK REASONER - MATHEMATICAL GENIUS
   * For complex statistical analysis, mathematical modeling, logical reasoning
   */
  async callDeepSeekReasoner(prompt, data, analysisType = 'statistical') {
    try {
      const systemPrompt = this.getReasonerSystemPrompt(analysisType);
      
      const response = await axios.post(this.endpoints.deepseekReasoner, {
        model: this.models.deepseekReasoner,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: this.formatReasonerPrompt(prompt, data, analysisType) }
        ],
        temperature: 0.1, // Low temperature for precise mathematical analysis
        max_tokens: 4000
      }, {
        headers: {
          'Authorization': `Bearer ${this.deepseekApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return this.parseReasonerResponse(response.data);
    } catch (error) {
      console.error('❌ DeepSeek Reasoner error:', error.message);
      return this.getFallbackReasonerResponse(analysisType);
    }
  }

  /**
   * DEEPSEEK CHAT - BUSINESS INTELLIGENCE EXPERT
   * For strategic insights, business recommendations, narrative generation
   */
  async callDeepSeekChat(prompt, context, insightType = 'business') {
    try {
      const systemPrompt = this.getChatSystemPrompt(insightType);
      
      const response = await axios.post(this.endpoints.deepseekChat, {
        model: this.models.deepseekChat,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: this.formatChatPrompt(prompt, context, insightType) }
        ],
        temperature: 0.7, // Higher temperature for creative business insights
        max_tokens: 3000
      }, {
        headers: {
          'Authorization': `Bearer ${this.deepseekApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return this.parseChatResponse(response.data);
    } catch (error) {
      console.error('❌ DeepSeek Chat error:', error.message);
      return this.getFallbackChatResponse(insightType);
    }
  }

  /**
   * JINA AI EMBEDDINGS - SEMANTIC CLUSTERING
   * For customer behavior clustering, semantic similarity analysis
   */
  async generateJinaEmbeddings(texts, task = 'clustering') {
    try {
      if (!this.jinaApiKey) {
        console.log('⚠️ Jina AI API key not provided, using fallback embeddings');
        return this.generateFallbackEmbeddings(texts);
      }

      const response = await axios.post(this.endpoints.jinaEmbeddings, {
        model: this.models.jinaEmbedding,
        input: Array.isArray(texts) ? texts : [texts],
        task: task
      }, {
        headers: {
          'Authorization': `Bearer ${this.jinaApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return this.parseEmbeddingResponse(response.data, 'jina');
    } catch (error) {
      console.error('❌ Jina AI embeddings error:', error.message);
      return this.generateFallbackEmbeddings(texts);
    }
  }

  /**
   * COHERE EMBEDDINGS - TEXT INTELLIGENCE
   * For product similarity, text analysis, semantic search
   */
  async generateCohereEmbeddings(texts, inputType = 'search_document') {
    try {
      if (!this.cohereApiKey) {
        console.log('⚠️ Cohere AI API key not provided, using fallback embeddings');
        return this.generateFallbackEmbeddings(texts);
      }

      const response = await axios.post(this.endpoints.cohereEmbeddings, {
        model: this.models.cohereEmbedding,
        texts: Array.isArray(texts) ? texts : [texts],
        input_type: inputType
      }, {
        headers: {
          'Authorization': `Bearer ${this.cohereApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return this.parseEmbeddingResponse(response.data, 'cohere');
    } catch (error) {
      console.error('❌ Cohere embeddings error:', error.message);
      return this.generateFallbackEmbeddings(texts);
    }
  }

  /**
   * HUGGINGFACE TRANSFORMERS - SPECIALIZED NLP
   * For sentiment analysis, classification, specialized tasks
   */
  async callHuggingFaceModel(texts, modelType = 'sentiment') {
    try {
      if (!this.huggingfaceApiKey) {
        console.log('⚠️ HuggingFace API key not provided, using fallback analysis');
        return this.getFallbackHuggingFaceResponse(modelType);
      }

      const modelName = this.models.huggingfaceModels[modelType];
      const response = await axios.post(`${this.endpoints.huggingface}/${modelName}`, {
        inputs: Array.isArray(texts) ? texts : [texts]
      }, {
        headers: {
          'Authorization': `Bearer ${this.huggingfaceApiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return this.parseHuggingFaceResponse(response.data, modelType);
    } catch (error) {
      console.error('❌ HuggingFace model error:', error.message);
      return this.getFallbackHuggingFaceResponse(modelType);
    }
  }

  /**
   * ADVANCED SEMANTIC CLUSTERING
   * Combines multiple embedding models for robust clustering
   */
  async performSemanticClustering(data, clusterType = 'customer') {
    try {
      console.log(`🔍 Performing semantic clustering for ${clusterType} analysis...`);
      
      // Extract text features for clustering
      const textFeatures = this.extractTextFeatures(data, clusterType);
      
      // Generate embeddings from multiple sources
      const [jinaEmbeddings, cohereEmbeddings] = await Promise.all([
        this.generateJinaEmbeddings(textFeatures, 'clustering'),
        this.generateCohereEmbeddings(textFeatures, 'clustering')
      ]);
      
      // Combine embeddings for robust clustering
      const combinedEmbeddings = this.combineEmbeddings(jinaEmbeddings, cohereEmbeddings);
      
      // Perform clustering algorithm
      const clusters = this.performKMeansClustering(combinedEmbeddings, data);
      
      return {
        clusters: clusters,
        embeddings: combinedEmbeddings,
        clusterAnalysis: this.analyzeClusterCharacteristics(clusters, data),
        insights: this.generateClusterInsights(clusters, data, clusterType)
      };
    } catch (error) {
      console.error('❌ Semantic clustering error:', error.message);
      return this.getFallbackClusteringResult(data, clusterType);
    }
  }

  /**
   * SYSTEM PROMPTS FOR DIFFERENT AI MODELS
   */
  getReasonerSystemPrompt(analysisType) {
    const prompts = {
      statistical: `You are a world-class data scientist and mathematical analyst. Your expertise includes:
        - Advanced statistical analysis and hypothesis testing
        - Mathematical modeling and optimization
        - Predictive analytics and forecasting
        - Complex KPI calculations and business metrics
        - Correlation analysis and causal inference
        
        Provide precise, mathematically sound analysis with specific numbers, formulas, and statistical insights.
        Focus on actionable mathematical conclusions and quantitative recommendations.`,
        
      predictive: `You are an expert in predictive analytics and machine learning. Your specialties include:
        - Time series forecasting and trend analysis
        - Customer behavior prediction and churn modeling
        - Revenue forecasting and business planning
        - Risk assessment and probability calculations
        - Optimization algorithms and decision science
        
        Provide data-driven predictions with confidence intervals and mathematical justification.`,
        
      optimization: `You are a business optimization expert with deep mathematical knowledge. Focus on:
        - Resource allocation and efficiency optimization
        - Performance improvement strategies
        - Cost-benefit analysis and ROI calculations
        - Process optimization and workflow improvement
        - Strategic planning with quantitative backing
        
        Provide specific optimization recommendations with mathematical proof and expected outcomes.`
    };
    
    return prompts[analysisType] || prompts.statistical;
  }

  getChatSystemPrompt(insightType) {
    const prompts = {
      business: `You are a senior business strategist and management consultant with expertise in:
        - Strategic business analysis and market intelligence
        - Executive-level insights and recommendations
        - Business growth strategies and competitive analysis
        - Risk assessment and opportunity identification
        - Industry best practices and benchmarking
        
        Provide strategic, actionable business insights that executives can implement immediately.
        Focus on growth opportunities, competitive advantages, and strategic recommendations.`,
        
      executive: `You are a C-level executive advisor specializing in:
        - High-level strategic planning and decision making
        - Board-level reporting and executive summaries
        - Market positioning and competitive strategy
        - Investment decisions and resource allocation
        - Long-term business planning and vision
        
        Provide executive-level insights suitable for board presentations and strategic planning.`,
        
      operational: `You are an operations excellence expert focusing on:
        - Operational efficiency and process improvement
        - Performance optimization and productivity enhancement
        - Quality management and continuous improvement
        - Team performance and resource utilization
        - Tactical implementation and execution strategies
        
        Provide practical, implementable operational recommendations with clear action steps.`
    };
    
    return prompts[insightType] || prompts.business;
  }

  /**
   * HELPER METHODS AND UTILITY FUNCTIONS
   */

  formatReasonerPrompt(prompt, data, analysisType) {
    const dataContext = this.prepareDataContext(data);
    return `
ANALYSIS REQUEST: ${prompt}
ANALYSIS TYPE: ${analysisType}

DATA CONTEXT:
${dataContext}

REQUIREMENTS:
- Provide precise mathematical analysis
- Include specific calculations and formulas
- Give confidence scores for predictions
- Suggest optimization opportunities
- Include statistical significance tests where applicable

Please provide a comprehensive ${analysisType} analysis with actionable insights.`;
  }

  formatChatPrompt(prompt, context, insightType) {
    return `
BUSINESS ANALYSIS REQUEST: ${prompt}
INSIGHT TYPE: ${insightType}

BUSINESS CONTEXT:
${JSON.stringify(context, null, 2)}

REQUIREMENTS:
- Provide strategic business insights
- Include actionable recommendations
- Identify growth opportunities and risks
- Suggest competitive advantages
- Focus on executive-level decision making

Please provide comprehensive business intelligence suitable for ${insightType} purposes.`;
  }

  prepareDataContext(data) {
    if (!data || !Array.isArray(data)) return 'No data provided';

    const sample = data.slice(0, 5);
    const summary = {
      totalRecords: data.length,
      columns: Object.keys(data[0] || {}),
      sampleData: sample,
      dataTypes: this.analyzeDataTypes(data[0] || {}),
      basicStats: this.calculateBasicStats(data)
    };

    return JSON.stringify(summary, null, 2);
  }

  analyzeDataTypes(record) {
    const types = {};
    Object.entries(record).forEach(([key, value]) => {
      types[key] = typeof value;
    });
    return types;
  }

  calculateBasicStats(data) {
    const numericColumns = this.getNumericColumns(data);
    const stats = {};

    numericColumns.forEach(column => {
      const values = data.map(row => Number(row[column])).filter(val => !isNaN(val));
      if (values.length > 0) {
        stats[column] = {
          count: values.length,
          mean: values.reduce((a, b) => a + b, 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values)
        };
      }
    });

    return stats;
  }

  getNumericColumns(data) {
    if (!data || data.length === 0) return [];

    const firstRow = data[0];
    return Object.keys(firstRow).filter(key => {
      const value = firstRow[key];
      return !isNaN(Number(value)) && value !== null && value !== '';
    });
  }

  extractTextFeatures(data, clusterType) {
    if (!data || !Array.isArray(data)) return [];

    switch (clusterType) {
      case 'customer':
        return data.map(row =>
          `${row['Συναλλασσόμενος'] || ''} ${row['Παραλήπτης'] || ''} ${row['Πηγή lead'] || ''}`
        ).filter(text => text.trim().length > 0);

      case 'product':
        return data.map(row => row['Περιγραφή'] || '').filter(text => text.trim().length > 0);

      case 'salesperson':
        return data.map(row => row['Πωλητής'] || '').filter(text => text.trim().length > 0);

      default:
        return data.map(row => Object.values(row).join(' ')).filter(text => text.trim().length > 0);
    }
  }

  combineEmbeddings(jinaEmbeddings, cohereEmbeddings) {
    // Simple concatenation strategy - can be enhanced with weighted combinations
    if (!jinaEmbeddings || !cohereEmbeddings) {
      return jinaEmbeddings || cohereEmbeddings || [];
    }

    return jinaEmbeddings.map((jinaEmb, index) => {
      const cohereEmb = cohereEmbeddings[index] || [];
      return [...jinaEmb, ...cohereEmb];
    });
  }

  performKMeansClustering(embeddings, data, k = 5) {
    // Simplified K-means implementation
    // In production, use a proper ML library like ml-kmeans

    if (!embeddings || embeddings.length === 0) {
      return this.createFallbackClusters(data, k);
    }

    // For now, create clusters based on simple grouping
    const clusterSize = Math.ceil(data.length / k);
    const clusters = [];

    for (let i = 0; i < k; i++) {
      const start = i * clusterSize;
      const end = Math.min(start + clusterSize, data.length);
      clusters.push({
        id: i,
        centroid: this.calculateCentroid(embeddings.slice(start, end)),
        members: data.slice(start, end),
        size: end - start
      });
    }

    return clusters;
  }

  calculateCentroid(embeddings) {
    if (!embeddings || embeddings.length === 0) return [];

    const dimensions = embeddings[0].length;
    const centroid = new Array(dimensions).fill(0);

    embeddings.forEach(embedding => {
      embedding.forEach((value, index) => {
        centroid[index] += value;
      });
    });

    return centroid.map(sum => sum / embeddings.length);
  }

  analyzeClusterCharacteristics(clusters, data) {
    return clusters.map(cluster => ({
      id: cluster.id,
      size: cluster.size,
      characteristics: this.extractClusterCharacteristics(cluster.members),
      dominantFeatures: this.findDominantFeatures(cluster.members)
    }));
  }

  extractClusterCharacteristics(members) {
    if (!members || members.length === 0) return {};

    const characteristics = {};

    // Analyze lead sources
    const leadSources = {};
    members.forEach(member => {
      const source = member['Πηγή lead'];
      if (source) leadSources[source] = (leadSources[source] || 0) + 1;
    });
    characteristics.leadSources = leadSources;

    // Analyze salespeople
    const salespeople = {};
    members.forEach(member => {
      const salesperson = member['Πωλητής'];
      if (salesperson) salespeople[salesperson] = (salespeople[salesperson] || 0) + 1;
    });
    characteristics.salespeople = salespeople;

    return characteristics;
  }

  findDominantFeatures(members) {
    // Find the most common features in this cluster
    const features = {};

    members.forEach(member => {
      Object.entries(member).forEach(([key, value]) => {
        if (value && typeof value === 'string') {
          const featureKey = `${key}:${value}`;
          features[featureKey] = (features[featureKey] || 0) + 1;
        }
      });
    });

    return Object.entries(features)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([feature, count]) => ({ feature, count, percentage: (count / members.length) * 100 }));
  }

  generateClusterInsights(clusters, data, clusterType) {
    const insights = [];

    clusters.forEach((cluster, index) => {
      const analysis = this.analyzeClusterCharacteristics([cluster], data)[0];

      insights.push({
        clusterId: cluster.id,
        clusterType: clusterType,
        size: cluster.size,
        percentage: ((cluster.size / data.length) * 100).toFixed(1),
        keyInsights: this.generateClusterKeyInsights(analysis, clusterType),
        recommendations: this.generateClusterRecommendations(analysis, clusterType)
      });
    });

    return insights;
  }

  generateClusterKeyInsights(analysis, clusterType) {
    const insights = [];

    if (analysis.characteristics.leadSources) {
      const topSource = Object.entries(analysis.characteristics.leadSources)
        .sort(([,a], [,b]) => b - a)[0];
      if (topSource) {
        insights.push(`Primary lead source: ${topSource[0]} (${topSource[1]} occurrences)`);
      }
    }

    if (analysis.characteristics.salespeople) {
      const topSalesperson = Object.entries(analysis.characteristics.salespeople)
        .sort(([,a], [,b]) => b - a)[0];
      if (topSalesperson) {
        insights.push(`Top salesperson: ${topSalesperson[0]} (${topSalesperson[1]} transactions)`);
      }
    }

    return insights;
  }

  generateClusterRecommendations(analysis, clusterType) {
    const recommendations = [];

    switch (clusterType) {
      case 'customer':
        recommendations.push('Focus marketing efforts on this customer segment');
        recommendations.push('Develop targeted products for this group');
        break;
      case 'product':
        recommendations.push('Optimize inventory for this product category');
        recommendations.push('Consider bundling strategies');
        break;
      case 'salesperson':
        recommendations.push('Share best practices from top performers');
        recommendations.push('Provide targeted training for improvement');
        break;
    }

    return recommendations;
  }

  // Fallback methods for when APIs are not available
  generateFallbackEmbeddings(texts) {
    // Simple hash-based embeddings as fallback
    return texts.map(text => {
      const hash = this.simpleHash(text);
      return Array.from({length: 384}, (_, i) => (hash + i) % 100 / 100);
    });
  }

  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  createFallbackClusters(data, k) {
    const clusterSize = Math.ceil(data.length / k);
    const clusters = [];

    for (let i = 0; i < k; i++) {
      const start = i * clusterSize;
      const end = Math.min(start + clusterSize, data.length);
      clusters.push({
        id: i,
        centroid: [],
        members: data.slice(start, end),
        size: end - start
      });
    }

    return clusters;
  }

  getFallbackClusteringResult(data, clusterType) {
    return {
      clusters: this.createFallbackClusters(data, 3),
      embeddings: [],
      clusterAnalysis: [],
      insights: [{
        clusterId: 0,
        clusterType: clusterType,
        size: data.length,
        percentage: '100.0',
        keyInsights: ['Fallback clustering applied'],
        recommendations: ['Enable API keys for advanced clustering']
      }]
    };
  }

  parseReasonerResponse(response) {
    try {
      const content = response.choices[0]?.message?.content || '';
      return {
        analysis: content,
        confidence: 0.8,
        type: 'mathematical',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return this.getFallbackReasonerResponse('statistical');
    }
  }

  parseChatResponse(response) {
    try {
      const content = response.choices[0]?.message?.content || '';
      return {
        insights: content,
        confidence: 0.8,
        type: 'business',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return this.getFallbackChatResponse('business');
    }
  }

  parseEmbeddingResponse(response, provider) {
    try {
      switch (provider) {
        case 'jina':
          return response.data?.map(item => item.embedding) || [];
        case 'cohere':
          return response.embeddings || [];
        default:
          return [];
      }
    } catch (error) {
      return [];
    }
  }

  parseHuggingFaceResponse(response, modelType) {
    try {
      switch (modelType) {
        case 'sentiment':
          return response.map(result => ({
            label: result[0]?.label || 'NEUTRAL',
            score: result[0]?.score || 0.5
          }));
        default:
          return response;
      }
    } catch (error) {
      return this.getFallbackHuggingFaceResponse(modelType);
    }
  }

  getFallbackReasonerResponse(analysisType) {
    return {
      analysis: `Fallback ${analysisType} analysis completed. Enable DeepSeek API for advanced mathematical insights.`,
      confidence: 0.3,
      type: 'fallback',
      timestamp: new Date().toISOString()
    };
  }

  getFallbackChatResponse(insightType) {
    return {
      insights: `Fallback ${insightType} insights generated. Enable DeepSeek API for advanced business intelligence.`,
      confidence: 0.3,
      type: 'fallback',
      timestamp: new Date().toISOString()
    };
  }

  getFallbackHuggingFaceResponse(modelType) {
    switch (modelType) {
      case 'sentiment':
        return [{ label: 'NEUTRAL', score: 0.5 }];
      default:
        return { message: 'Fallback response - enable HuggingFace API for advanced NLP' };
    }
  }
}

module.exports = EnhancedAIService;
