const DeepSeekAPI = require('./DeepSeekAPI');
const winston = require('winston');
const { v4: uuidv4 } = require('uuid');

class BusinessAnalyzer {
  constructor() {
    this.deepSeekAPI = new DeepSeekAPI();
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/business-analyzer.log' })
      ]
    });

    this.analysisCache = new Map();
    this.cacheTimeout = 30 * 60 * 1000; // 30 minutes
  }

  async analyzeBusinessData(data, options = {}) {
    try {
      const analysisId = uuidv4();
      const startTime = Date.now();

      this.logger.info('Starting business analysis:', { analysisId, dataSize: data.length });

      // Generate cache key
      const cacheKey = this.generateCacheKey(data, options);
      
      // Check cache first
      if (this.analysisCache.has(cacheKey)) {
        const cached = this.analysisCache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.cacheTimeout) {
          this.logger.info('Returning cached analysis result');
          return cached.result;
        }
      }

      // Perform comprehensive analysis
      const result = {
        analysisId,
        timestamp: new Date().toISOString(),
        dataMetadata: this.analyzeDataMetadata(data),
        statisticalAnalysis: this.performStatisticalAnalysis(data),
        businessMetrics: await this.calculateBusinessMetrics(data),
        trendAnalysis: this.analyzeTrends(data),
        insights: await this.generateBusinessInsights(data),
        recommendations: await this.generateRecommendations(data),
        riskAssessment: this.assessRisks(data),
        kpis: this.identifyKPIs(data),
        processingTime: Date.now() - startTime
      };

      // Cache the result
      this.analysisCache.set(cacheKey, {
        result,
        timestamp: Date.now()
      });

      this.logger.info('Business analysis completed:', {
        analysisId,
        processingTime: result.processingTime
      });

      return result;
    } catch (error) {
      this.logger.error('Business analysis failed:', error);
      throw error;
    }
  }

  analyzeDataMetadata(data) {
    if (!Array.isArray(data) || data.length === 0) {
      return {
        totalRecords: 0,
        columns: [],
        dataQuality: 'poor',
        completeness: 0
      };
    }

    const firstRow = data[0];
    const columns = Object.keys(firstRow);
    const totalRecords = data.length;

    // Analyze data quality
    let totalFields = 0;
    let completeFields = 0;

    data.forEach(row => {
      columns.forEach(col => {
        totalFields++;
        if (row[col] !== null && row[col] !== undefined && row[col] !== '') {
          completeFields++;
        }
      });
    });

    const completeness = totalFields > 0 ? (completeFields / totalFields) * 100 : 0;
    
    return {
      totalRecords,
      columns: columns.map(col => ({
        name: col,
        type: this.inferColumnType(data, col),
        uniqueValues: this.countUniqueValues(data, col),
        nullCount: this.countNulls(data, col)
      })),
      dataQuality: completeness > 90 ? 'excellent' : completeness > 70 ? 'good' : completeness > 50 ? 'fair' : 'poor',
      completeness: Math.round(completeness * 100) / 100
    };
  }

  performStatisticalAnalysis(data) {
    const numericColumns = this.getNumericColumns(data);
    const analysis = {};

    numericColumns.forEach(column => {
      const values = data.map(row => parseFloat(row[column])).filter(val => !isNaN(val));
      
      if (values.length > 0) {
        analysis[column] = {
          count: values.length,
          mean: this.calculateMean(values),
          median: this.calculateMedian(values),
          mode: this.calculateMode(values),
          standardDeviation: this.calculateStandardDeviation(values),
          variance: this.calculateVariance(values),
          min: Math.min(...values),
          max: Math.max(...values),
          range: Math.max(...values) - Math.min(...values),
          quartiles: this.calculateQuartiles(values),
          outliers: this.detectOutliers(values)
        };
      }
    });

    return analysis;
  }

  async calculateBusinessMetrics(data) {
    try {
      const metrics = {};
      const numericColumns = this.getNumericColumns(data);

      // Revenue-related metrics
      const revenueColumns = numericColumns.filter(col => 
        col.toLowerCase().includes('revenue') || 
        col.toLowerCase().includes('sales') || 
        col.toLowerCase().includes('income')
      );

      if (revenueColumns.length > 0) {
        const revenueColumn = revenueColumns[0];
        const revenueData = data.map(row => parseFloat(row[revenueColumn])).filter(val => !isNaN(val));
        
        metrics.revenue = {
          total: revenueData.reduce((sum, val) => sum + val, 0),
          average: this.calculateMean(revenueData),
          growth: this.calculateGrowthRate(revenueData),
          trend: this.calculateTrend(revenueData)
        };
      }

      // Cost-related metrics
      const costColumns = numericColumns.filter(col => 
        col.toLowerCase().includes('cost') || 
        col.toLowerCase().includes('expense') || 
        col.toLowerCase().includes('spending')
      );

      if (costColumns.length > 0) {
        const costColumn = costColumns[0];
        const costData = data.map(row => parseFloat(row[costColumn])).filter(val => !isNaN(val));
        
        metrics.costs = {
          total: costData.reduce((sum, val) => sum + val, 0),
          average: this.calculateMean(costData),
          trend: this.calculateTrend(costData)
        };
      }

      // Profit calculation
      if (metrics.revenue && metrics.costs) {
        metrics.profit = {
          total: metrics.revenue.total - metrics.costs.total,
          margin: ((metrics.revenue.total - metrics.costs.total) / metrics.revenue.total) * 100
        };
      }

      // Customer metrics
      const customerColumns = this.getCustomerColumns(data);
      if (customerColumns.length > 0) {
        metrics.customers = await this.calculateCustomerMetrics(data, customerColumns);
      }

      // Performance ratios
      metrics.ratios = this.calculateBusinessRatios(data, metrics);

      return metrics;
    } catch (error) {
      this.logger.error('Business metrics calculation failed:', error);
      return {};
    }
  }

  analyzeTrends(data) {
    const trends = {};
    const numericColumns = this.getNumericColumns(data);
    const dateColumns = this.getDateColumns(data);

    // Time-based trends
    if (dateColumns.length > 0 && numericColumns.length > 0) {
      const dateColumn = dateColumns[0];
      
      numericColumns.forEach(numCol => {
        const timeSeriesData = this.createTimeSeries(data, dateColumn, numCol);
        trends[numCol] = {
          direction: this.calculateTrendDirection(timeSeriesData),
          strength: this.calculateTrendStrength(timeSeriesData),
          seasonality: this.detectSeasonality(timeSeriesData),
          forecast: this.generateSimpleForecast(timeSeriesData)
        };
      });
    }

    // Sequential trends (if no date column)
    if (dateColumns.length === 0) {
      numericColumns.forEach(col => {
        const values = data.map(row => parseFloat(row[col])).filter(val => !isNaN(val));
        trends[col] = {
          direction: this.calculateSequentialTrend(values),
          volatility: this.calculateVolatility(values)
        };
      });
    }

    return trends;
  }

  async generateBusinessInsights(data) {
    try {
      const prompt = this.createInsightsPrompt(data);
      const response = await this.deepSeekAPI.generateInsights(data, prompt);
      
      return {
        aiInsights: response.insights,
        automaticInsights: this.generateAutomaticInsights(data),
        correlations: this.findCorrelations(data),
        anomalies: this.detectDataAnomalies(data)
      };
    } catch (error) {
      this.logger.error('Insight generation failed:', error);
      return {
        automaticInsights: this.generateAutomaticInsights(data),
        correlations: this.findCorrelations(data),
        anomalies: this.detectDataAnomalies(data)
      };
    }
  }

  async generateRecommendations(data) {
    try {
      const businessContext = this.analyzeBusinessContext(data);
      const analysis = {
        metadata: this.analyzeDataMetadata(data),
        trends: this.analyzeTrends(data),
        metrics: await this.calculateBusinessMetrics(data)
      };

      const response = await this.deepSeekAPI.generateRecommendations(analysis, businessContext);
      
      return {
        aiRecommendations: response.recommendations,
        automaticRecommendations: this.generateAutomaticRecommendations(data),
        priorities: this.prioritizeRecommendations(data),
        actionItems: this.createActionItems(data)
      };
    } catch (error) {
      this.logger.error('Recommendation generation failed:', error);
      return {
        automaticRecommendations: this.generateAutomaticRecommendations(data),
        priorities: this.prioritizeRecommendations(data),
        actionItems: this.createActionItems(data)
      };
    }
  }

  assessRisks(data) {
    const risks = [];
    const numericColumns = this.getNumericColumns(data);

    // Volatility risks
    numericColumns.forEach(col => {
      const values = data.map(row => parseFloat(row[col])).filter(val => !isNaN(val));
      const volatility = this.calculateVolatility(values);
      
      if (volatility > 0.3) {
        risks.push({
          type: 'volatility',
          metric: col,
          severity: volatility > 0.5 ? 'high' : 'medium',
          description: `High volatility detected in ${col}`,
          impact: 'Unpredictable performance and planning difficulties'
        });
      }
    });

    // Data quality risks
    const metadata = this.analyzeDataMetadata(data);
    if (metadata.completeness < 80) {
      risks.push({
        type: 'data_quality',
        severity: metadata.completeness < 50 ? 'high' : 'medium',
        description: `Low data completeness: ${metadata.completeness}%`,
        impact: 'Unreliable analysis and decision-making'
      });
    }

    // Outlier risks
    numericColumns.forEach(col => {
      const values = data.map(row => parseFloat(row[col])).filter(val => !isNaN(val));
      const outliers = this.detectOutliers(values);
      
      if (outliers.length > values.length * 0.1) {
        risks.push({
          type: 'outliers',
          metric: col,
          severity: 'medium',
          description: `High number of outliers in ${col}: ${outliers.length}`,
          impact: 'Skewed analysis results and misleading insights'
        });
      }
    });

    return risks;
  }

  identifyKPIs(data) {
    const kpis = [];
    const columns = Object.keys(data[0] || {});
    
    // Revenue KPIs
    const revenueColumns = columns.filter(col => 
      col.toLowerCase().includes('revenue') || 
      col.toLowerCase().includes('sales') || 
      col.toLowerCase().includes('income')
    );

    revenueColumns.forEach(col => {
      const values = data.map(row => parseFloat(row[col])).filter(val => !isNaN(val));
      if (values.length > 0) {
        kpis.push({
          name: col,
          category: 'revenue',
          current: values[values.length - 1],
          average: this.calculateMean(values),
          trend: this.calculateTrend(values),
          importance: 'high'
        });
      }
    });

    // Cost KPIs
    const costColumns = columns.filter(col => 
      col.toLowerCase().includes('cost') || 
      col.toLowerCase().includes('expense')
    );

    costColumns.forEach(col => {
      const values = data.map(row => parseFloat(row[col])).filter(val => !isNaN(val));
      if (values.length > 0) {
        kpis.push({
          name: col,
          category: 'cost',
          current: values[values.length - 1],
          average: this.calculateMean(values),
          trend: this.calculateTrend(values),
          importance: 'high'
        });
      }
    });

    // Performance KPIs
    const performanceColumns = columns.filter(col => 
      col.toLowerCase().includes('performance') || 
      col.toLowerCase().includes('efficiency') ||
      col.toLowerCase().includes('productivity')
    );

    performanceColumns.forEach(col => {
      const values = data.map(row => parseFloat(row[col])).filter(val => !isNaN(val));
      if (values.length > 0) {
        kpis.push({
          name: col,
          category: 'performance',
          current: values[values.length - 1],
          average: this.calculateMean(values),
          trend: this.calculateTrend(values),
          importance: 'medium'
        });
      }
    });

    return kpis;
  }

  // Helper methods
  generateCacheKey(data, options) {
    const dataHash = require('crypto').createHash('md5')
      .update(JSON.stringify(data.slice(0, 10))) // Hash first 10 rows
      .digest('hex');
    const optionsHash = require('crypto').createHash('md5')
      .update(JSON.stringify(options))
      .digest('hex');
    return `${dataHash}-${optionsHash}`;
  }

  inferColumnType(data, column) {
    const sample = data.slice(0, 100).map(row => row[column]).filter(val => val !== null && val !== undefined);
    
    if (sample.length === 0) return 'empty';
    
    const numericCount = sample.filter(val => !isNaN(Number(val))).length;
    const dateCount = sample.filter(val => !isNaN(Date.parse(val))).length;
    
    if (numericCount > sample.length * 0.8) return 'numeric';
    if (dateCount > sample.length * 0.7) return 'date';
    return 'text';
  }

  getNumericColumns(data) {
    if (!data.length) return [];
    const columns = Object.keys(data[0]);
    return columns.filter(col => this.inferColumnType(data, col) === 'numeric');
  }

  getDateColumns(data) {
    if (!data.length) return [];
    const columns = Object.keys(data[0]);
    return columns.filter(col => this.inferColumnType(data, col) === 'date');
  }

  getCustomerColumns(data) {
    if (!data.length) return [];
    const columns = Object.keys(data[0]);
    return columns.filter(col => 
      col.toLowerCase().includes('customer') || 
      col.toLowerCase().includes('client') ||
      col.toLowerCase().includes('user')
    );
  }

  countUniqueValues(data, column) {
    const values = data.map(row => row[column]);
    return new Set(values).size;
  }

  countNulls(data, column) {
    return data.filter(row => 
      row[column] === null || row[column] === undefined || row[column] === ''
    ).length;
  }

  calculateMean(values) {
    return values.length > 0 ? values.reduce((sum, val) => sum + val, 0) / values.length : 0;
  }

  calculateMedian(values) {
    const sorted = values.slice().sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
  }

  calculateMode(values) {
    const frequency = {};
    values.forEach(val => frequency[val] = (frequency[val] || 0) + 1);
    return Object.keys(frequency).reduce((a, b) => frequency[a] > frequency[b] ? a : b);
  }

  calculateStandardDeviation(values) {
    const mean = this.calculateMean(values);
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return Math.sqrt(variance);
  }

  calculateVariance(values) {
    const mean = this.calculateMean(values);
    return values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
  }

  calculateQuartiles(values) {
    const sorted = values.slice().sort((a, b) => a - b);
    const q1Index = Math.floor(sorted.length * 0.25);
    const q3Index = Math.floor(sorted.length * 0.75);
    
    return {
      q1: sorted[q1Index],
      q2: this.calculateMedian(values),
      q3: sorted[q3Index]
    };
  }

  detectOutliers(values) {
    const quartiles = this.calculateQuartiles(values);
    const iqr = quartiles.q3 - quartiles.q1;
    const lowerBound = quartiles.q1 - 1.5 * iqr;
    const upperBound = quartiles.q3 + 1.5 * iqr;
    
    return values.filter(val => val < lowerBound || val > upperBound);
  }

  calculateTrend(values) {
    if (values.length < 2) return 'insufficient_data';
    
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * values[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    
    if (slope > 0.1) return 'increasing';
    if (slope < -0.1) return 'decreasing';
    return 'stable';
  }

  calculateVolatility(values) {
    if (values.length < 2) return 0;
    const returns = [];
    for (let i = 1; i < values.length; i++) {
      if (values[i - 1] !== 0) {
        returns.push((values[i] - values[i - 1]) / values[i - 1]);
      }
    }
    return this.calculateStandardDeviation(returns);
  }

  // Additional helper methods for insights and recommendations
  generateAutomaticInsights(data) {
    const insights = [];
    const numericColumns = this.getNumericColumns(data);
    
    numericColumns.forEach(col => {
      const values = data.map(row => parseFloat(row[col])).filter(val => !isNaN(val));
      const trend = this.calculateTrend(values);
      const volatility = this.calculateVolatility(values);
      
      if (trend === 'increasing') {
        insights.push(`${col} shows an increasing trend, indicating positive growth.`);
      } else if (trend === 'decreasing') {
        insights.push(`${col} shows a decreasing trend, requiring attention.`);
      }
      
      if (volatility > 0.3) {
        insights.push(`${col} shows high volatility, suggesting unstable performance.`);
      }
    });

    return insights;
  }

  generateAutomaticRecommendations(data) {
    const recommendations = [];
    const metadata = this.analyzeDataMetadata(data);
    
    if (metadata.completeness < 80) {
      recommendations.push({
        priority: 'high',
        category: 'data_quality',
        action: 'Improve data collection processes to increase completeness',
        impact: 'Better analysis accuracy and reliability'
      });
    }

    const numericColumns = this.getNumericColumns(data);
    numericColumns.forEach(col => {
      const values = data.map(row => parseFloat(row[col])).filter(val => !isNaN(val));
      const trend = this.calculateTrend(values);
      
      if (trend === 'decreasing' && col.toLowerCase().includes('revenue')) {
        recommendations.push({
          priority: 'high',
          category: 'revenue',
          action: `Investigate and address declining ${col}`,
          impact: 'Prevent further revenue loss and restore growth'
        });
      }
    });

    return recommendations;
  }

  prioritizeRecommendations(data) {
    // Simple prioritization based on business impact
    return ['revenue', 'cost', 'customer', 'performance', 'data_quality'];
  }

  createActionItems(data) {
    const actionItems = [];
    const risks = this.assessRisks(data);
    
    risks.forEach(risk => {
      if (risk.severity === 'high') {
        actionItems.push({
          title: `Address ${risk.type} risk in ${risk.metric || 'data'}`,
          priority: 'urgent',
          timeline: 'immediate',
          owner: 'data_team'
        });
      }
    });

    return actionItems;
  }

  analyzeBusinessContext(data) {
    const columns = Object.keys(data[0] || {});
    const hasRevenue = columns.some(col => col.toLowerCase().includes('revenue'));
    const hasSales = columns.some(col => col.toLowerCase().includes('sales'));
    const hasCustomers = columns.some(col => col.toLowerCase().includes('customer'));
    
    let context = 'General business data analysis';
    
    if (hasRevenue && hasSales) {
      context = 'Sales and revenue analysis';
    } else if (hasCustomers) {
      context = 'Customer data analysis';
    } else if (hasRevenue) {
      context = 'Financial performance analysis';
    }
    
    return context;
  }

  createInsightsPrompt(data) {
    const metadata = this.analyzeDataMetadata(data);
    return `Analyze business data with ${metadata.totalRecords} records across ${metadata.columns.length} columns. 
    Data quality: ${metadata.dataQuality} (${metadata.completeness}% complete).
    Focus on business implications and actionable insights.`;
  }

  findCorrelations(data) {
    const correlations = [];
    const numericColumns = this.getNumericColumns(data);
    
    for (let i = 0; i < numericColumns.length; i++) {
      for (let j = i + 1; j < numericColumns.length; j++) {
        const col1 = numericColumns[i];
        const col2 = numericColumns[j];
        const correlation = this.calculateCorrelation(data, col1, col2);
        
        if (Math.abs(correlation) > 0.5) {
          correlations.push({
            column1: col1,
            column2: col2,
            correlation,
            strength: Math.abs(correlation) > 0.8 ? 'strong' : 'moderate',
            direction: correlation > 0 ? 'positive' : 'negative'
          });
        }
      }
    }
    
    return correlations;
  }

  calculateCorrelation(data, col1, col2) {
    const pairs = data.map(row => [parseFloat(row[col1]), parseFloat(row[col2])])
      .filter(pair => !isNaN(pair[0]) && !isNaN(pair[1]));
    
    if (pairs.length < 2) return 0;
    
    const n = pairs.length;
    const sum1 = pairs.reduce((sum, pair) => sum + pair[0], 0);
    const sum2 = pairs.reduce((sum, pair) => sum + pair[1], 0);
    const sum1Sq = pairs.reduce((sum, pair) => sum + pair[0] * pair[0], 0);
    const sum2Sq = pairs.reduce((sum, pair) => sum + pair[1] * pair[1], 0);
    const pSum = pairs.reduce((sum, pair) => sum + pair[0] * pair[1], 0);
    
    const num = pSum - (sum1 * sum2 / n);
    const den = Math.sqrt((sum1Sq - sum1 * sum1 / n) * (sum2Sq - sum2 * sum2 / n));
    
    return den === 0 ? 0 : num / den;
  }

  detectDataAnomalies(data) {
    const anomalies = [];
    const numericColumns = this.getNumericColumns(data);
    
    numericColumns.forEach(col => {
      const values = data.map(row => parseFloat(row[col])).filter(val => !isNaN(val));
      const outliers = this.detectOutliers(values);
      
      if (outliers.length > 0) {
        anomalies.push({
          column: col,
          type: 'outliers',
          count: outliers.length,
          values: outliers.slice(0, 5) // Show first 5 outliers
        });
      }
    });
    
    return anomalies;
  }
}

module.exports = BusinessAnalyzer;
