version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7-jammy
    container_name: ai-data-analysis-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: ai_data_analysis
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - ai-data-analysis-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ai-data-analysis-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-data-analysis-network

  # AI Data Analysis Platform
  app:
    build: .
    container_name: ai-data-analysis-app
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: ***************************************************************************
      REDIS_URL: redis://redis:6379
      DEEPSEEK_API_KEY: ${DEEPSEEK_API_KEY}
      JWT_SECRET: ${JWT_SECRET:-your_super_secret_jwt_key_change_this_in_production}
      FRONTEND_URL: http://localhost:3001
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
      - app_temp:/app/temp
    depends_on:
      - mongodb
      - redis
    networks:
      - ai-data-analysis-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (Optional - for production)
  nginx:
    image: nginx:alpine
    container_name: ai-data-analysis-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - app
    networks:
      - ai-data-analysis-network
    profiles:
      - production

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local
  app_temp:
    driver: local

networks:
  ai-data-analysis-network:
    driver: bridge
