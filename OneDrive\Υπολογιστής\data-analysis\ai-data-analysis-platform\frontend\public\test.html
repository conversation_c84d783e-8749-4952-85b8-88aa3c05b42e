<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Data Analysis Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .upload-section {
            border: 2px dashed #e1e5e9;
            border-radius: 10px;
            padding: 3rem 2rem;
            text-align: center;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }
        
        .upload-section:hover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }
        
        .upload-section.dragover {
            border-color: #667eea;
            background-color: #f0f8ff;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .upload-btn:hover {
            transform: translateY(-2px);
        }
        
        .file-info {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
        }
        
        .results {
            display: none;
            margin-top: 2rem;
        }
        
        .status {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .preview {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .preview table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .preview th,
        .preview td {
            border: 1px solid #dee2e6;
            padding: 0.5rem;
            text-align: left;
        }
        
        .preview th {
            background: #e9ecef;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 AI Data Analysis Platform</h1>
            <p>Upload your data files and get AI-powered insights instantly</p>
        </div>
        
        <div class="upload-section" id="uploadSection">
            <div>
                <h3>📁 Drop your file here or click to browse</h3>
                <p>Supported formats: CSV, Excel (.xlsx, .xls), JSON</p>
                <br>
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    Choose File
                </button>
                <input type="file" id="fileInput" class="file-input" accept=".csv,.xlsx,.xls,.json" onchange="handleFileSelect(this.files[0])">
            </div>
        </div>
        
        <div class="file-info" id="fileInfo">
            <h4>Selected File:</h4>
            <p id="fileName"></p>
            <p id="fileSize"></p>
            <button class="upload-btn" onclick="uploadFile()">Upload & Analyze</button>
        </div>
        
        <div class="results" id="results">
            <div class="status" id="status"></div>
            <div class="preview" id="preview"></div>
        </div>
    </div>

    <script>
        let selectedFile = null;
        const apiBaseUrl = 'http://localhost:3001/api';

        // Drag and drop functionality
        const uploadSection = document.getElementById('uploadSection');
        
        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });
        
        uploadSection.addEventListener('dragleave', () => {
            uploadSection.classList.remove('dragover');
        });
        
        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        function handleFileSelect(file) {
            if (!file) return;
            
            selectedFile = file;
            const fileInfo = document.getElementById('fileInfo');
            const fileName = document.getElementById('fileName');
            const fileSize = document.getElementById('fileSize');
            
            fileName.textContent = file.name;
            fileSize.textContent = `Size: ${(file.size / 1024 / 1024).toFixed(2)} MB`;
            
            fileInfo.style.display = 'block';
        }

        async function uploadFile() {
            if (!selectedFile) return;
            
            const results = document.getElementById('results');
            const status = document.getElementById('status');
            const preview = document.getElementById('preview');
            
            results.style.display = 'block';
            status.className = 'status loading';
            status.innerHTML = '🔄 Uploading and processing file...';
            preview.innerHTML = '';
            
            const formData = new FormData();
            formData.append('file', selectedFile);
            
            try {
                const response = await fetch(`${apiBaseUrl}/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    status.className = 'status success';
                    status.innerHTML = `✅ ${result.message}`;
                    
                    // Display file metadata
                    const metadata = result.data.metadata;
                    let metadataHtml = `
                        <h4>📊 File Analysis Results</h4>
                        <p><strong>File ID:</strong> ${result.data.fileId}</p>
                        <p><strong>Rows:</strong> ${metadata.rows}</p>
                        <p><strong>Columns:</strong> ${metadata.columns}</p>
                        <p><strong>Processing Time:</strong> ${metadata.processingTime}ms</p>
                    `;
                    
                    // Display preview data
                    if (result.data.preview && result.data.preview.length > 0) {
                        metadataHtml += '<h4>📋 Data Preview (First 5 rows)</h4>';
                        metadataHtml += '<table>';
                        
                        // Header row
                        const headers = Object.keys(result.data.preview[0]);
                        metadataHtml += '<tr>';
                        headers.forEach(header => {
                            metadataHtml += `<th>${header}</th>`;
                        });
                        metadataHtml += '</tr>';
                        
                        // Data rows
                        result.data.preview.forEach(row => {
                            metadataHtml += '<tr>';
                            headers.forEach(header => {
                                metadataHtml += `<td>${row[header] || ''}</td>`;
                            });
                            metadataHtml += '</tr>';
                        });
                        
                        metadataHtml += '</table>';
                    }
                    
                    preview.innerHTML = metadataHtml;
                    
                } else {
                    status.className = 'status error';
                    status.innerHTML = `❌ Upload failed: ${result.message}`;
                }
                
            } catch (error) {
                status.className = 'status error';
                status.innerHTML = `❌ Upload failed: ${error.message}`;
                console.error('Upload error:', error);
            }
        }

        // Test backend connection on page load
        window.addEventListener('load', async () => {
            try {
                const response = await fetch(`${apiBaseUrl.replace('/api', '')}/health`);
                const result = await response.json();
                console.log('Backend connection test:', result);
            } catch (error) {
                console.error('Backend connection failed:', error);
                const status = document.getElementById('status');
                const results = document.getElementById('results');
                results.style.display = 'block';
                status.className = 'status error';
                status.innerHTML = '❌ Cannot connect to backend server. Make sure the backend is running on http://localhost:3001';
            }
        });
    </script>
</body>
</html>
