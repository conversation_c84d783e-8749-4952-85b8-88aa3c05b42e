const mongoose = require('mongoose');
const winston = require('winston');

class DatabaseConfig {
  constructor() {
    this.connectionString = process.env.MONGODB_URI || 'mongodb://localhost:27017/ai-data-analysis';
    this.options = {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      family: 4
    };
    
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/database.log' })
      ]
    });
  }

  async connect() {
    try {
      await mongoose.connect(this.connectionString, this.options);
      this.logger.info('Connected to MongoDB successfully');
      
      mongoose.connection.on('error', (err) => {
        this.logger.error('MongoDB connection error:', err);
      });

      mongoose.connection.on('disconnected', () => {
        this.logger.warn('MongoDB disconnected');
      });

      return mongoose.connection;
    } catch (error) {
      this.logger.error('Failed to connect to MongoDB:', error);
      throw error;
    }
  }

  async disconnect() {
    try {
      await mongoose.disconnect();
      this.logger.info('Disconnected from MongoDB');
    } catch (error) {
      this.logger.error('Error disconnecting from MongoDB:', error);
      throw error;
    }
  }

  isConnected() {
    return mongoose.connection.readyState === 1;
  }
}

// Database Schemas
const analysisSchema = new mongoose.Schema({
  id: { type: String, required: true, unique: true },
  fileName: { type: String, required: true },
  status: { 
    type: String, 
    enum: ['pending', 'processing', 'completed', 'failed'], 
    default: 'pending' 
  },
  data: { type: mongoose.Schema.Types.Mixed },
  results: { type: mongoose.Schema.Types.Mixed },
  agentResults: {
    executor: { type: mongoose.Schema.Types.Mixed },
    expresser: { type: mongoose.Schema.Types.Mixed },
    reviewer: { type: mongoose.Schema.Types.Mixed },
    dataFining: { type: mongoose.Schema.Types.Mixed },
    storyteller: { type: mongoose.Schema.Types.Mixed }
  },
  insights: { type: mongoose.Schema.Types.Mixed },
  recommendations: [{ type: String }],
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  completedAt: { type: Date },
  metadata: {
    fileSize: { type: Number },
    rowCount: { type: Number },
    columnCount: { type: Number },
    dataTypes: { type: mongoose.Schema.Types.Mixed }
  }
});

const agentSchema = new mongoose.Schema({
  type: { 
    type: String, 
    enum: ['planner', 'executor', 'expresser', 'reviewer', 'dataFining', 'storyteller'],
    required: true 
  },
  status: { 
    type: String, 
    enum: ['idle', 'working', 'completed', 'error'], 
    default: 'idle' 
  },
  currentTask: { type: String },
  lastActivity: { type: Date, default: Date.now },
  results: { type: mongoose.Schema.Types.Mixed },
  error: { type: String },
  performance: {
    tasksCompleted: { type: Number, default: 0 },
    averageTime: { type: Number, default: 0 },
    successRate: { type: Number, default: 100 }
  }
});

const fileSchema = new mongoose.Schema({
  filename: { type: String, required: true },
  originalName: { type: String, required: true },
  path: { type: String, required: true },
  size: { type: Number, required: true },
  mimetype: { type: String, required: true },
  uploadedAt: { type: Date, default: Date.now },
  processedAt: { type: Date },
  analysisId: { type: String },
  metadata: {
    rows: { type: Number },
    columns: { type: Number },
    headers: [{ type: String }],
    dataTypes: { type: mongoose.Schema.Types.Mixed }
  }
});

// Create models
const Analysis = mongoose.model('Analysis', analysisSchema);
const Agent = mongoose.model('Agent', agentSchema);
const File = mongoose.model('File', fileSchema);

module.exports = {
  DatabaseConfig,
  Analysis,
  Agent,
  File,
  mongoose
};
