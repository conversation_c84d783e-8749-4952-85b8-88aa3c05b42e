const axios = require('axios');

class DeepSeekAPI {
  constructor() {
    this.apiKey = process.env.DEEPSEEK_API_KEY || '***********************************';
    this.baseURL = 'https://api.deepseek.com/v1';

    // Validate API key format
    if (!this.apiKey || !this.apiKey.startsWith('sk-')) {
      console.warn('⚠️  Invalid or missing DeepSeek API key. Using fallback analysis only.');
      this.apiKey = null;
    }

    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 90000, // Increased timeout to 90 seconds
      maxContentLength: Infinity,
      maxBodyLength: Infinity,
      // Add keep-alive and connection management
      httpAgent: new (require('http').Agent)({ keepAlive: true }),
      httpsAgent: new (require('https').Agent)({ keepAlive: true })
    });

    // Add response interceptor for better error handling
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.code === 'ECONNRESET' || error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
          console.log(`🔄 Connection issue (${error.code}), retrying...`);
          // Implement simple retry logic
          if (error.config && !error.config.__isRetryRequest) {
            error.config.__isRetryRequest = true;
            await new Promise(resolve => setTimeout(resolve, 3000)); // Wait 3 seconds
            return this.client.request(error.config);
          }
        }
        throw error;
      }
    );
  }

  async analyzeBusinessContext(data, metadata = {}) {
    const headers = Object.keys(data[0] || {});

    // Detect Greek healthcare context
    const isHealthcareData = headers.some(h =>
      h.includes('παραλήπτης') || h.includes('ασθενής') ||
      h.includes('εντολέας') || h.includes('γιατρός') ||
      h.toLowerCase().includes('patient') || h.toLowerCase().includes('doctor')
    );

    const prompt = `
    You are an expert data analyst specializing in Greek business and healthcare data analysis.

    IMPORTANT CONTEXT DETECTION:
    - If data contains "παραλήπτης" = patient/ασθενής
    - If data contains "εντολέας" = doctor/γιατρός
    - Greek column names like "Ημ/νία" = Date, "Πρώτη πώληση" = First Sale, "Νέα Πώληση" = New Sale

    Analyze this ${isHealthcareData ? 'HEALTHCARE' : 'BUSINESS'} data:

    Column Headers: ${headers.join(', ')}
    Sample Records: ${JSON.stringify(data.slice(0, 3), null, 2)}
    Total Records: ${data.length}

    Provide comprehensive analysis in JSON format:
    {
      "data_type": "${isHealthcareData ? 'healthcare' : 'business'}",
      "business_context": "detailed context analysis",
      "industry": "specific industry identification",
      "key_entities": ["main entities in the data"],
      "relationships": ["key relationships between entities"],
      "data_quality": "assessment of data completeness",
      "greek_context": "Greek business/healthcare specific insights",
      "confidence": 0.95,
      "recommendations": ["analysis recommendations"]
    }
    `;

    try {
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: 'You are a specialized data analyst expert in Greek business and healthcare data. You understand Greek terminology and can identify healthcare vs business contexts. Always provide detailed, accurate analysis.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.2,
        max_tokens: 1500
      });

      return this.parseJSONResponse(response.data.choices[0].message.content);
    } catch (error) {
      console.error('DeepSeek API Error:', error);
      return this.fallbackBusinessAnalysis(data);
    }
  }

  async discoverKPIs(data, businessContext = {}) {
    const headers = Object.keys(data[0] || {});
    const prompt = `
    As a KPI expert for ${businessContext.businessType || 'business'} in ${businessContext.industry || 'general industry'}, 
    analyze this data structure and identify the most relevant KPIs:
    
    Available columns: ${headers.join(', ')}
    Business context: ${JSON.stringify(businessContext, null, 2)}
    
    Identify and prioritize KPIs in JSON format:
    {
      "primary_kpis": ["top 3 most important KPIs"],
      "secondary_kpis": ["next 3-5 important KPIs"],
      "calculated_kpis": ["KPIs that can be calculated from existing data"],
      "missing_kpis": ["important KPIs not available in current data"],
      "kpi_definitions": {"kpi_name": "clear definition and calculation method"},
      "business_impact": {"kpi_name": "impact on business success"},
      "recommendations": ["suggestions for KPI optimization"]
    }
    `;

    try {
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: 'You are a KPI and business metrics expert with deep knowledge across industries.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.2,
        max_tokens: 1500
      });

      return this.parseJSONResponse(response.data.choices[0].message.content);
    } catch (error) {
      console.error('DeepSeek KPI Discovery Error:', error);
      return this.fallbackKPIDiscovery(data);
    }
  }

  async performStatisticalAnalysis(data, context = {}) {
    // Add data validation
    if (!data || !Array.isArray(data) || data.length === 0) {
      console.log('No valid data provided, using fallback analysis');
      return this.fallbackStatisticalAnalysis(data || []);
    }

    const headers = Object.keys(data[0] || {});
    const numericColumns = this.getNumericColumns(data);
    const dateColumns = this.detectTimeColumns(data);

    // Detect data patterns
    const isHealthcareData = headers.some(h =>
      h.includes('παραλήπτης') || h.includes('ασθενής') ||
      h.includes('εντολέας') || h.includes('γιατρός')
    );

    // If no API key, use fallback
    if (!this.apiKey) {
      console.log('No DeepSeek API key provided, using fallback analysis');
      return this.fallbackStatisticalAnalysis(data);
    }

    const prompt = `
    You are an expert data analyst specializing in Greek ${isHealthcareData ? 'healthcare' : 'business'} data.

    COMPREHENSIVE DATA ANALYSIS REQUEST:

    Dataset Overview:
    - Total Records: ${data.length}
    - All Columns: ${headers.join(', ')}
    - Numeric Columns: ${numericColumns.join(', ')}
    - Date Columns: ${dateColumns.join(', ')}
    - Data Type: ${isHealthcareData ? 'Healthcare/Medical' : 'Business/Sales'}

    Greek Context Understanding:
    - "Ημ/νία" = Date
    - "Πρώτη πώληση" = First Sale
    - "Νέα Πώληση" = New Sale
    - "παραλήπτης" = Patient
    - "εντολέας" = Doctor

    Sample Data: ${JSON.stringify(data.slice(0, 5), null, 2)}

    PERFORM DEEP STATISTICAL ANALYSIS:

    1. Calculate comprehensive descriptive statistics
    2. Identify correlations and patterns
    3. Detect trends and seasonality
    4. Extract meaningful KPIs
    5. Provide business/healthcare insights
    6. Identify anomalies or outliers
    7. Calculate growth rates and performance metrics

    Return detailed JSON analysis:
    {
      "descriptive_stats": {
        "column_name": {
          "mean": number,
          "median": number,
          "std_dev": number,
          "min": number,
          "max": number,
          "count": number,
          "trend": "increasing/decreasing/stable",
          "growth_rate": number,
          "coefficient_of_variation": number
        }
      },
      "correlations": [
        {
          "column1": "name",
          "column2": "name",
          "correlation": number,
          "significance": "high/medium/low",
          "business_meaning": "interpretation"
        }
      ],
      "key_insights": [
        "Most important findings from the data"
      ],
      "performance_metrics": {
        "total_revenue": number,
        "average_transaction": number,
        "growth_rate": number,
        "trend_direction": "positive/negative/stable"
      },
      "anomalies": ["unusual patterns detected"],
      "recommendations": ["actionable insights"],
      "data_quality_score": number
    }
    `;

    try {
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: 'You are a senior data analyst and statistician specializing in Greek business and healthcare data analysis. Provide comprehensive, accurate statistical analysis with business context. Always respond with valid JSON.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.1,
        max_tokens: 3000,
        stream: false
      });

      if (response.data && response.data.choices && response.data.choices[0]) {
        return this.parseJSONResponse(response.data.choices[0].message.content);
      } else {
        throw new Error('Invalid API response structure');
      }
    } catch (error) {
      console.error('DeepSeek Statistical Analysis Error:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
      return this.fallbackStatisticalAnalysis(data);
    }
  }

  async generateForecasts(data, context = {}) {
    const timeColumns = this.detectTimeColumns(data);
    const numericColumns = this.getNumericColumns(data);
    
    const prompt = `
    Generate business forecasts based on this data:
    
    Time columns: ${timeColumns.join(', ')}
    Numeric columns: ${numericColumns.join(', ')}
    Business context: ${JSON.stringify(context, null, 2)}
    Recent data trends: ${JSON.stringify(data.slice(-10), null, 2)}
    
    Provide forecasting analysis in JSON format:
    {
      "forecasts": [
        {
          "metric": "metric_name",
          "current_value": number,
          "forecast_next_month": number,
          "forecast_next_quarter": number,
          "forecast_next_year": number,
          "confidence_interval": {"lower": number, "upper": number},
          "trend": "increasing/decreasing/stable",
          "growth_rate": number,
          "confidence_score": number
        }
      ],
      "assumptions": ["key assumptions for forecasts"],
      "risk_factors": ["factors that could impact forecasts"],
      "recommendations": ["strategic recommendations based on forecasts"]
    }
    `;

    try {
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: 'You are a business forecasting expert. Respond only with valid JSON.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.2,
        max_tokens: 1500,
        stream: false
      });

      if (response.data && response.data.choices && response.data.choices[0]) {
        return this.parseJSONResponse(response.data.choices[0].message.content);
      } else {
        throw new Error('Invalid API response structure');
      }
    } catch (error) {
      console.error('DeepSeek Forecasting Error:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
      return this.fallbackForecasting(data);
    }
  }

  async generateBusinessRecommendations(analysisResults, context = {}) {
    const prompt = `
    Based on comprehensive business analysis, generate actionable recommendations:
    
    Business Context: ${JSON.stringify(context, null, 2)}
    Analysis Results: ${JSON.stringify(analysisResults, null, 2)}
    
    Generate strategic recommendations in JSON format:
    {
      "executive_summary": "2-3 sentence overview of key findings",
      "recommendations": [
        {
          "priority": "High/Medium/Low",
          "category": "Revenue/Operations/Customer/Marketing/Finance",
          "title": "Clear action title",
          "description": "Detailed recommendation",
          "expected_impact": "Quantified expected outcome",
          "implementation_timeline": "Time to implement",
          "resources_needed": ["required resources"],
          "success_metrics": ["how to measure success"],
          "risk_level": "Low/Medium/High"
        }
      ],
      "quick_wins": ["immediate actions for fast results"],
      "long_term_strategy": ["strategic initiatives for sustained growth"],
      "key_insights": ["most important data-driven insights"]
    }
    `;

    try {
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: 'You are a senior business consultant with expertise in data-driven strategy and recommendations.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.3,
        max_tokens: 2500
      });

      return this.parseJSONResponse(response.data.choices[0].message.content);
    } catch (error) {
      console.error('DeepSeek Recommendations Error:', error);
      return this.fallbackRecommendations();
    }
  }

  // Utility methods
  parseJSONResponse(content) {
    try {
      // Extract JSON from markdown code blocks if present
      const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/) || content.match(/```\n([\s\S]*?)\n```/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[1]);
      }
      return JSON.parse(content);
    } catch (error) {
      console.error('Failed to parse JSON response:', error);
      return { error: 'Failed to parse response', raw: content };
    }
  }

  getNumericColumns(data) {
    if (!data || !Array.isArray(data) || data.length === 0) return [];
    if (!data[0] || typeof data[0] !== 'object') return [];

    return Object.keys(data[0]).filter(key =>
      data.some(row => {
        const value = row && row[key];
        return typeof value === 'number' && !isNaN(value);
      })
    );
  }

  detectTimeColumns(data) {
    if (!data || data.length === 0) return [];
    return Object.keys(data[0]).filter(key => 
      key.toLowerCase().includes('date') || 
      key.toLowerCase().includes('time') ||
      key.toLowerCase().includes('month') ||
      key.toLowerCase().includes('year')
    );
  }

  // Fallback methods for when API fails
  fallbackBusinessAnalysis(data) {
    const headers = Object.keys(data[0] || {});
    let businessType = "General Business";
    
    if (headers.some(h => h.toLowerCase().includes('sale') || h.toLowerCase().includes('revenue'))) {
      businessType = "Retail/Sales";
    } else if (headers.some(h => h.toLowerCase().includes('customer'))) {
      businessType = "Service Business";
    }
    
    return {
      businessType,
      industry: "General",
      context: "Business operations analysis",
      confidence: 0.7,
      reasoning: "Fallback analysis based on data structure"
    };
  }

  fallbackKPIDiscovery(data) {
    const numericColumns = this.getNumericColumns(data);
    return {
      primary_kpis: numericColumns.slice(0, 3),
      secondary_kpis: numericColumns.slice(3, 6),
      total_metrics: numericColumns.length
    };
  }

  fallbackStatisticalAnalysis(data) {
    const numericColumns = this.getNumericColumns(data);
    const stats = {};
    const correlations = [];

    numericColumns.forEach(col => {
      const values = data.map(row => row[col]).filter(v => !isNaN(v) && v !== null);
      if (values.length > 0) {
        const sum = values.reduce((a, b) => a + b, 0);
        const mean = sum / values.length;
        const sortedValues = [...values].sort((a, b) => a - b);
        const median = sortedValues.length % 2 === 0
          ? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
          : sortedValues[Math.floor(sortedValues.length / 2)];

        stats[col] = {
          mean,
          median,
          min: Math.min(...values),
          max: Math.max(...values),
          count: values.length,
          trend: this.calculateTrend(values),
          std_dev: this.calculateStdDev(values, mean)
        };
      }
    });

    // Calculate basic correlations between numeric columns
    for (let i = 0; i < numericColumns.length; i++) {
      for (let j = i + 1; j < numericColumns.length; j++) {
        const col1 = numericColumns[i];
        const col2 = numericColumns[j];
        const correlation = this.calculateCorrelation(data, col1, col2);
        if (!isNaN(correlation)) {
          correlations.push({
            column1: col1,
            column2: col2,
            correlation: correlation,
            significance: Math.abs(correlation) > 0.7 ? 'high' : Math.abs(correlation) > 0.4 ? 'medium' : 'low'
          });
        }
      }
    }

    return {
      descriptive_stats: stats,
      correlations: correlations,
      business_insights: [
        "Statistical analysis completed using fallback method",
        `Analyzed ${numericColumns.length} numeric columns with ${data.length} records`,
        "Consider upgrading to AI-powered analysis for deeper insights"
      ]
    };
  }

  calculateTrend(values) {
    if (values.length < 3) return 'stable';
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;
    const change = (secondAvg - firstAvg) / firstAvg;
    return change > 0.05 ? 'increasing' : change < -0.05 ? 'decreasing' : 'stable';
  }

  calculateStdDev(values, mean) {
    const squaredDiffs = values.map(v => Math.pow(v - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
    return Math.sqrt(avgSquaredDiff);
  }

  calculateCorrelation(data, col1, col2) {
    const values1 = data.map(row => parseFloat(row[col1])).filter(v => !isNaN(v));
    const values2 = data.map(row => parseFloat(row[col2])).filter(v => !isNaN(v));

    if (values1.length !== values2.length || values1.length < 2) return 0;

    const mean1 = values1.reduce((a, b) => a + b, 0) / values1.length;
    const mean2 = values2.reduce((a, b) => a + b, 0) / values2.length;

    let numerator = 0;
    let sum1 = 0;
    let sum2 = 0;

    for (let i = 0; i < values1.length; i++) {
      const diff1 = values1[i] - mean1;
      const diff2 = values2[i] - mean2;
      numerator += diff1 * diff2;
      sum1 += diff1 * diff1;
      sum2 += diff2 * diff2;
    }

    const denominator = Math.sqrt(sum1 * sum2);
    return denominator === 0 ? 0 : numerator / denominator;
  }

  fallbackForecasting(data) {
    const numericColumns = this.getNumericColumns(data);
    return {
      forecasts: numericColumns.map(col => ({
        metric: col,
        trend: 'stable',
        confidence_score: 0.5
      }))
    };
  }

  fallbackRecommendations() {
    return {
      recommendations: [
        {
          priority: "Medium",
          category: "Operations",
          title: "Improve data collection",
          description: "Enhance data quality for better analysis",
          expected_impact: "Better insights",
          implementation_timeline: "1-2 months"
        }
      ]
    };
  }
}

module.exports = DeepSeekAPI;