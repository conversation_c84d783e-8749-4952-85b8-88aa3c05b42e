const axios = require('axios');

class DeepSeekAPI {
  constructor() {
    this.apiKey = process.env.DEEPSEEK_API_KEY;
    this.baseURL = 'https://api.deepseek.com/v1';
    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
  }

  async analyzeBusinessContext(data, metadata = {}) {
    const prompt = `
    You are an expert business analyst. Analyze the following business data and determine:
    1. Business type and industry
    2. Key business context
    3. Main business model
    4. Critical success factors
    
    Data structure: ${JSON.stringify(Object.keys(data[0] || {}), null, 2)}
    Sample data: ${JSON.stringify(data.slice(0, 3), null, 2)}
    
    Provide a comprehensive business context analysis in JSON format with:
    - businessType
    - industry
    - context
    - businessModel
    - criticalFactors
    - confidence (0-1)
    - reasoning
    `;

    try {
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-v3',
        messages: [
          { role: 'system', content: 'You are a business intelligence expert specializing in data analysis and business context understanding.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.3,
        max_tokens: 1000
      });

      return this.parseJSONResponse(response.data.choices[0].message.content);
    } catch (error) {
      console.error('DeepSeek API Error:', error);
      return this.fallbackBusinessAnalysis(data);
    }
  }

  async discoverKPIs(data, businessContext = {}) {
    const headers = Object.keys(data[0] || {});
    const prompt = `
    As a KPI expert for ${businessContext.businessType || 'business'} in ${businessContext.industry || 'general industry'}, 
    analyze this data structure and identify the most relevant KPIs:
    
    Available columns: ${headers.join(', ')}
    Business context: ${JSON.stringify(businessContext, null, 2)}
    
    Identify and prioritize KPIs in JSON format:
    {
      "primary_kpis": ["top 3 most important KPIs"],
      "secondary_kpis": ["next 3-5 important KPIs"],
      "calculated_kpis": ["KPIs that can be calculated from existing data"],
      "missing_kpis": ["important KPIs not available in current data"],
      "kpi_definitions": {"kpi_name": "clear definition and calculation method"},
      "business_impact": {"kpi_name": "impact on business success"},
      "recommendations": ["suggestions for KPI optimization"]
    }
    `;

    try {
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-v3',
        messages: [
          { role: 'system', content: 'You are a KPI and business metrics expert with deep knowledge across industries.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.2,
        max_tokens: 1500
      });

      return this.parseJSONResponse(response.data.choices[0].message.content);
    } catch (error) {
      console.error('DeepSeek KPI Discovery Error:', error);
      return this.fallbackKPIDiscovery(data);
    }
  }

  async performStatisticalAnalysis(data, kpis = {}) {
    const numericColumns = this.getNumericColumns(data);
    const prompt = `
    Perform comprehensive statistical analysis on this business data:
    
    Numeric columns: ${numericColumns.join(', ')}
    Key KPIs: ${JSON.stringify(kpis.primary_kpis || [], null, 2)}
    Data sample: ${JSON.stringify(data.slice(0, 5), null, 2)}
    
    Provide statistical insights in JSON format:
    {
      "descriptive_stats": {
        "column_name": {
          "mean": number,
          "median": number,
          "std_dev": number,
          "min": number,
          "max": number,
          "trend": "increasing/decreasing/stable",
          "outliers_count": number
        }
      },
      "correlations": [
        {"column1": "name", "column2": "name", "correlation": number, "significance": "high/medium/low"}
      ],
      "time_series_analysis": {
        "seasonal_patterns": ["detected patterns"],
        "trend_analysis": "overall trend description",
        "anomalies": ["unusual data points or periods"]
      },
      "business_insights": [
        "actionable insights from statistical analysis"
      ]
    }
    `;

    try {
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-r1',
        messages: [
          { role: 'system', content: 'You are a data scientist expert in business analytics and statistical analysis.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.1,
        max_tokens: 2000
      });

      return this.parseJSONResponse(response.data.choices[0].message.content);
    } catch (error) {
      console.error('DeepSeek Statistical Analysis Error:', error);
      return this.fallbackStatisticalAnalysis(data);
    }
  }

  async generateForecasts(data, context = {}) {
    const timeColumns = this.detectTimeColumns(data);
    const numericColumns = this.getNumericColumns(data);
    
    const prompt = `
    Generate business forecasts based on this data:
    
    Time columns: ${timeColumns.join(', ')}
    Numeric columns: ${numericColumns.join(', ')}
    Business context: ${JSON.stringify(context, null, 2)}
    Recent data trends: ${JSON.stringify(data.slice(-10), null, 2)}
    
    Provide forecasting analysis in JSON format:
    {
      "forecasts": [
        {
          "metric": "metric_name",
          "current_value": number,
          "forecast_next_month": number,
          "forecast_next_quarter": number,
          "forecast_next_year": number,
          "confidence_interval": {"lower": number, "upper": number},
          "trend": "increasing/decreasing/stable",
          "growth_rate": number,
          "confidence_score": number
        }
      ],
      "assumptions": ["key assumptions for forecasts"],
      "risk_factors": ["factors that could impact forecasts"],
      "recommendations": ["strategic recommendations based on forecasts"]
    }
    `;

    try {
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-v3',
        messages: [
          { role: 'system', content: 'You are a business forecasting expert with expertise in predictive analytics.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.2,
        max_tokens: 2000
      });

      return this.parseJSONResponse(response.data.choices[0].message.content);
    } catch (error) {
      console.error('DeepSeek Forecasting Error:', error);
      return this.fallbackForecasting(data);
    }
  }

  async generateBusinessRecommendations(analysisResults, context = {}) {
    const prompt = `
    Based on comprehensive business analysis, generate actionable recommendations:
    
    Business Context: ${JSON.stringify(context, null, 2)}
    Analysis Results: ${JSON.stringify(analysisResults, null, 2)}
    
    Generate strategic recommendations in JSON format:
    {
      "executive_summary": "2-3 sentence overview of key findings",
      "recommendations": [
        {
          "priority": "High/Medium/Low",
          "category": "Revenue/Operations/Customer/Marketing/Finance",
          "title": "Clear action title",
          "description": "Detailed recommendation",
          "expected_impact": "Quantified expected outcome",
          "implementation_timeline": "Time to implement",
          "resources_needed": ["required resources"],
          "success_metrics": ["how to measure success"],
          "risk_level": "Low/Medium/High"
        }
      ],
      "quick_wins": ["immediate actions for fast results"],
      "long_term_strategy": ["strategic initiatives for sustained growth"],
      "key_insights": ["most important data-driven insights"]
    }
    `;

    try {
      const response = await this.client.post('/chat/completions', {
        model: 'deepseek-v3',
        messages: [
          { role: 'system', content: 'You are a senior business consultant with expertise in data-driven strategy and recommendations.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.3,
        max_tokens: 2500
      });

      return this.parseJSONResponse(response.data.choices[0].message.content);
    } catch (error) {
      console.error('DeepSeek Recommendations Error:', error);
      return this.fallbackRecommendations();
    }
  }

  // Utility methods
  parseJSONResponse(content) {
    try {
      // Extract JSON from markdown code blocks if present
      const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/) || content.match(/```\n([\s\S]*?)\n```/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[1]);
      }
      return JSON.parse(content);
    } catch (error) {
      console.error('Failed to parse JSON response:', error);
      return { error: 'Failed to parse response', raw: content };
    }
  }

  getNumericColumns(data) {
    if (!data || data.length === 0) return [];
    return Object.keys(data[0]).filter(key => 
      data.some(row => typeof row[key] === 'number' && !isNaN(row[key]))
    );
  }

  detectTimeColumns(data) {
    if (!data || data.length === 0) return [];
    return Object.keys(data[0]).filter(key => 
      key.toLowerCase().includes('date') || 
      key.toLowerCase().includes('time') ||
      key.toLowerCase().includes('month') ||
      key.toLowerCase().includes('year')
    );
  }

  // Fallback methods for when API fails
  fallbackBusinessAnalysis(data) {
    const headers = Object.keys(data[0] || {});
    let businessType = "General Business";
    
    if (headers.some(h => h.toLowerCase().includes('sale') || h.toLowerCase().includes('revenue'))) {
      businessType = "Retail/Sales";
    } else if (headers.some(h => h.toLowerCase().includes('customer'))) {
      businessType = "Service Business";
    }
    
    return {
      businessType,
      industry: "General",
      context: "Business operations analysis",
      confidence: 0.7,
      reasoning: "Fallback analysis based on data structure"
    };
  }

  fallbackKPIDiscovery(data) {
    const numericColumns = this.getNumericColumns(data);
    return {
      primary_kpis: numericColumns.slice(0, 3),
      secondary_kpis: numericColumns.slice(3, 6),
      total_metrics: numericColumns.length
    };
  }

  fallbackStatisticalAnalysis(data) {
    const numericColumns = this.getNumericColumns(data);
    const stats = {};
    
    numericColumns.forEach(col => {
      const values = data.map(row => row[col]).filter(v => !isNaN(v) && v !== null);
      if (values.length > 0) {
        const sum = values.reduce((a, b) => a + b, 0);
        const mean = sum / values.length;
        stats[col] = {
          mean,
          min: Math.min(...values),
          max: Math.max(...values),
          count: values.length
        };
      }
    });
    
    return { descriptive_stats: stats };
  }

  fallbackForecasting(data) {
    const numericColumns = this.getNumericColumns(data);
    return {
      forecasts: numericColumns.map(col => ({
        metric: col,
        trend: 'stable',
        confidence_score: 0.5
      }))
    };
  }

  fallbackRecommendations() {
    return {
      recommendations: [
        {
          priority: "Medium",
          category: "Operations",
          title: "Improve data collection",
          description: "Enhance data quality for better analysis",
          expected_impact: "Better insights",
          implementation_timeline: "1-2 months"
        }
      ]
    };
  }
}

module.exports = DeepSeekAPI;