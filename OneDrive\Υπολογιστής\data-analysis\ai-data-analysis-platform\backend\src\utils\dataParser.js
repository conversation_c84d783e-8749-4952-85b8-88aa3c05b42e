const fs = require('fs').promises;
const path = require('path');
const winston = require('winston');

class DataParser {
  constructor() {
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({ filename: 'logs/data-parser.log' })
      ]
    });
  }

  // Parse and clean data for analysis
  async parseAndCleanData(data, options = {}) {
    try {
      const {
        removeNulls = true,
        standardizeHeaders = true,
        inferTypes = true,
        handleOutliers = false,
        fillMissingValues = false
      } = options;

      this.logger.info('Starting data parsing and cleaning:', {
        records: data.length,
        options
      });

      let cleanedData = [...data];

      // Standardize headers
      if (standardizeHeaders && cleanedData.length > 0) {
        cleanedData = this.standardizeHeaders(cleanedData);
      }

      // Remove null/empty records
      if (removeNulls) {
        cleanedData = this.removeNullRecords(cleanedData);
      }

      // Infer and convert data types
      if (inferTypes) {
        cleanedData = this.inferAndConvertTypes(cleanedData);
      }

      // Fill missing values
      if (fillMissingValues) {
        cleanedData = this.fillMissingValues(cleanedData);
      }

      // Handle outliers
      if (handleOutliers) {
        cleanedData = this.handleOutliers(cleanedData);
      }

      const result = {
        data: cleanedData,
        metadata: this.generateMetadata(data, cleanedData, options),
        statistics: this.generateBasicStatistics(cleanedData)
      };

      this.logger.info('Data parsing completed:', {
        originalRecords: data.length,
        cleanedRecords: cleanedData.length,
        removed: data.length - cleanedData.length
      });

      return result;
    } catch (error) {
      this.logger.error('Data parsing failed:', error);
      throw error;
    }
  }

  // Standardize column headers
  standardizeHeaders(data) {
    if (!data.length) return data;

    const firstRow = data[0];
    const headerMapping = {};

    // Create standardized header mapping
    Object.keys(firstRow).forEach(header => {
      const standardized = header
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '_')
        .replace(/_+/g, '_')
        .replace(/^_|_$/g, '');
      headerMapping[header] = standardized;
    });

    // Apply header mapping to all records
    return data.map(row => {
      const newRow = {};
      Object.keys(row).forEach(oldHeader => {
        const newHeader = headerMapping[oldHeader];
        newRow[newHeader] = row[oldHeader];
      });
      return newRow;
    });
  }

  // Remove records with too many null values
  removeNullRecords(data, threshold = 0.5) {
    return data.filter(row => {
      const values = Object.values(row);
      const nonNullValues = values.filter(val => 
        val !== null && val !== undefined && val !== ''
      );
      return (nonNullValues.length / values.length) >= threshold;
    });
  }

  // Infer and convert data types
  inferAndConvertTypes(data) {
    if (!data.length) return data;

    const columns = Object.keys(data[0]);
    const typeMapping = {};

    // Analyze each column to infer type
    columns.forEach(column => {
      const sample = data.slice(0, Math.min(100, data.length))
        .map(row => row[column])
        .filter(val => val !== null && val !== undefined && val !== '');

      typeMapping[column] = this.inferColumnType(sample);
    });

    // Convert values based on inferred types
    return data.map(row => {
      const convertedRow = { ...row };
      
      Object.keys(typeMapping).forEach(column => {
        const type = typeMapping[column];
        const value = row[column];

        if (value === null || value === undefined || value === '') {
          return; // Keep as is
        }

        try {
          switch (type) {
            case 'number':
              convertedRow[column] = this.parseNumber(value);
              break;
            case 'date':
              convertedRow[column] = this.parseDate(value);
              break;
            case 'boolean':
              convertedRow[column] = this.parseBoolean(value);
              break;
            case 'string':
            default:
              convertedRow[column] = String(value).trim();
              break;
          }
        } catch (error) {
          // Keep original value if conversion fails
          this.logger.warn('Type conversion failed:', {
            column,
            value,
            type,
            error: error.message
          });
        }
      });

      return convertedRow;
    });
  }

  // Infer the type of a column based on sample values
  inferColumnType(sample) {
    if (sample.length === 0) return 'string';

    // Check for numbers
    const numericValues = sample.filter(val => this.isNumeric(val));
    if (numericValues.length > sample.length * 0.8) {
      return 'number';
    }

    // Check for dates
    const dateValues = sample.filter(val => this.isDate(val));
    if (dateValues.length > sample.length * 0.7) {
      return 'date';
    }

    // Check for booleans
    const booleanValues = sample.filter(val => this.isBoolean(val));
    if (booleanValues.length > sample.length * 0.8) {
      return 'boolean';
    }

    return 'string';
  }

  // Type checking utilities
  isNumeric(value) {
    if (typeof value === 'number') return !isNaN(value);
    if (typeof value === 'string') {
      const cleaned = value.replace(/[,$%]/g, '');
      return !isNaN(parseFloat(cleaned)) && isFinite(cleaned);
    }
    return false;
  }

  isDate(value) {
    if (value instanceof Date) return !isNaN(value.getTime());
    if (typeof value === 'string') {
      const date = new Date(value);
      return !isNaN(date.getTime()) && value.length >= 8;
    }
    return false;
  }

  isBoolean(value) {
    if (typeof value === 'boolean') return true;
    if (typeof value === 'string') {
      const lower = value.toLowerCase().trim();
      return ['true', 'false', 'yes', 'no', '1', '0', 'y', 'n'].includes(lower);
    }
    return false;
  }

  // Type conversion utilities
  parseNumber(value) {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      // Remove common formatting characters
      const cleaned = value.replace(/[,$%]/g, '');
      const num = parseFloat(cleaned);
      if (isNaN(num)) throw new Error(`Cannot convert "${value}" to number`);
      return num;
    }
    throw new Error(`Cannot convert ${typeof value} to number`);
  }

  parseDate(value) {
    if (value instanceof Date) return value;
    
    const date = new Date(value);
    if (isNaN(date.getTime())) {
      throw new Error(`Cannot convert "${value}" to date`);
    }
    return date;
  }

  parseBoolean(value) {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
      const lower = value.toLowerCase().trim();
      if (['true', 'yes', '1', 'y'].includes(lower)) return true;
      if (['false', 'no', '0', 'n'].includes(lower)) return false;
    }
    if (typeof value === 'number') {
      return value !== 0;
    }
    throw new Error(`Cannot convert "${value}" to boolean`);
  }

  // Fill missing values
  fillMissingValues(data) {
    if (!data.length) return data;

    const columns = Object.keys(data[0]);
    const fillValues = {};

    // Calculate fill values for each column
    columns.forEach(column => {
      const values = data.map(row => row[column])
        .filter(val => val !== null && val !== undefined && val !== '');

      if (values.length === 0) {
        fillValues[column] = null;
        return;
      }

      const sampleValue = values[0];
      
      if (typeof sampleValue === 'number') {
        // Use median for numeric values
        fillValues[column] = this.calculateMedian(values);
      } else if (sampleValue instanceof Date) {
        // Use most recent date
        fillValues[column] = new Date(Math.max(...values.map(d => d.getTime())));
      } else {
        // Use mode for categorical values
        fillValues[column] = this.calculateMode(values);
      }
    });

    // Apply fill values
    return data.map(row => {
      const filledRow = { ...row };
      
      Object.keys(fillValues).forEach(column => {
        if (row[column] === null || row[column] === undefined || row[column] === '') {
          filledRow[column] = fillValues[column];
        }
      });

      return filledRow;
    });
  }

  // Handle outliers using IQR method
  handleOutliers(data, method = 'cap') {
    if (!data.length) return data;

    const numericColumns = Object.keys(data[0]).filter(column => {
      const sample = data.slice(0, 10).map(row => row[column]);
      return sample.every(val => typeof val === 'number' && !isNaN(val));
    });

    const outlierBounds = {};

    // Calculate outlier bounds for numeric columns
    numericColumns.forEach(column => {
      const values = data.map(row => row[column]).filter(val => !isNaN(val));
      const q1 = this.calculateQuantile(values, 0.25);
      const q3 = this.calculateQuantile(values, 0.75);
      const iqr = q3 - q1;
      
      outlierBounds[column] = {
        lower: q1 - 1.5 * iqr,
        upper: q3 + 1.5 * iqr
      };
    });

    // Handle outliers based on method
    return data.map(row => {
      const processedRow = { ...row };

      numericColumns.forEach(column => {
        const value = row[column];
        const bounds = outlierBounds[column];

        if (!isNaN(value)) {
          if (value < bounds.lower || value > bounds.upper) {
            switch (method) {
              case 'cap':
                processedRow[column] = value < bounds.lower ? bounds.lower : bounds.upper;
                break;
              case 'remove':
                return null; // Mark for removal
              case 'flag':
                processedRow[`${column}_outlier`] = true;
                break;
            }
          }
        }
      });

      return processedRow;
    }).filter(row => row !== null);
  }

  // Statistical calculations
  calculateMedian(values) {
    const sorted = values.slice().sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 
      ? (sorted[mid - 1] + sorted[mid]) / 2 
      : sorted[mid];
  }

  calculateMode(values) {
    const frequency = {};
    values.forEach(val => {
      frequency[val] = (frequency[val] || 0) + 1;
    });
    
    return Object.keys(frequency).reduce((a, b) => 
      frequency[a] > frequency[b] ? a : b
    );
  }

  calculateQuantile(values, quantile) {
    const sorted = values.slice().sort((a, b) => a - b);
    const index = quantile * (sorted.length - 1);
    
    if (Number.isInteger(index)) {
      return sorted[index];
    } else {
      const lower = sorted[Math.floor(index)];
      const upper = sorted[Math.ceil(index)];
      return lower + (upper - lower) * (index - Math.floor(index));
    }
  }

  // Generate metadata about the parsing process
  generateMetadata(originalData, cleanedData, options) {
    return {
      original: {
        records: originalData.length,
        columns: originalData.length > 0 ? Object.keys(originalData[0]).length : 0
      },
      cleaned: {
        records: cleanedData.length,
        columns: cleanedData.length > 0 ? Object.keys(cleanedData[0]).length : 0
      },
      processing: {
        recordsRemoved: originalData.length - cleanedData.length,
        removalRate: originalData.length > 0 
          ? ((originalData.length - cleanedData.length) / originalData.length) * 100 
          : 0,
        options
      },
      timestamp: new Date().toISOString()
    };
  }

  // Generate basic statistics
  generateBasicStatistics(data) {
    if (!data.length) return {};

    const columns = Object.keys(data[0]);
    const statistics = {};

    columns.forEach(column => {
      const values = data.map(row => row[column]);
      const nonNullValues = values.filter(val => 
        val !== null && val !== undefined && val !== ''
      );

      statistics[column] = {
        totalCount: values.length,
        nonNullCount: nonNullValues.length,
        nullCount: values.length - nonNullValues.length,
        completeness: (nonNullValues.length / values.length) * 100,
        uniqueValues: new Set(nonNullValues).size
      };

      // Add numeric statistics if applicable
      const numericValues = nonNullValues.filter(val => typeof val === 'number');
      if (numericValues.length > 0) {
        statistics[column].numeric = {
          min: Math.min(...numericValues),
          max: Math.max(...numericValues),
          mean: numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length,
          median: this.calculateMedian(numericValues)
        };
      }
    });

    return statistics;
  }

  // Validate data structure
  validateDataStructure(data) {
    const issues = [];

    if (!Array.isArray(data)) {
      issues.push('Data is not an array');
      return { valid: false, issues };
    }

    if (data.length === 0) {
      issues.push('Data array is empty');
      return { valid: false, issues };
    }

    // Check if all records have the same structure
    const firstRowKeys = Object.keys(data[0]).sort();
    
    for (let i = 1; i < Math.min(data.length, 100); i++) {
      const currentRowKeys = Object.keys(data[i]).sort();
      
      if (JSON.stringify(firstRowKeys) !== JSON.stringify(currentRowKeys)) {
        issues.push(`Inconsistent structure at record ${i}`);
      }
    }

    // Check for duplicate records
    const uniqueRecords = new Set(data.map(row => JSON.stringify(row)));
    if (uniqueRecords.size !== data.length) {
      issues.push(`Found ${data.length - uniqueRecords.size} duplicate records`);
    }

    return {
      valid: issues.length === 0,
      issues,
      recordCount: data.length,
      columnCount: firstRowKeys.length,
      columns: firstRowKeys
    };
  }

  // Export processed data
  async exportData(data, format = 'json', filePath = null) {
    try {
      let content;
      let extension;

      switch (format.toLowerCase()) {
        case 'json':
          content = JSON.stringify(data, null, 2);
          extension = 'json';
          break;
        case 'csv':
          content = this.convertToCSV(data);
          extension = 'csv';
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      if (filePath) {
        const fullPath = filePath.endsWith(`.${extension}`) 
          ? filePath 
          : `${filePath}.${extension}`;
        
        await fs.writeFile(fullPath, content, 'utf8');
        return { success: true, filePath: fullPath };
      }

      return { success: true, content };
    } catch (error) {
      this.logger.error('Data export failed:', error);
      throw error;
    }
  }

  // Convert data to CSV format
  convertToCSV(data) {
    if (!data.length) return '';

    const headers = Object.keys(data[0]);
    const csvHeaders = headers.map(header => `"${header}"`).join(',');

    const csvRows = data.map(row => {
      return headers.map(header => {
        const value = row[header];
        if (value === null || value === undefined) return '';
        return `"${String(value).replace(/"/g, '""')}"`;
      }).join(',');
    });

    return [csvHeaders, ...csvRows].join('\n');
  }
}

module.exports = DataParser;
