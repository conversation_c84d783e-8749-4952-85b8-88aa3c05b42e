import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add request timestamp for debugging
    config.metadata = { startTime: new Date() };
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling and token refresh
api.interceptors.response.use(
  (response) => {
    // Add response time for debugging
    const endTime = new Date();
    const duration = endTime - response.config.metadata.startTime;
    response.duration = duration;
    
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      // Clear invalid token
      localStorage.removeItem('token');
      delete api.defaults.headers.common['Authorization'];
      
      // Redirect to login or emit auth error event
      window.dispatchEvent(new CustomEvent('auth:logout'));
      
      return Promise.reject(error);
    }
    
    // Handle 403 errors (forbidden)
    if (error.response?.status === 403) {
      window.dispatchEvent(new CustomEvent('auth:forbidden'));
    }
    
    // Handle network errors
    if (!error.response) {
      error.message = 'Network error. Please check your connection.';
    }
    
    // Handle timeout errors
    if (error.code === 'ECONNABORTED') {
      error.message = 'Request timeout. Please try again.';
    }
    
    return Promise.reject(error);
  }
);

// API service methods
const apiService = {
  // Set authentication token
  setAuthToken: (token) => {
    if (token) {
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      localStorage.setItem('token', token);
    } else {
      delete api.defaults.headers.common['Authorization'];
      localStorage.removeItem('token');
    }
  },

  // Clear authentication
  clearAuth: () => {
    delete api.defaults.headers.common['Authorization'];
    localStorage.removeItem('token');
  },

  // Generic HTTP methods
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
  delete: (url, config = {}) => api.delete(url, config),

  // Authentication APIs
  auth: {
    login: (credentials) => api.post('/auth/login', credentials),
    register: (userData) => api.post('/auth/register', userData),
    logout: () => api.post('/auth/logout'),
    refreshToken: () => api.post('/auth/refresh'),
    me: () => api.get('/auth/me'),
    forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
    resetPassword: (token, password) => api.post('/auth/reset-password', { token, password }),
    changePassword: (currentPassword, newPassword) => 
      api.post('/auth/change-password', { currentPassword, newPassword }),
  },

  // File upload APIs
  upload: {
    single: (file, options = {}) => {
      const formData = new FormData();
      formData.append('file', file);
      
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, typeof value === 'object' ? JSON.stringify(value) : value);
        }
      });

      return api.post('/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
        ...options.config
      });
    },

    batch: (files, options = {}) => {
      const formData = new FormData();
      files.forEach(file => formData.append('files', file));
      
      Object.entries(options).forEach(([key, value]) => {
        if (key !== 'config' && value !== undefined && value !== null) {
          formData.append(key, typeof value === 'object' ? JSON.stringify(value) : value);
        }
      });

      return api.post('/upload/batch', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
        ...options.config
      });
    },

    list: (filters = {}) => {
      const params = new URLSearchParams(filters);
      return api.get(`/upload?${params.toString()}`);
    },

    info: (fileId) => api.get(`/upload/${fileId}/info`),
    delete: (fileId) => api.delete(`/upload/${fileId}`),
    validate: (fileId) => api.post(`/upload/${fileId}/validate`),
    cleanup: () => api.post('/upload/cleanup'),
  },

  // Analysis APIs
  analysis: {
    start: (config) => api.post('/analysis/start', config),
    list: (filters = {}) => {
      const params = new URLSearchParams(filters);
      return api.get(`/analysis?${params.toString()}`);
    },
    status: (analysisId) => api.get(`/analysis/${analysisId}/status`),
    result: (analysisId) => api.get(`/analysis/${analysisId}/result`),
    insights: (analysisId) => api.get(`/analysis/${analysisId}/insights`),
    kpis: (analysisId) => api.get(`/analysis/${analysisId}/kpis`),
    recommendations: (analysisId) => api.get(`/analysis/${analysisId}/recommendations`),
    report: (analysisId, format = 'pdf', options = {}) => 
      api.post(`/analysis/${analysisId}/report`, { format, options }),
    rerun: (analysisId, options = {}) => api.post(`/analysis/${analysisId}/rerun`, options),
    delete: (analysisId) => api.delete(`/analysis/${analysisId}`),
  },

  // Agent APIs
  agents: {
    execute: (type, data, options = {}) => 
      api.post('/agents/execute', { type, data, ...options }),
    executeAsync: (type, data, options = {}) => 
      api.post('/agents/execute/async', { type, data, ...options }),
    status: (agentId) => api.get(`/agents/${agentId}/status`),
    result: (agentId) => api.get(`/agents/${agentId}/result`),
    list: (filters = {}) => {
      const params = new URLSearchParams(filters);
      return api.get(`/agents?${params.toString()}`);
    },
    cancel: (agentId) => api.post(`/agents/${agentId}/cancel`),
    delete: (agentId) => api.delete(`/agents/${agentId}`),
    types: () => api.get('/agents/types'),
  },

  // Utility methods
  utils: {
    // Health check
    health: () => api.get('/health'),

    // Test connectivity
    ping: () => api.get('/ping'),

    // Get API version
    version: () => api.get('/version'),

    // Download file helper
    download: async (url, filename) => {
      try {
        const response = await api.get(url, {
          responseType: 'blob',
        });

        const blob = new Blob([response.data]);
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);

        return true;
      } catch (error) {
        console.error('Download failed:', error);
        throw error;
      }
    },

    // Upload with progress tracking
    uploadWithProgress: (url, data, onProgress) => {
      return api.post(url, data, {
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress && onProgress(progress);
        }
      });
    },

    // Retry request with exponential backoff
    retryRequest: async (requestFn, maxRetries = 3, baseDelay = 1000) => {
      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          return await requestFn();
        } catch (error) {
          if (attempt === maxRetries) {
            throw error;
          }

          // Don't retry client errors (4xx)
          if (error.response?.status >= 400 && error.response?.status < 500) {
            throw error;
          }

          const delay = baseDelay * Math.pow(2, attempt);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    },

    // Batch requests with concurrency control
    batchRequests: async (requests, concurrency = 5) => {
      const results = [];
      const executing = [];

      for (const request of requests) {
        const promise = request().then(result => {
          executing.splice(executing.indexOf(promise), 1);
          return result;
        });

        results.push(promise);
        executing.push(promise);

        if (executing.length >= concurrency) {
          await Promise.race(executing);
        }
      }

      return Promise.allSettled(results);
    }
  }
};

// Auto-set token from localStorage on initialization
const savedToken = localStorage.getItem('token');
if (savedToken) {
  apiService.setAuthToken(savedToken);
}

// Listen for auth events
window.addEventListener('auth:logout', () => {
  apiService.clearAuth();
});

export default apiService;
